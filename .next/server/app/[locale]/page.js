(()=>{var a={};a.id=465,a.ids=[465],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2822:()=>{},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5934:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Contact-submit.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Contact-submit.js","default")},6042:(a,b,c)=>{"use strict";c.d(b,{Ij:()=>j,Vx:()=>g,dK:()=>i});var d=c(49863),e=c(33544);function f(a,b,c,d){return a.params.createElements&&Object.keys(d).forEach(f=>{if(!c[f]&&!0===c.auto){let g=(0,e.e)(a.el,`.${d[f]}`)[0];g||((g=(0,e.c)("div",d[f])).className=d[f],a.el.append(g)),c[f]=g,b[f]=g}}),c}function g(a){let{swiper:b,extendParams:c,on:d,emit:e}=a;c({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),b.navigation={nextEl:null,prevEl:null};let g=a=>(Array.isArray(a)?a:[a]).filter(a=>!!a);function h(a){let c;return a&&"string"==typeof a&&b.isElement&&(c=b.el.querySelector(a))?c:(a&&("string"==typeof a&&(c=[...document.querySelectorAll(a)]),b.params.uniqueNavElements&&"string"==typeof a&&c.length>1&&1===b.el.querySelectorAll(a).length&&(c=b.el.querySelector(a))),a&&!c)?a:c}function i(a,c){let d=b.params.navigation;(a=g(a)).forEach(a=>{a&&(a.classList[c?"add":"remove"](...d.disabledClass.split(" ")),"BUTTON"===a.tagName&&(a.disabled=c),b.params.watchOverflow&&b.enabled&&a.classList[b.isLocked?"add":"remove"](d.lockClass))})}function j(){let{nextEl:a,prevEl:c}=b.navigation;if(b.params.loop){i(c,!1),i(a,!1);return}i(c,b.isBeginning&&!b.params.rewind),i(a,b.isEnd&&!b.params.rewind)}function k(a){a.preventDefault(),(!b.isBeginning||b.params.loop||b.params.rewind)&&(b.slidePrev(),e("navigationPrev"))}function l(a){a.preventDefault(),(!b.isEnd||b.params.loop||b.params.rewind)&&(b.slideNext(),e("navigationNext"))}function m(){let a=b.params.navigation;if(b.params.navigation=f(b,b.originalParams.navigation,b.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(a.nextEl||a.prevEl))return;let c=h(a.nextEl),d=h(a.prevEl);Object.assign(b.navigation,{nextEl:c,prevEl:d}),c=g(c),d=g(d);let e=(c,d)=>{c&&c.addEventListener("click","next"===d?l:k),!b.enabled&&c&&c.classList.add(...a.lockClass.split(" "))};c.forEach(a=>e(a,"next")),d.forEach(a=>e(a,"prev"))}function n(){let{nextEl:a,prevEl:c}=b.navigation;a=g(a),c=g(c);let d=(a,c)=>{a.removeEventListener("click","next"===c?l:k),a.classList.remove(...b.params.navigation.disabledClass.split(" "))};a.forEach(a=>d(a,"next")),c.forEach(a=>d(a,"prev"))}d("init",()=>{!1===b.params.navigation.enabled?o():(m(),j())}),d("toEdge fromEdge lock unlock",()=>{j()}),d("destroy",()=>{n()}),d("enable disable",()=>{let{nextEl:a,prevEl:c}=b.navigation;if(a=g(a),c=g(c),b.enabled)return void j();[...a,...c].filter(a=>!!a).forEach(a=>a.classList.add(b.params.navigation.lockClass))}),d("click",(a,c)=>{let{nextEl:d,prevEl:f}=b.navigation;d=g(d),f=g(f);let h=c.target;if(b.params.navigation.hideOnClick&&!f.includes(h)&&!d.includes(h)){let a;if(b.pagination&&b.params.pagination&&b.params.pagination.clickable&&(b.pagination.el===h||b.pagination.el.contains(h)))return;d.length?a=d[0].classList.contains(b.params.navigation.hiddenClass):f.length&&(a=f[0].classList.contains(b.params.navigation.hiddenClass)),!0===a?e("navigationShow"):e("navigationHide"),[...d,...f].filter(a=>!!a).forEach(a=>a.classList.toggle(b.params.navigation.hiddenClass))}});let o=()=>{b.el.classList.add(...b.params.navigation.navigationDisabledClass.split(" ")),n()};Object.assign(b.navigation,{enable:()=>{b.el.classList.remove(...b.params.navigation.navigationDisabledClass.split(" ")),m(),j()},disable:o,update:j,init:m,destroy:n})}function h(a){return void 0===a&&(a=""),`.${a.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function i(a){let b,{swiper:c,extendParams:d,on:g,emit:i}=a,j="swiper-pagination";d({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:a=>a,formatFractionTotal:a=>a,bulletClass:`${j}-bullet`,bulletActiveClass:`${j}-bullet-active`,modifierClass:`${j}-`,currentClass:`${j}-current`,totalClass:`${j}-total`,hiddenClass:`${j}-hidden`,progressbarFillClass:`${j}-progressbar-fill`,progressbarOppositeClass:`${j}-progressbar-opposite`,clickableClass:`${j}-clickable`,lockClass:`${j}-lock`,horizontalClass:`${j}-horizontal`,verticalClass:`${j}-vertical`,paginationDisabledClass:`${j}-disabled`}}),c.pagination={el:null,bullets:[]};let k=0,l=a=>(Array.isArray(a)?a:[a]).filter(a=>!!a);function m(){return!c.params.pagination.el||!c.pagination.el||Array.isArray(c.pagination.el)&&0===c.pagination.el.length}function n(a,b){let{bulletActiveClass:d}=c.params.pagination;a&&(a=a[`${"prev"===b?"previous":"next"}ElementSibling`])&&(a.classList.add(`${d}-${b}`),(a=a[`${"prev"===b?"previous":"next"}ElementSibling`])&&a.classList.add(`${d}-${b}-${b}`))}function o(a){let b=a.target.closest(h(c.params.pagination.bulletClass));if(!b)return;a.preventDefault();let d=(0,e.g)(b)*c.params.slidesPerGroup;if(c.params.loop){if(c.realIndex===d)return;let a=c.realIndex,b=c.getSlideIndexByData(d),e=c.getSlideIndexByData(c.realIndex),f=d=>{let e=c.activeIndex;c.loopFix({direction:d,activeSlideIndex:b,slideTo:!1}),e===c.activeIndex&&c.slideToLoop(a,0,!1,!0)};b>c.slides.length-c.loopedSlides?f(b>e?"next":"prev"):c.params.centeredSlides&&b<Math.floor(("auto"===c.params.slidesPerView?c.slidesPerViewDynamic():Math.ceil(parseFloat(c.params.slidesPerView,10)))/2)&&f("prev"),c.slideToLoop(d)}else c.slideTo(d)}function p(){let a,d,f=c.rtl,g=c.params.pagination;if(m())return;let j=c.pagination.el;j=l(j);let o=c.virtual&&c.params.virtual.enabled?c.virtual.slides.length:c.slides.length,p=c.params.loop?Math.ceil(o/c.params.slidesPerGroup):c.snapGrid.length;if(c.params.loop?(d=c.previousRealIndex||0,a=c.params.slidesPerGroup>1?Math.floor(c.realIndex/c.params.slidesPerGroup):c.realIndex):void 0!==c.snapIndex?(a=c.snapIndex,d=c.previousSnapIndex):(d=c.previousIndex||0,a=c.activeIndex||0),"bullets"===g.type&&c.pagination.bullets&&c.pagination.bullets.length>0){let h,i,l,m=c.pagination.bullets;if(g.dynamicBullets&&(b=(0,e.f)(m[0],c.isHorizontal()?"width":"height",!0),j.forEach(a=>{a.style[c.isHorizontal()?"width":"height"]=`${b*(g.dynamicMainBullets+4)}px`}),g.dynamicMainBullets>1&&void 0!==d&&((k+=a-(d||0))>g.dynamicMainBullets-1?k=g.dynamicMainBullets-1:k<0&&(k=0)),l=((i=(h=Math.max(a-k,0))+(Math.min(m.length,g.dynamicMainBullets)-1))+h)/2),m.forEach(a=>{let b=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(a=>`${g.bulletActiveClass}${a}`)].map(a=>"string"==typeof a&&a.includes(" ")?a.split(" "):a).flat();a.classList.remove(...b)}),j.length>1)m.forEach(b=>{let d=(0,e.g)(b);d===a?b.classList.add(...g.bulletActiveClass.split(" ")):c.isElement&&b.setAttribute("part","bullet"),g.dynamicBullets&&(d>=h&&d<=i&&b.classList.add(...`${g.bulletActiveClass}-main`.split(" ")),d===h&&n(b,"prev"),d===i&&n(b,"next"))});else{let b=m[a];if(b&&b.classList.add(...g.bulletActiveClass.split(" ")),c.isElement&&m.forEach((b,c)=>{b.setAttribute("part",c===a?"bullet-active":"bullet")}),g.dynamicBullets){let a=m[h],b=m[i];for(let a=h;a<=i;a+=1)m[a]&&m[a].classList.add(...`${g.bulletActiveClass}-main`.split(" "));n(a,"prev"),n(b,"next")}}if(g.dynamicBullets){let a=Math.min(m.length,g.dynamicMainBullets+4),d=(b*a-b)/2-l*b,e=f?"right":"left";m.forEach(a=>{a.style[c.isHorizontal()?e:"top"]=`${d}px`})}}j.forEach((b,d)=>{if("fraction"===g.type&&(b.querySelectorAll(h(g.currentClass)).forEach(b=>{b.textContent=g.formatFractionCurrent(a+1)}),b.querySelectorAll(h(g.totalClass)).forEach(a=>{a.textContent=g.formatFractionTotal(p)})),"progressbar"===g.type){let d;d=g.progressbarOpposite?c.isHorizontal()?"vertical":"horizontal":c.isHorizontal()?"horizontal":"vertical";let e=(a+1)/p,f=1,i=1;"horizontal"===d?f=e:i=e,b.querySelectorAll(h(g.progressbarFillClass)).forEach(a=>{a.style.transform=`translate3d(0,0,0) scaleX(${f}) scaleY(${i})`,a.style.transitionDuration=`${c.params.speed}ms`})}"custom"===g.type&&g.renderCustom?(b.innerHTML=g.renderCustom(c,a+1,p),0===d&&i("paginationRender",b)):(0===d&&i("paginationRender",b),i("paginationUpdate",b)),c.params.watchOverflow&&c.enabled&&b.classList[c.isLocked?"add":"remove"](g.lockClass)})}function q(){let a=c.params.pagination;if(m())return;let b=c.virtual&&c.params.virtual.enabled?c.virtual.slides.length:c.slides.length,d=c.pagination.el;d=l(d);let e="";if("bullets"===a.type){let d=c.params.loop?Math.ceil(b/c.params.slidesPerGroup):c.snapGrid.length;c.params.freeMode&&c.params.freeMode.enabled&&d>b&&(d=b);for(let b=0;b<d;b+=1)a.renderBullet?e+=a.renderBullet.call(c,b,a.bulletClass):e+=`<${a.bulletElement} ${c.isElement?'part="bullet"':""} class="${a.bulletClass}"></${a.bulletElement}>`}"fraction"===a.type&&(e=a.renderFraction?a.renderFraction.call(c,a.currentClass,a.totalClass):`<span class="${a.currentClass}"></span> / <span class="${a.totalClass}"></span>`),"progressbar"===a.type&&(e=a.renderProgressbar?a.renderProgressbar.call(c,a.progressbarFillClass):`<span class="${a.progressbarFillClass}"></span>`),c.pagination.bullets=[],d.forEach(b=>{"custom"!==a.type&&(b.innerHTML=e||""),"bullets"===a.type&&c.pagination.bullets.push(...b.querySelectorAll(h(a.bulletClass)))}),"custom"!==a.type&&i("paginationRender",d[0])}function r(){let a;c.params.pagination=f(c,c.originalParams.pagination,c.params.pagination,{el:"swiper-pagination"});let b=c.params.pagination;b.el&&("string"==typeof b.el&&c.isElement&&(a=c.el.querySelector(b.el)),a||"string"!=typeof b.el||(a=[...document.querySelectorAll(b.el)]),a||(a=b.el),a&&0!==a.length&&(c.params.uniqueNavElements&&"string"==typeof b.el&&Array.isArray(a)&&a.length>1&&(a=[...c.el.querySelectorAll(b.el)]).length>1&&(a=a.filter(a=>(0,e.a)(a,".swiper")[0]===c.el)[0]),Array.isArray(a)&&1===a.length&&(a=a[0]),Object.assign(c.pagination,{el:a}),(a=l(a)).forEach(a=>{"bullets"===b.type&&b.clickable&&a.classList.add(...(b.clickableClass||"").split(" ")),a.classList.add(b.modifierClass+b.type),a.classList.add(c.isHorizontal()?b.horizontalClass:b.verticalClass),"bullets"===b.type&&b.dynamicBullets&&(a.classList.add(`${b.modifierClass}${b.type}-dynamic`),k=0,b.dynamicMainBullets<1&&(b.dynamicMainBullets=1)),"progressbar"===b.type&&b.progressbarOpposite&&a.classList.add(b.progressbarOppositeClass),b.clickable&&a.addEventListener("click",o),c.enabled||a.classList.add(b.lockClass)})))}function s(){let a=c.params.pagination;if(m())return;let b=c.pagination.el;b&&(b=l(b)).forEach(b=>{b.classList.remove(a.hiddenClass),b.classList.remove(a.modifierClass+a.type),b.classList.remove(c.isHorizontal()?a.horizontalClass:a.verticalClass),a.clickable&&(b.classList.remove(...(a.clickableClass||"").split(" ")),b.removeEventListener("click",o))}),c.pagination.bullets&&c.pagination.bullets.forEach(b=>b.classList.remove(...a.bulletActiveClass.split(" ")))}g("changeDirection",()=>{if(!c.pagination||!c.pagination.el)return;let a=c.params.pagination,{el:b}=c.pagination;(b=l(b)).forEach(b=>{b.classList.remove(a.horizontalClass,a.verticalClass),b.classList.add(c.isHorizontal()?a.horizontalClass:a.verticalClass)})}),g("init",()=>{!1===c.params.pagination.enabled?t():(r(),q(),p())}),g("activeIndexChange",()=>{void 0===c.snapIndex&&p()}),g("snapIndexChange",()=>{p()}),g("snapGridLengthChange",()=>{q(),p()}),g("destroy",()=>{s()}),g("enable disable",()=>{let{el:a}=c.pagination;a&&(a=l(a)).forEach(a=>a.classList[c.enabled?"remove":"add"](c.params.pagination.lockClass))}),g("lock unlock",()=>{p()}),g("click",(a,b)=>{let d=b.target,e=l(c.pagination.el);if(c.params.pagination.el&&c.params.pagination.hideOnClick&&e&&e.length>0&&!d.classList.contains(c.params.pagination.bulletClass)){if(c.navigation&&(c.navigation.nextEl&&d===c.navigation.nextEl||c.navigation.prevEl&&d===c.navigation.prevEl))return;!0===e[0].classList.contains(c.params.pagination.hiddenClass)?i("paginationShow"):i("paginationHide"),e.forEach(a=>a.classList.toggle(c.params.pagination.hiddenClass))}});let t=()=>{c.el.classList.add(c.params.pagination.paginationDisabledClass);let{el:a}=c.pagination;a&&(a=l(a)).forEach(a=>a.classList.add(c.params.pagination.paginationDisabledClass)),s()};Object.assign(c.pagination,{enable:()=>{c.el.classList.remove(c.params.pagination.paginationDisabledClass);let{el:a}=c.pagination;a&&(a=l(a)).forEach(a=>a.classList.remove(c.params.pagination.paginationDisabledClass)),r(),q(),p()},disable:t,render:q,update:p,init:r,destroy:s})}function j(a){let b,c,e,f,g,h,i,j,k,{swiper:l,extendParams:m,on:n,emit:o,params:p}=a;l.autoplay={running:!1,paused:!1,timeLeft:0},m({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let q=p&&p.autoplay?p.autoplay.delay:3e3,r=p&&p.autoplay?p.autoplay.delay:3e3,s=new Date().getTime;function t(a){l&&!l.destroyed&&l.wrapperEl&&a.target===l.wrapperEl&&(l.wrapperEl.removeEventListener("transitionend",t),z())}let u=()=>{if(l.destroyed||!l.autoplay.running)return;l.autoplay.paused?f=!0:f&&(r=e,f=!1);let a=l.autoplay.paused?e:s+r-new Date().getTime();l.autoplay.timeLeft=a,o("autoplayTimeLeft",a,a/q),c=requestAnimationFrame(()=>{u()})},v=a=>{if(l.destroyed||!l.autoplay.running)return;cancelAnimationFrame(c),u();let d=void 0===a?l.params.autoplay.delay:a;q=l.params.autoplay.delay,r=l.params.autoplay.delay;let f=(()=>{let a;if(a=l.virtual&&l.params.virtual.enabled?l.slides.filter(a=>a.classList.contains("swiper-slide-active"))[0]:l.slides[l.activeIndex])return parseInt(a.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(f)&&f>0&&void 0===a&&(d=f,q=f,r=f),e=d;let g=l.params.speed,h=()=>{l&&!l.destroyed&&(l.params.autoplay.reverseDirection?!l.isBeginning||l.params.loop||l.params.rewind?(l.slidePrev(g,!0,!0),o("autoplay")):l.params.autoplay.stopOnLastSlide||(l.slideTo(l.slides.length-1,g,!0,!0),o("autoplay")):!l.isEnd||l.params.loop||l.params.rewind?(l.slideNext(g,!0,!0),o("autoplay")):l.params.autoplay.stopOnLastSlide||(l.slideTo(0,g,!0,!0),o("autoplay")),l.params.cssMode&&(s=new Date().getTime(),requestAnimationFrame(()=>{v()})))};return d>0?(clearTimeout(b),b=setTimeout(()=>{h()},d)):requestAnimationFrame(()=>{h()}),d},w=()=>{l.autoplay.running=!0,v(),o("autoplayStart")},x=()=>{l.autoplay.running=!1,clearTimeout(b),cancelAnimationFrame(c),o("autoplayStop")},y=(a,c)=>{if(l.destroyed||!l.autoplay.running)return;clearTimeout(b),a||(k=!0);let d=()=>{o("autoplayPause"),l.params.autoplay.waitForTransition?l.wrapperEl.addEventListener("transitionend",t):z()};if(l.autoplay.paused=!0,c){j&&(e=l.params.autoplay.delay),j=!1,d();return}e=(e||l.params.autoplay.delay)-(new Date().getTime()-s),l.isEnd&&e<0&&!l.params.loop||(e<0&&(e=0),d())},z=()=>{l.isEnd&&e<0&&!l.params.loop||l.destroyed||!l.autoplay.running||(s=new Date().getTime(),k?(k=!1,v(e)):v(),l.autoplay.paused=!1,o("autoplayResume"))},A=()=>{if(l.destroyed||!l.autoplay.running)return;let a=(0,d.g)();"hidden"===a.visibilityState&&(k=!0,y(!0)),"visible"===a.visibilityState&&z()},B=a=>{"mouse"===a.pointerType&&(k=!0,l.animating||l.autoplay.paused||y(!0))},C=a=>{"mouse"===a.pointerType&&l.autoplay.paused&&z()};n("init",()=>{l.params.autoplay.enabled&&(l.params.autoplay.pauseOnMouseEnter&&(l.el.addEventListener("pointerenter",B),l.el.addEventListener("pointerleave",C)),(0,d.g)().addEventListener("visibilitychange",A),s=new Date().getTime(),w())}),n("destroy",()=>{l.el.removeEventListener("pointerenter",B),l.el.removeEventListener("pointerleave",C),(0,d.g)().removeEventListener("visibilitychange",A),l.autoplay.running&&x()}),n("beforeTransitionStart",(a,b,c)=>{!l.destroyed&&l.autoplay.running&&(c||!l.params.autoplay.disableOnInteraction?y(!0,!0):x())}),n("sliderFirstMove",()=>{if(!l.destroyed&&l.autoplay.running){if(l.params.autoplay.disableOnInteraction)return void x();g=!0,h=!1,k=!1,i=setTimeout(()=>{k=!0,h=!0,y(!0)},200)}}),n("touchEnd",()=>{if(!l.destroyed&&l.autoplay.running&&g){if(clearTimeout(i),clearTimeout(b),l.params.autoplay.disableOnInteraction){h=!1,g=!1;return}h&&l.params.cssMode&&z(),h=!1,g=!1}}),n("slideChange",()=>{!l.destroyed&&l.autoplay.running&&(j=!0)}),Object.assign(l.autoplay,{start:w,stop:x,pause:y,resume:z})}},10196:(a,b,c)=>{"use strict";c.d(b,{default:()=>ar});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c.n(g);function i(){return(i=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}let j={origin:[0,0,0],round:4},k={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function l(a){let b=a.pathValue[a.segmentStart],c=b.toLowerCase(),{data:d}=a;for(;d.length>=k[c]&&("m"===c&&d.length>2?(a.segments.push([b,...d.splice(0,2)]),c="l",b="m"===b?"l":"L"):a.segments.push([b,...d.splice(0,k[c])]),k[c]););}let m="SVGPathCommander error";function n(a){return a>=48&&a<=57}let o="Invalid path value";function p(a){var b;let{pathValue:c,max:d}=a;for(;a.index<d&&(10===(b=c.charCodeAt(a.index))||13===b||8232===b||8233===b||32===b||9===b||11===b||12===b||160===b||b>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(b));)a.index+=1}function q(a){let{max:b,pathValue:c,index:d}=a,e=c.charCodeAt(d),f=k[c[d].toLowerCase()];if(a.segmentStart=d,function(a){switch(32|a){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}(e))if(a.index+=1,p(a),a.data=[],f){for(;;){var g;for(let d=f;d>0;d-=1){if(97!=(32|e)||3!==d&&4!==d?function(a){let{max:b,pathValue:c,index:d}=a,e,f=d,g=!1,h=!1,i=!1,j=!1;if(f>=b)a.err=`${m}: ${o} at index ${f}, "pathValue" is missing param`;else if(43!==(e=c.charCodeAt(f))&&45!==e||(f+=1,e=c.charCodeAt(f)),n(e)||46===e){if(46!==e){if(g=48===e,f+=1,e=c.charCodeAt(f),g&&f<b&&e&&n(e))return a.err=`${m}: ${o} at index ${d}, "${c[d]}" illegal number`;for(;f<b&&n(c.charCodeAt(f));)f+=1,h=!0;e=c.charCodeAt(f)}if(46===e){for(j=!0,f+=1;n(c.charCodeAt(f));)f+=1,i=!0;e=c.charCodeAt(f)}if(101===e||69===e){if(j&&!h&&!i)return a.err=`${m}: ${o} at index ${f}, "${c[f]}" invalid float exponent`;if(f+=1,43!==(e=c.charCodeAt(f))&&45!==e||(f+=1),!(f<b&&n(c.charCodeAt(f))))return a.err=`${m}: ${o} at index ${f}, "${c[f]}" invalid integer exponent`;for(;f<b&&n(c.charCodeAt(f));)f+=1}a.index=f,a.param=+a.pathValue.slice(d,f)}else a.err=`${m}: ${o} at index ${f}, "${c[f]}" is not a number`}(a):function(a){let{index:b,pathValue:c}=a,d=c.charCodeAt(b);48===d?(a.param=0,a.index+=1):49===d?(a.param=1,a.index+=1):a.err=`${m}: invalid Arc flag "${c[b]}", expecting 0 or 1 at index ${b}`}(a),a.err.length)return;a.data.push(a.param),p(a),a.index<b&&44===c.charCodeAt(a.index)&&(a.index+=1,p(a))}if(a.index>=a.max||!((g=c.charCodeAt(a.index))>=48&&g<=57||43===g||45===g||46===g))break}l(a)}else l(a);else a.err=`${m}: ${o} "${c[d]}" is not a path command`}function r(a){return a.map(a=>Array.isArray(a)?[...a]:a)}function s(a){this.segments=[],this.pathValue=a,this.max=a.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""}function t(a){return Array.isArray(a)&&a.every(a=>{let b=a[0].toLowerCase();return k[b]===a.length-1&&"achlmqstvz".includes(b)})}function u(a){if(t(a))return r(a);let b=new s(a);for(p(b);b.index<b.max&&!b.err.length;)q(b);return b.err?b.err:b.segments}function v(a){return t(a)&&a.every(([a])=>a===a.toUpperCase())}function w(a){if(v(a))return r(a);let b=u(a),c=0,d=0,e=0,f=0;return b.map(a=>{let b=a.slice(1).map(Number),[g]=a,h=g.toUpperCase();if("M"===g)return[c,d]=b,e=c,f=d,["M",c,d];let i=[];if(g!==h)switch(h){case"A":i=[h,b[0],b[1],b[2],b[3],b[4],b[5]+c,b[6]+d];break;case"V":i=[h,b[0]+d];break;case"H":i=[h,b[0]+c];break;default:i=[h,...b.map((a,b)=>a+(b%2?d:c))]}else i=[h,...b];let j=i.length;switch(h){case"Z":c=e,d=f;break;case"H":[,c]=i;break;case"V":[,d]=i;break;default:c=i[j-2],d=i[j-1],"M"===h&&(e=c,f=d)}return i})}function x(a){return t(a)&&a.slice(1).every(([a])=>a===a.toLowerCase())}function y(a){if(x(a))return r(a);let b=u(a),c=0,d=0,e=0,f=0;return b.map(a=>{let b=a.slice(1).map(Number),[g]=a,h=g.toLowerCase();if("M"===g)return[c,d]=b,e=c,f=d,["M",c,d];let i=[];if(g!==h)switch(h){case"a":i=[h,b[0],b[1],b[2],b[3],b[4],b[5]-c,b[6]-d];break;case"v":i=[h,b[0]-d];break;case"h":i=[h,b[0]-c];break;default:i=[h,...b.map((a,b)=>a-(b%2?d:c))]}else"m"===g&&(e=b[0]+c,f=b[1]+d),i=[h,...b];let j=i.length;switch(h){case"z":c=e,d=f;break;case"h":c+=i[1];break;case"v":d+=i[1];break;default:c+=i[j-2],d+=i[j-1]}return i})}function z(a,b,c){if(a[c].length>7){a[c].shift();let d=a[c],e=c;for(;d.length;)b[c]="A",a.splice(e+=1,0,["C",...d.splice(0,6)]);a.splice(c,1)}}function A(a){return v(a)&&a.every(([a])=>"ACLMQZ".includes(a))}function B(a){return A(a)&&a.every(([a])=>"MC".includes(a))}let C={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function D(a){if(A(a))return r(a);let b=w(a),c={...C},d=b.length;for(let a=0;a<d;a+=1){b[a],b[a]=function(a,b){let[c]=a,{x1:d,y1:e,x2:f,y2:g}=b,h=a.slice(1).map(Number),i=a;if("TQ".includes(c)||(b.qx=null,b.qy=null),"H"===c)i=["L",a[1],e];else if("V"===c)i=["L",d,a[1]];else if("S"===c){let a=2*d-f,c=2*e-g;b.x1=a,b.y1=c,i=["C",a,c,...h]}else if("T"===c){let a=2*d-b.qx,c=2*e-b.qy;b.qx=a,b.qy=c,i=["Q",a,c,...h]}else if("Q"===c){let[a,c]=h;b.qx=a,b.qy=c}return i}(b[a],c);let d=b[a],e=d.length;c.x1=+d[e-2],c.y1=+d[e-1],c.x2=+d[e-4]||c.x1,c.y2=+d[e-3]||c.y1}return b}function E(a,b,c){return{x:a*Math.cos(c)-b*Math.sin(c),y:a*Math.sin(c)+b*Math.cos(c)}}function F(a,b,c){let[d,e]=a,[f,g]=b;return[d+(f-d)*c,e+(g-e)*c]}function G(a,b){return Math.sqrt((a[0]-b[0])*(a[0]-b[0])+(a[1]-b[1])*(a[1]-b[1]))}function H(a,b,c,d,e){let f=G([a,b],[c,d]),g={x:0,y:0};if("number"==typeof e)if(e<=0)g={x:a,y:b};else if(e>=f)g={x:c,y:d};else{let[h,i]=F([a,b],[c,d],e/f);g={x:h,y:i}}return{length:f,point:g,min:{x:Math.min(a,c),y:Math.min(b,d)},max:{x:Math.max(a,c),y:Math.max(b,d)}}}function I(a,b,c,d){let e=[a,b],f=[c,d],g=F(e,f,.5),h=F(f,g,.5),i=F(g,h,.5),j=F(h,i,.5),k=F(i,j,.5),l=H(...e,...g,...i,...k,.5).point,m=H(...k,...j,...h,...f,0).point;return[l.x,l.y,m.x,m.y,c,d]}function J(a,b){let[c]=a,d=a.slice(1).map(Number),[e,f]=d,{x1:g,y1:h,x:i,y:j}=b;switch("TQ".includes(c)||(b.qx=null,b.qy=null),c){case"M":return b.x=e,b.y=f,a;case"A":return["C",...function a(b,c,d,e,f,g,h,i,j,k){let l=b,m=c,n=d,o=e,p=i,q=j,r=120*Math.PI/180,s=Math.PI/180*(+f||0),t,u,v,w,x,y=[];if(k)[u,v,w,x]=k;else{l=(t=E(l,m,-s)).x,m=t.y,p=(t=E(p,q,-s)).x,q=t.y;let a=(l-p)/2,b=(m-q)/2,c=a*a/(n*n)+b*b/(o*o);c>1&&(n*=c=Math.sqrt(c),o*=c);let d=n*n,e=o*o,f=(g===h?-1:1)*Math.sqrt(Math.abs((d*e-d*b*b-e*a*a)/(d*b*b+e*a*a)));w=f*n*b/o+(l+p)/2,x=-(f*o)*a/n+(m+q)/2,u=Math.asin(((m-x)/o*1e9|0)/1e9),v=Math.asin(((q-x)/o*1e9|0)/1e9),u=l<w?Math.PI-u:u,v=p<w?Math.PI-v:v,u<0&&(u=2*Math.PI+u),v<0&&(v=2*Math.PI+v),h&&u>v&&(u-=2*Math.PI),!h&&v>u&&(v-=2*Math.PI)}let z=v-u;if(Math.abs(z)>r){let b=v,c=p,d=q;y=a(p=w+n*Math.cos(v=u+r*(h&&v>u?1:-1)),q=x+o*Math.sin(v),n,o,f,0,h,c,d,[v,b,w,x])}z=v-u;let A=Math.cos(u),B=Math.cos(v),C=Math.tan(z/4),D=4/3*n*C,F=4/3*o*C,G=[l,m],H=[l+D*Math.sin(u),m-F*A],I=[p+D*Math.sin(v),q-F*B],J=[p,q];if(H[0]=2*G[0]-H[0],H[1]=2*G[1]-H[1],k)return[...H,...I,...J,...y];y=[...H,...I,...J,...y];let K=[];for(let a=0,b=y.length;a<b;a+=1)K[a]=a%2?E(y[a-1],y[a],s).y:E(y[a],y[a+1],s).x;return K}(g,h,...d)];case"Q":return b.qx=e,b.qy=f,["C",...function(a,b,c,d,e,f){return[1/3*a+2/3*c,1/3*b+2/3*d,1/3*e+2/3*c,1/3*f+2/3*d,e,f]}(g,h,...d)];case"L":return["C",...I(g,h,e,f)];case"Z":return["C",...I(g,h,i,j)]}return a}function K(a){if(B(a))return r(a);let b=D(a),c={...C},d=[],e="",f=b.length;for(let a=0;a<f;a+=1){[e]=b[a],d[a]=e,b[a]=J(b[a],c),z(b,d,a),f=b.length;let g=b[a],h=g.length;c.x1=+g[h-2],c.y1=+g[h-1],c.x2=+g[h-4]||c.x1,c.y2=+g[h-3]||c.y1}return b}function L(a,b){let{round:c}=j;if("off"===b||"off"===c)return r(a);let d="number"==typeof(c=b>=0?b:c)&&c>=1?10**c:1;return a.map(a=>{let b=a.slice(1).map(Number).map(a=>c?Math.round(a*d)/d:Math.round(a));return[a[0],...b]})}function M(a,b){return L(a,b).map(a=>a[0]+a.slice(1).join(" ")).join("")}function N(a){let b=w(a),c="Z"===b.slice(-1)[0][0],d=D(b).map((a,c)=>{let[d,e]=a.slice(-2).map(Number);return{seg:b[c],n:a,c:b[c][0],x:d,y:e}}).map((a,b,d)=>{let e=a.seg,f=a.n,g=b&&d[b-1],h=d[b+1],i=a.c,j=d.length,k=b?d[b-1].x:d[j-1].x,l=b?d[b-1].y:d[j-1].y,m=[];switch(i){case"M":m=c?["Z"]:[i,k,l];break;case"A":m=[i,...e.slice(1,-3),+(1!==e[5]),k,l];break;case"C":m=h&&"S"===h.c?["S",e[1],e[2],k,l]:[i,e[3],e[4],e[1],e[2],k,l];break;case"S":m=g&&"CS".includes(g.c)&&(!h||"S"!==h.c)?["C",f[3],f[4],f[1],f[2],k,l]:[i,f[1],f[2],k,l];break;case"Q":m=h&&"T"===h.c?["T",k,l]:[i,...e.slice(1,-2),k,l];break;case"T":m=g&&"QT".includes(g.c)&&(!h||"T"!==h.c)?["Q",f[1],f[2],k,l]:[i,k,l];break;case"Z":m=["M",k,l];break;case"H":m=[i,k];break;case"V":m=[i,l];break;default:m=[i,...e.slice(1,-2),k,l]}return m});return c?d.reverse():[d[0],...d.slice(1).reverse()]}function O(a){let b=[],c,d=-1;return a.forEach(a=>{"M"===a[0]?(c=[a],d+=1):c=[...c,a],b[d]=c}),b}function P(a,b){let c=w(a),d=D(c),e={...C},f=[],g=c.length,h="",i="",j=0,k=0,l=0,m=0;for(let a=0;a<g;a+=1){[h]=c[a],f[a]=h,a&&(i=f[a-1]),c[a]=function(a,b,c,d){let[e]=a,f=a=>Math.round(1e4*a)/1e4,g=a.slice(1).map(a=>+a),h=b.slice(1).map(a=>+a),{x1:i,y1:j,x2:k,y2:l,x:m,y:n}=c,o=a,[p,q]=h.slice(-2);if("TQ".includes(e)||(c.qx=null,c.qy=null),["V","H","S","T","Z"].includes(e))o=[e,...g];else if("L"===e)f(m)===f(p)?o=["V",q]:f(n)===f(q)&&(o=["H",p]);else if("C"===e){let[a,b]=h;"CS".includes(d)&&(f(a)===f(2*i-k)&&f(b)===f(2*j-l)||f(i)===f(2*k-m)&&f(j)===f(2*l-n))&&(o=["S",...h.slice(-4)]),c.x1=a,c.y1=b}else if("Q"===e){let[a,b]=h;c.qx=a,c.qy=b,"QT".includes(d)&&(f(a)===f(2*i-k)&&f(b)===f(2*j-l)||f(i)===f(2*k-m)&&f(j)===f(2*l-n))&&(o=["T",...h.slice(-2)])}return o}(c[a],d[a],e,i);let b=c[a],g=b.length;switch(e.x1=+b[g-2],e.y1=+b[g-1],e.x2=+b[g-4]||e.x1,e.y2=+b[g-3]||e.y1,h){case"Z":j=l,k=m;break;case"H":[,j]=b;break;case"V":[,k]=b;break;default:[j,k]=b.slice(-2).map(Number),"M"===h&&(l=j,m=k)}e.x=j,e.y=k}let n=L(c,b),o=L(y(c),b);return n.map((a,b)=>b?a.join("").length<o[b].join("").length?a:o[b]:a)}function Q(a){let b=new aa,c=Array.from(a);if(!c.every(a=>!Number.isNaN(a)))throw TypeError(`CSSMatrix: "${a}" must only have numbers.`);if(16===c.length){let[a,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r]=c;b.m11=a,b.a=a,b.m21=g,b.c=g,b.m31=k,b.m41=o,b.e=o,b.m12=d,b.b=d,b.m22=h,b.d=h,b.m32=l,b.m42=p,b.f=p,b.m13=e,b.m23=i,b.m33=m,b.m43=q,b.m14=f,b.m24=j,b.m34=n,b.m44=r}else{if(6!==c.length)throw TypeError("CSSMatrix: expecting an Array of 6/16 values.");{let[a,d,e,f,g,h]=c;b.m11=a,b.a=a,b.m12=d,b.b=d,b.m21=e,b.c=e,b.m22=f,b.d=f,b.m41=g,b.e=g,b.m42=h,b.f=h}}return b}function R(a){let b=Object.keys(new aa);if("object"==typeof a&&b.every(b=>b in a))return Q([a.m11,a.m12,a.m13,a.m14,a.m21,a.m22,a.m23,a.m24,a.m31,a.m32,a.m33,a.m34,a.m41,a.m42,a.m43,a.m44]);throw TypeError(`CSSMatrix: "${JSON.stringify(a)}" is not a DOMMatrix / CSSMatrix / JSON compatible object.`)}function S(a){if("string"!=typeof a)throw TypeError(`CSSMatrix: "${a}" is not a string.`);let b=String(a).replace(/\s/g,""),c=new aa,d=`CSSMatrix: invalid transform string "${a}"`;return b.split(")").filter(a=>a).forEach(a=>{let[b,e]=a.split("(");if(!e)throw TypeError(d);let f=e.split(",").map(a=>a.includes("rad")?parseFloat(a)*(180/Math.PI):parseFloat(a)),[g,h,i,j]=f,k=[g,h,i],l=[g,h,i,j];if("perspective"===b&&g&&[h,i].every(a=>void 0===a))c.m34=-1/g;else if(b.includes("matrix")&&[6,16].includes(f.length)&&f.every(a=>!Number.isNaN(+a))){let a=f.map(a=>1e-6>Math.abs(a)?0:a);c=c.multiply(Q(a))}else if("translate3d"===b&&k.every(a=>!Number.isNaN(+a)))c=c.translate(g,h,i);else if("translate"===b&&g&&void 0===i)c=c.translate(g,h||0,0);else if("rotate3d"===b&&l.every(a=>!Number.isNaN(+a))&&j)c=c.rotateAxisAngle(g,h,i,j);else if("rotate"===b&&g&&[h,i].every(a=>void 0===a))c=c.rotate(0,0,g);else if("scale3d"===b&&k.every(a=>!Number.isNaN(+a))&&k.some(a=>1!==a))c=c.scale(g,h,i);else if("scale"!==b||Number.isNaN(g)||1===g||void 0!==i)if("skew"===b&&(g||!Number.isNaN(g)&&h)&&void 0===i)c=c.skew(g,h||0);else{if(!(/[XYZ]/.test(b)&&g&&[h,i].every(a=>void 0===a)&&["translate","rotate","scale","skew"].some(a=>b.includes(a))))throw TypeError(d);if(["skewX","skewY"].includes(b))c=c[b](g);else{let a=b.replace(/[XYZ]/,""),d=["X","Y","Z"].indexOf(b.replace(a,"")),e=+("scale"===a),f=[0===d?g:e,1===d?g:e,2===d?g:e];c=c[a](...f)}}else{let a=Number.isNaN(+h)?g:h;c=c.scale(g,a,1)}}),c}function T(a,b){return b?[a.a,a.b,a.c,a.d,a.e,a.f]:[a.m11,a.m12,a.m13,a.m14,a.m21,a.m22,a.m23,a.m24,a.m31,a.m32,a.m33,a.m34,a.m41,a.m42,a.m43,a.m44]}function U(a,b,c){let d=new aa;return d.m41=a,d.e=a,d.m42=b,d.f=b,d.m43=c,d}function V(a,b,c){let d=new aa,e=Math.PI/180,f=a*e,g=b*e,h=c*e,i=Math.cos(f),j=-Math.sin(f),k=Math.cos(g),l=-Math.sin(g),m=Math.cos(h),n=-Math.sin(h),o=k*m,p=-k*n;d.m11=o,d.a=o,d.m12=p,d.b=p,d.m13=l;let q=j*l*m+i*n;d.m21=q,d.c=q;let r=i*m-j*l*n;return d.m22=r,d.d=r,d.m23=-j*k,d.m31=j*n-i*l*m,d.m32=j*m+i*l*n,d.m33=i*k,d}function W(a,b,c,d){let e=new aa,f=Math.sqrt(a*a+b*b+c*c);if(0===f)return e;let g=a/f,h=b/f,i=c/f,j=Math.PI/360*d,k=Math.sin(j),l=Math.cos(j),m=k*k,n=g*g,o=h*h,p=i*i,q=1-2*(o+p)*m;e.m11=q,e.a=q;let r=2*(g*h*m+i*k*l);e.m12=r,e.b=r,e.m13=2*(g*i*m-h*k*l);let s=2*(h*g*m-i*k*l);e.m21=s,e.c=s;let t=1-2*(p+n)*m;return e.m22=t,e.d=t,e.m23=2*(h*i*m+g*k*l),e.m31=2*(i*g*m+h*k*l),e.m32=2*(i*h*m-g*k*l),e.m33=1-2*(n+o)*m,e}function X(a,b,c){let d=new aa;return d.m11=a,d.a=a,d.m22=b,d.d=b,d.m33=c,d}function Y(a,b){let c=new aa;if(a){let b=Math.tan(a*Math.PI/180);c.m21=b,c.c=b}if(b){let a=Math.tan(b*Math.PI/180);c.m12=a,c.b=a}return c}function Z(a){return Y(a,0)}function $(a){return Y(0,a)}function _(a,b){return Q([b.m11*a.m11+b.m12*a.m21+b.m13*a.m31+b.m14*a.m41,b.m11*a.m12+b.m12*a.m22+b.m13*a.m32+b.m14*a.m42,b.m11*a.m13+b.m12*a.m23+b.m13*a.m33+b.m14*a.m43,b.m11*a.m14+b.m12*a.m24+b.m13*a.m34+b.m14*a.m44,b.m21*a.m11+b.m22*a.m21+b.m23*a.m31+b.m24*a.m41,b.m21*a.m12+b.m22*a.m22+b.m23*a.m32+b.m24*a.m42,b.m21*a.m13+b.m22*a.m23+b.m23*a.m33+b.m24*a.m43,b.m21*a.m14+b.m22*a.m24+b.m23*a.m34+b.m24*a.m44,b.m31*a.m11+b.m32*a.m21+b.m33*a.m31+b.m34*a.m41,b.m31*a.m12+b.m32*a.m22+b.m33*a.m32+b.m34*a.m42,b.m31*a.m13+b.m32*a.m23+b.m33*a.m33+b.m34*a.m43,b.m31*a.m14+b.m32*a.m24+b.m33*a.m34+b.m34*a.m44,b.m41*a.m11+b.m42*a.m21+b.m43*a.m31+b.m44*a.m41,b.m41*a.m12+b.m42*a.m22+b.m43*a.m32+b.m44*a.m42,b.m41*a.m13+b.m42*a.m23+b.m43*a.m33+b.m44*a.m43,b.m41*a.m14+b.m42*a.m24+b.m43*a.m34+b.m44*a.m44])}class aa{constructor(...a){if(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0,this.m11=1,this.m12=0,this.m13=0,this.m14=0,this.m21=0,this.m22=1,this.m23=0,this.m24=0,this.m31=0,this.m32=0,this.m33=1,this.m34=0,this.m41=0,this.m42=0,this.m43=0,this.m44=1,a.length){let b=[16,6].some(b=>b===a.length)?a:a[0];return this.setMatrixValue(b)}return this}get isIdentity(){return 1===this.m11&&0===this.m12&&0===this.m13&&0===this.m14&&0===this.m21&&1===this.m22&&0===this.m23&&0===this.m24&&0===this.m31&&0===this.m32&&1===this.m33&&0===this.m34&&0===this.m41&&0===this.m42&&0===this.m43&&1===this.m44}get is2D(){return 0===this.m31&&0===this.m32&&1===this.m33&&0===this.m34&&0===this.m43&&1===this.m44}setMatrixValue(a){return"string"==typeof a&&a.length&&"none"!==a?S(a):[Array,Float64Array,Float32Array].some(b=>a instanceof b)?Q(a):[aa,DOMMatrix,Object].some(b=>a instanceof b)?R(a):this}toFloat32Array(a){return Float32Array.from(T(this,a))}toFloat64Array(a){return Float64Array.from(T(this,a))}toString(){let{is2D:a}=this;return`${a?"matrix":"matrix3d"}(${this.toFloat64Array(a).join(", ")})`}toJSON(){let{is2D:a,isIdentity:b}=this;return{...this,is2D:a,isIdentity:b}}multiply(a){return _(this,a)}translate(a,b,c){let d=b,e=c;return void 0===d&&(d=0),void 0===e&&(e=0),_(this,U(a,d,e))}scale(a,b,c){let d=b,e=c;return void 0===d&&(d=a),void 0===e&&(e=1),_(this,X(a,d,e))}rotate(a,b,c){let d=a,e=b||0,f=c||0;return"number"==typeof a&&void 0===b&&void 0===c&&(f=d,d=0,e=0),_(this,V(d,e,f))}rotateAxisAngle(a,b,c,d){if([a,b,c,d].some(a=>Number.isNaN(+a)))throw TypeError("CSSMatrix: expecting 4 values");return _(this,W(a,b,c,d))}skewX(a){return _(this,Z(a))}skewY(a){return _(this,$(a))}skew(a,b){return _(this,Y(a,b))}transformPoint(a){let b=this.m11*a.x+this.m21*a.y+this.m31*a.z+this.m41*a.w,c=this.m12*a.x+this.m22*a.y+this.m32*a.z+this.m42*a.w,d=this.m13*a.x+this.m23*a.y+this.m33*a.z+this.m43*a.w,e=this.m14*a.x+this.m24*a.y+this.m34*a.z+this.m44*a.w;return a instanceof DOMPoint?new DOMPoint(b,c,d,e):{x:b,y:c,z:d,w:e}}}function ab(a,b,c){var d;let e,[f,g,h]=c,[i,j,k]=(e=U(...d=[...b,0,1]),[,,,e.m44]=d,[(e=a.multiply(e)).m41,e.m42,e.m43,e.m44]),l=k-h;return[(i-f)*(Math.abs(h)/Math.abs(l)||1)+f,(j-g)*(Math.abs(h)/Math.abs(l)||1)+g]}function ac(a,b){let c,d,e,f,g,h,i=0,k=0,l=w(a),m=b&&Object.keys(b);if(!b||!m.length)return r(l);let n=D(l);if(!b.origin){let{origin:a}=j;Object.assign(b,{origin:a})}let o=function(a){let b=new aa,{origin:c}=a,[d,e]=c,{translate:f}=a,{rotate:g}=a,{skew:h}=a,{scale:i}=a;return Array.isArray(f)&&f.every(a=>!Number.isNaN(+a))&&f.some(a=>0!==a)?b=b.translate(...f):"number"!=typeof f||Number.isNaN(f)||(b=b.translate(f)),(g||h||i)&&(b=b.translate(d,e),Array.isArray(g)&&g.every(a=>!Number.isNaN(+a))&&g.some(a=>0!==a)?b=b.rotate(...g):"number"!=typeof g||Number.isNaN(g)||(b=b.rotate(g)),Array.isArray(h)&&h.every(a=>!Number.isNaN(+a))&&h.some(a=>0!==a)?(b=h[0]?b.skewX(h[0]):b,b=h[1]?b.skewY(h[1]):b):"number"!=typeof h||Number.isNaN(h)||(b=b.skewX(h)),Array.isArray(i)&&i.every(a=>!Number.isNaN(+a))&&i.some(a=>1!==a)?b=b.scale(...i):"number"!=typeof i||Number.isNaN(i)||(b=b.scale(i)),b=b.translate(-d,-e)),b}(b),{origin:p}=b,q={...C},s=[],t=0,u="",v=[],x=[];if(!o.isIdentity){for(c=0,e=l.length;c<e;c+=1)s=l[c],l[c]&&([u]=s),x[c]=u,"A"===u&&(s=J(n[c],q),l[c]=J(n[c],q),z(l,x,c),n[c]=J(n[c],q),z(n,x,c),e=Math.max(l.length,n.length)),t=(s=n[c]).length,q.x1=+s[t-2],q.y1=+s[t-1],q.x2=+s[t-4]||q.x1,q.y2=+s[t-3]||q.y1,v=[...v,{s:l[c],c:l[c][0],x:q.x1,y:q.y1}];return v.map(a=>{switch(u=a.c,s=a.s,u){case"L":case"H":case"V":return[g,h]=ab(o,[a.x,a.y],p),i!==g&&k!==h?s=["L",g,h]:k===h?s=["H",g]:i===g&&(s=["V",h]),i=g,k=h,s;default:for(d=1,f=s.length;d<f;d+=2)[i,k]=ab(o,[+s[d],+s[d+1]],p),s[d]=i,s[d+1]=k;return s}})}return r(l)}function ad(a,b){let{x:c,y:d}=a,{x:e,y:f}=b,g=Math.sqrt((c**2+d**2)*(e**2+f**2));return(c*f-d*e<0?-1:1)*Math.acos((c*e+d*f)/g)}function ae(a,b){let c=D(a),d="number"==typeof b,e,f,g,h=[],i=0,j=0,k=0,l=0,m=[],n=[],o=0,p={x:0,y:0},q=p,r=p,s=p,t=0;for(let a=0,u=c.length;a<u;a+=1)g=c[a],[f]=g,h=(e="M"===f)?h:[i,j,...g.slice(1)],e?([,k,l]=g,q=p={x:k,y:l},o=0,d&&b<.001&&(s=p)):"L"===f?{length:o,min:p,max:q,point:r}=H(...h,(b||0)-t):"A"===f?{length:o,min:p,max:q,point:r}=function(a,b,c,d,e,f,g,h,i,j){let k="number"==typeof j,l=a,m=b,n=0,o=[l,m,0],p=[l,m],q=0,r={x:0,y:0},s=[{x:l,y:m}];k&&j<=0&&(r={x:l,y:m});for(let t=0;t<=300;t+=1){if(q=t/300,{x:l,y:m}=function(a,b,c,d,e,f,g,h,i,j){let{abs:k,sin:l,cos:m,sqrt:n,PI:o}=Math,p=k(c),q=k(d),r=(e%360+360)%360*(o/180);if(a===h&&b===i)return{x:a,y:b};if(0===p||0===q)return H(a,b,h,i,j).point;let s=(a-h)/2,t=(b-i)/2,u=m(r)*s+l(r)*t,v=-l(r)*s+m(r)*t,w=u**2/p**2+v**2/q**2;w>1&&(p*=n(w),q*=n(w));let x=(p**2*q**2-p**2*v**2-q**2*u**2)/(p**2*v**2+q**2*u**2),y=(f!==g?1:-1)*n(x=x<0?0:x),z=p*v/q*y,A=-q*u/p*y,B=m(r)*z-l(r)*A+(a+h)/2,C=l(r)*z+m(r)*A+(b+i)/2,D={x:(u-z)/p,y:(v-A)/q},E=ad({x:1,y:0},D),F=ad(D,{x:(-u-z)/p,y:(-v-A)/q});!g&&F>0?F-=2*o:g&&F<0&&(F+=2*o);let G=E+(F%=2*o)*j,I=p*m(G),J=q*l(G);return{x:m(r)*I-l(r)*J+B,y:l(r)*I+m(r)*J+C}}(a,b,c,d,e,f,g,h,i,q),s=[...s,{x:l,y:m}],n+=G(p,[l,m]),p=[l,m],k&&n>j&&j>o[2]){let a=(n-j)/(n-o[2]);r={x:p[0]*(1-a)+o[0]*a,y:p[1]*(1-a)+o[1]*a}}o=[l,m,n]}return k&&j>=n&&(r={x:h,y:i}),{length:n,point:r,min:{x:Math.min(...s.map(a=>a.x)),y:Math.min(...s.map(a=>a.y))},max:{x:Math.max(...s.map(a=>a.x)),y:Math.max(...s.map(a=>a.y))}}}(...h,(b||0)-t):"C"===f?{length:o,min:p,max:q,point:r}=function(a,b,c,d,e,f,g,h,i){let j="number"==typeof i,k=a,l=b,m=0,n=[k,l,0],o=[k,l],p=0,q={x:0,y:0},r=[{x:k,y:l}];j&&i<=0&&(q={x:k,y:l});for(let s=0;s<=300;s+=1){if(p=s/300,{x:k,y:l}=function(a,b,c,d,e,f,g,h,i){let j=1-i;return{x:j**3*a+3*j**2*i*c+3*j*i**2*e+i**3*g,y:j**3*b+3*j**2*i*d+3*j*i**2*f+i**3*h}}(a,b,c,d,e,f,g,h,p),r=[...r,{x:k,y:l}],m+=G(o,[k,l]),o=[k,l],j&&m>i&&i>n[2]){let a=(m-i)/(m-n[2]);q={x:o[0]*(1-a)+n[0]*a,y:o[1]*(1-a)+n[1]*a}}n=[k,l,m]}return j&&i>=m&&(q={x:g,y:h}),{length:m,point:q,min:{x:Math.min(...r.map(a=>a.x)),y:Math.min(...r.map(a=>a.y))},max:{x:Math.max(...r.map(a=>a.x)),y:Math.max(...r.map(a=>a.y))}}}(...h,(b||0)-t):"Q"===f?{length:o,min:p,max:q,point:r}=function(a,b,c,d,e,f,g){let h="number"==typeof g,i=a,j=b,k=0,l=[i,j,0],m=[i,j],n=0,o={x:0,y:0},p=[{x:i,y:j}];h&&g<=0&&(o={x:i,y:j});for(let q=0;q<=300;q+=1){if(n=q/300,{x:i,y:j}=function(a,b,c,d,e,f,g){let h=1-g;return{x:h**2*a+2*h*g*c+g**2*e,y:h**2*b+2*h*g*d+g**2*f}}(a,b,c,d,e,f,n),p=[...p,{x:i,y:j}],k+=G(m,[i,j]),m=[i,j],h&&k>g&&g>l[2]){let a=(k-g)/(k-l[2]);o={x:m[0]*(1-a)+l[0]*a,y:m[1]*(1-a)+l[1]*a}}l=[i,j,k]}return h&&g>=k&&(o={x:e,y:f}),{length:k,point:o,min:{x:Math.min(...p.map(a=>a.x)),y:Math.min(...p.map(a=>a.y))},max:{x:Math.max(...p.map(a=>a.x)),y:Math.max(...p.map(a=>a.y))}}}(...h,(b||0)-t):"Z"===f&&(h=[i,j,k,l],{length:o,min:p,max:q,point:r}=H(...h,(b||0)-t)),d&&t<b&&t+o>=b&&(s=r),n=[...n,q],m=[...m,p],t+=o,[i,j]="Z"!==f?g.slice(-2):[k,l];return d&&b>=t&&(s={x:i,y:j}),{length:t,point:s,min:{x:Math.min(...m.map(a=>a.x)),y:Math.min(...m.map(a=>a.y))},max:{x:Math.max(...n.map(a=>a.x)),y:Math.max(...n.map(a=>a.y))}}}function af(a){if(!a)return{x:0,y:0,width:0,height:0,x2:0,y2:0,cx:0,cy:0,cz:0};let{min:{x:b,y:c},max:{x:d,y:e}}=ae(a),f=d-b,g=e-c;return{width:f,height:g,x:b,y:c,x2:d,y2:e,cx:b+f/2,cy:c+g/2,cz:Math.max(f,g)+Math.min(f,g)/2}}function ag(a){return ae(a).length}function ah(a,b){return ae(a,b).point}Object.assign(aa,{Translate:U,Rotate:V,RotateAxisAngle:W,Scale:X,SkewX:Z,SkewY:$,Skew:Y,Multiply:_,fromArray:Q,fromMatrix:R,fromString:S,toArray:T}),Object.assign(aa,{Version:"1.0.3"});class ai{constructor(a,b){let c,d,e=void 0===a;if(e||!a.length)throw TypeError(`${m}: "pathValue" is ${e?"undefined":"empty"}`);let f=u(a);if("string"==typeof f)throw TypeError(f);this.segments=f;let{width:g,height:h,cx:i,cy:k,cz:l}=this.getBBox(),{round:n,origin:o}=b||{};if("auto"===n){let a=`${Math.floor(Math.max(g,h))}`.length;c=a>=4?0:4-a}else Number.isInteger(n)||"off"===n?c=n:{round:c}=j;if(Array.isArray(o)&&o.length>=2){let[a,b,c]=o.map(Number);d=[Number.isNaN(a)?i:a,Number.isNaN(b)?k:b,Number.isNaN(c)?l:c]}else d=[i,k,l];return this.round=c,this.origin=d,this}getBBox(){return af(this.segments)}getTotalLength(){return ag(this.segments)}getPointAtLength(a){return ah(this.segments,a)}toAbsolute(){let{segments:a}=this;return this.segments=w(a),this}toRelative(){let{segments:a}=this;return this.segments=y(a),this}toCurve(){let{segments:a}=this;return this.segments=K(a),this}reverse(a){this.toAbsolute();let{segments:b}=this,c=O(b),d=c.length>1?c:0,e=d&&r(d).map((b,c)=>a?c?N(b):u(b):N(b)),f=[];return f=d?e.flat(1):a?b:N(b),this.segments=r(f),this}normalize(){let{segments:a}=this;return this.segments=D(a),this}optimize(){let{segments:a}=this;return this.segments=P(a,this.round),this}transform(a){if(!a||"object"!=typeof a||"object"==typeof a&&!["translate","rotate","skew","scale"].some(b=>b in a))return this;let b={};Object.keys(a).forEach(c=>{b[c]=Array.isArray(a[c])?[...a[c]]:Number(a[c])});let{segments:c}=this,[d,e,f]=this.origin,{origin:g}=b;if(Array.isArray(g)&&g.length>=2){let[a,c,h]=g.map(Number);b.origin=[Number.isNaN(a)?d:a,Number.isNaN(c)?e:c,h||f]}else b.origin=[d,e,f];return this.segments=ac(c,b),this}flipX(){return this.transform({rotate:[0,180,0]}),this}flipY(){return this.transform({rotate:[180,0,0]}),this}toString(){return M(this.segments,this.round)}}function aj(a){let b=0,c=0,d=0;return K(a).map(a=>"M"===a[0]?([,b,c]=a,0):(d=function(a,b,c,d,e,f,g,h){return 3*((h-b)*(c+e)-(g-a)*(d+f)+d*(a-e)-c*(b-f)+h*(e+a/3)-g*(f+b/3))/20}(b,c,...a.slice(1)),[b,c]=a.slice(-2),d)).reduce((a,b)=>a+b,0)}function ak(a,b){let c=u(a);if("string"==typeof c)throw TypeError(c);let d=[...c],e=ag(d),f=d.length-1,g=0,h=0,i=c[0],[j,k]=i.slice(-2);if(f<=0||!b||!Number.isFinite(b))return{segment:i,index:0,length:h,point:{x:j,y:k},lengthAtSegment:g};if(b>=e)return h=e-(g=ag(d=c.slice(0,-1))),{segment:c[f],index:f,length:h,lengthAtSegment:g};let l=[];for(;f>0;)i=d[f],h=e-(g=ag(d=d.slice(0,-1))),e=g,l.push({segment:i,index:f,length:h,lengthAtSegment:g}),f-=1;return l.find(({lengthAtSegment:a})=>a<=b)}function al(a,b){let c=u(a),d=D(c),e=ag(c),f=a=>{let c=a.x-b.x,d=a.y-b.y;return c*c+d*d},g,h,i,j,k=8,l=0,m=0,n=1/0;for(let a=0;a<=e;a+=k)(l=f(g=ah(d,a)))<n&&(h=g,m=a,n=l);k/=2;let o=0,p=0,q=0,r=0;for(;k>.5;)q=f(i=ah(d,o=m-k)),r=f(j=ah(d,p=m+k)),o>=0&&q<n?(h=i,m=o,n=q):p<=e&&r<n?(h=j,m=p,n=r):k/=2;return{closest:h,distance:Math.sqrt(n),segment:ak(c,m)}}function am(a){if("string"!=typeof a)return!1;let b=new s(a);for(p(b);b.index<b.max&&!b.err.length;)q(b);return!b.err.length&&"mM".includes(b.segments[0][0])}let an={line:["x1","y1","x2","y2"],circle:["cx","cy","r"],ellipse:["cx","cy","rx","ry"],rect:["width","height","x","y","rx","ry"],polygon:["points"],polyline:["points"],glyph:["d"]};Object.assign(ai,{CSSMatrix:aa,parsePathString:u,isPathArray:t,isCurveArray:B,isAbsoluteArray:v,isRelativeArray:x,isNormalizedArray:A,isValidPath:am,pathToAbsolute:w,pathToRelative:y,pathToCurve:K,pathToString:M,getDrawDirection:function(a){return aj(K(a))>=0},getPathArea:aj,getPathBBox:af,pathLengthFactory:ae,getTotalLength:ag,getPointAtLength:ah,getClosestPoint:function(a,b){return al(a,b).closest},getSegmentOfPoint:function(a,b){return al(a,b).segment},getPropertiesAtPoint:al,getPropertiesAtLength:ak,getSegmentAtLength:function(a,b){return ak(a,b).segment},isPointInStroke:function(a,b){let{distance:c}=al(a,b);return .001>Math.abs(c)},clonePath:r,splitPath:O,fixPath:function(a){let b=u(a),c=D(b),{length:d}=b,e="Z"===c.slice(-1)[0][0],[f,g]=c[0].slice(1),[h,i]=c[e?d-2:d-1].slice(-2);return e&&f===h&&g===i?b.slice(0,-1):b},roundPath:L,optimizePath:P,reverseCurve:function(a){let b=a.slice(1).map((b,c,d)=>c?[...d[c-1].slice(-2),...b.slice(1)]:[...a[0].slice(1),...b.slice(1)]).map(a=>a.map((b,c)=>a[a.length-c-2*(1-c%2)])).reverse();return[["M",...b[0].slice(0,2)],...b.map(a=>["C",...a.slice(2)])]},reversePath:N,normalizePath:D,transformPath:ac,shapeToPath:function(a,b){let c,d=Object.keys(an),{tagName:e}=a;if(e&&!d.some(a=>e===a))throw TypeError(`${m}: "${e}" is not SVGElement`);let f=document.createElementNS("http://www.w3.org/2000/svg","path"),g=e||a.type,h={};h.type=g;let i=an[g];e?(i.forEach(b=>{h[b]=a.getAttribute(b)}),Object.values(a.attributes).forEach(({name:a,value:b})=>{i.includes(a)||f.setAttribute(a,b)})):(Object.assign(h,a),Object.keys(h).forEach(a=>{i.includes(a)||"type"===a||f.setAttribute(a.replace(/[A-Z]/g,a=>`-${a.toLowerCase()}`),h[a])}));let{round:k}=j;return"circle"===g?c=M(function(a){let{cx:b,cy:c,r:d}=a;return[["M",b-d,c],["a",d,d,0,1,0,2*d,0],["a",d,d,0,1,0,-2*d,0]]}(h),k):"ellipse"===g?c=M(function(a){let{cx:b,cy:c,rx:d,ry:e}=a;return[["M",b-d,c],["a",d,e,0,1,0,2*d,0],["a",d,e,0,1,0,-2*d,0]]}(h),k):["polyline","polygon"].includes(g)?c=M(function(a){let b=[],c=(a.points||"").trim().split(/[\s|,]/).map(Number),d=0;for(;d<c.length;)b.push([d?"L":"M",c[d],c[d+1]]),d+=2;return"polygon"===a.type?[...b,["z"]]:b}(h),k):"rect"===g?c=M(function(a){let b=+a.x||0,c=+a.y||0,d=+a.width,e=+a.height,f=+a.rx,g=+a.ry;return f||g?(f=f||g,g=g||f,2*f>d&&(f-=(2*f-d)/2),2*g>e&&(g-=(2*g-e)/2),[["M",b+f,c],["h",d-2*f],["s",f,0,f,g],["v",e-2*g],["s",0,g,-f,g],["h",2*f-d],["s",-f,0,-f,-g],["v",2*g-e],["s",0,-g,f,-g]]):[["M",b,c],["h",d],["v",e],["H",b],["Z"]]}(h),k):"line"===g?c=M(function(a){let{x1:b,y1:c,x2:d,y2:e}=a;return[["M",b,c],["L",d,e]]}(h),k):"glyph"===g&&(c=e?a.getAttribute("d"):a.d),!!am(c)&&(f.setAttribute("d",c),b&&e&&(a.before(f,a),a.remove()),f)},options:j},{Version:"1.0.5"});let ao=a=>{let{width:b,height:c,cx:d,cy:e,rx:f,ry:j,startOffset:k,reversed:l,text:m,svgProps:n,ellipseProps:o,textPathProps:p,textProps:q,tspanProps:r}=a,[s,t]=(0,g.useState)(!1),[u]=(0,g.useState)(`ellipse-id${(0,g.useId)().replaceAll(":","-")}`),v=(0,g.useRef)();if((0,g.useEffect)(()=>{if(v.current){let a={id:u,type:"ellipse",rx:f,ry:j,cx:d,cy:e,style:"fill: none;",...o},b=v.current,c=ai.shapeToPath(a,!0),g=document.getElementById(u);if(g&&g.remove(),b.prepend(c),l){let a=c.getAttribute("d"),b=ai.reversePath(a),d=ai.pathToString(b);c.setAttribute("d",d)}t(!0)}},[v.current,l,b,c,n,d,e,f,j,o]),null==b)throw Error("ReactCurvedText Error: width is required");if(null==c)throw Error("ReactCurvedText Error: height is required");if(null==d)throw Error("ReactCurvedText Error: cx is required");if(null==e)throw Error("ReactCurvedText Error: cy is required");if(null==f)throw Error("ReactCurvedText Error: rx is required");if(null==j)throw Error("ReactCurvedText Error: ry is required");if(null==m)throw Error("ReactCurvedText Error: text is required");let w=JSON.stringify({width:b,height:c,cx:d,cy:e,rx:f,ry:j,startOffset:k,reversed:l,text:m,svgProps:n,ellipseProps:o,textPathProps:p,textProps:q,tspanProps:r,rendered:s});return h().createElement("svg",i({ref:v,height:c,width:b},n),h().createElement("text",i({key:w},q),h().createElement("textPath",i({xlinkHref:`#${u}`,startOffset:k},p),h().createElement("tspan",r,m))))};var ap=c(77618),aq=c(70189);function ar(){let a=(0,ap.c3)("AboutArea"),b=(0,ap.c3)("HeaderBtn");return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("section",{className:"about-one",id:"about",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-7",children:(0,d.jsxs)("div",{className:"about-one__content",children:[(0,d.jsxs)("div",{className:"sec-title tg-heading-subheading animation-style2",children:[(0,d.jsxs)("div",{className:"sec-title__tagline",children:[(0,d.jsx)("div",{className:"line"}),(0,d.jsx)("div",{className:"text tg-element-title",children:(0,d.jsx)("h4",{children:a("title")})}),(0,d.jsx)("div",{className:"line2"})]}),(0,d.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[a("sub_title1")," ",(0,d.jsxs)("span",{children:[" ",a("sub_title2")," "]}),(0,d.jsxs)("span",{children:[" ",a("sub_title3")]})]})]}),(0,d.jsx)("div",{className:"about-one__content-text1",children:(0,d.jsx)("p",{children:a("sm_des")})}),(0,d.jsx)("div",{className:"about-one__content-text2",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-6 col-lg-6 col-md-6",children:(0,d.jsxs)("div",{className:"about-one__content-text2-single",children:[(0,d.jsxs)("div",{className:"about-one__content-text2-single-top",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{children:(0,d.jsx)("img",{style:{width:"32px"},src:"assets/images/about/Environment.svg",alt:""})})}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h3",{children:a("card1_title")})})]}),(0,d.jsx)("p",{children:a("card1_des")})]})}),(0,d.jsx)("div",{className:"col-xl-6 col-lg-6 col-md-6",children:(0,d.jsxs)("div",{className:"about-one__content-text2-single",children:[(0,d.jsxs)("div",{className:"about-one__content-text2-single-top",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{children:(0,d.jsx)("img",{style:{width:"32px"},src:"assets/images/about/Carving.svg",alt:""})})}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h3",{children:a("card2_title")})})]}),(0,d.jsx)("p",{children:a("card2_des")})]})})]})}),(0,d.jsx)("div",{className:"about-one__content-bottom",children:(0,d.jsxs)("div",{className:"contact-box",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone2"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:b("phone_des")}),(0,d.jsx)("h4",{children:(0,d.jsx)(aq.A,{})})]})]})})]})}),(0,d.jsx)("div",{className:"about-one__img-box col-xl-5 ",children:(0,d.jsxs)("div",{className:"about-one__img",children:[(0,d.jsx)("div",{className:"shape2 float-bob-y",children:(0,d.jsx)("img",{src:"assets/images/shapes/about-v1-shape2.png",alt:""})}),(0,d.jsx)("div",{className:"about-one__img1 reveal",children:(0,d.jsx)("img",{src:"assets/images/about/about-v1-img3.png",alt:""})}),(0,d.jsxs)("div",{className:"about-one__img2",children:[(0,d.jsx)("div",{className:"about-one__img2-inner reveal",children:(0,d.jsx)("img",{src:"assets/images/about/about-v4-img2.png",alt:""})}),(0,d.jsx)("div",{className:"about-one__circle-text",children:(0,d.jsxs)("div",{className:"about-one__round-text-box",children:[(0,d.jsx)("div",{className:"inner",children:(0,d.jsx)("div",{className:"about-one__curved-circle rotate-me",children:(0,d.jsx)(ao,{width:"150",height:"150",cx:"75",cy:"75",rx:"55",ry:"55",startOffset:"0",reversed:!0,text:"SAK WOODWORKS SINCE 2016",textProps:{style:{fontSize:"14"}},textPathProps:{fill:"#ffffff"},tspanProps:null,ellipseProps:null,svgProps:null})})}),(0,d.jsx)("div",{className:"overlay-icon-box",children:(0,d.jsx)(f(),{href:"#",children:(0,d.jsx)("img",{src:"assets/images/icon/symbol.svg",style:{width:"64px"}})})})]})})]})]})})]})})})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11433:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Branch.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Branch.js","default")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22346:(a,b,c)=>{"use strict";c.d(b,{default:()=>bs});var d,e,f=c(60687),g=c(43210),h=c(77618);let i="carousel",j="controller",k="portal",l="toolbar",m="zoom",n="loading",o="error",p="complete",q=a=>`active-slide-${a}`;q(n),q("playing"),q(o),q(p);let r="flex_center",s="no_scroll_padding",t="slide_wrapper",u="prev",v="next",w="swipe",x="close",y="onPointerDown",z="onPointerMove",A="onPointerUp",B="onPointerLeave",C="onPointerCancel",D="onKeyDown",E="onWheel",F="icon",G="contain",H="cover",I="Unknown action type";var J=c(51215);let K="yarl__";function L(...a){return[...a].filter(Boolean).join(" ")}function M(a){return`${K}${a}`}function N(a){return`--${K}${a}`}function O(a,b){return`${a}${b?`_${b}`:""}`}function P(a){return b=>O(a,b)}function Q(...a){return()=>{a.forEach(a=>{a()})}}function R(a,b,c){return()=>{let d=g.useContext(c);if(!d)throw Error(`${a} must be used within a ${b}.Provider`);return d}}function S(a,b=0){let c=10**b;return Math.round((a+Number.EPSILON)*c)/c}function T(a){return void 0===a.type||"image"===a.type}function U(a,b){return a.imageFit===H||a.imageFit!==G&&b===H}function V(a){return"string"==typeof a?Number.parseInt(a,10):a}function W(a){if("number"==typeof a)return{pixel:a};if("string"==typeof a){let b=V(a);return a.endsWith("%")?{percent:b}:{pixel:b}}return{pixel:0}}function X(a,b){var c;return a[(c=a.length)>0?(b%c+c)%c:0]}function Y(a,b){return a.length>0?X(a,b):void 0}let Z=Number(g.version.split(".")[0])>=19,$={open:!1,close:()=>{},index:0,slides:[],render:{},plugins:[],toolbar:{buttons:[x]},labels:{},animation:{fade:250,swipe:500,easing:{fade:"ease",swipe:"ease-out",navigation:"ease-in-out"}},carousel:{finite:!1,preload:2,padding:"16px",spacing:"30%",imageFit:G,imageProps:{}},controller:{ref:null,focus:!0,aria:!1,touchAction:"none",closeOnPullUp:!1,closeOnPullDown:!1,closeOnBackdropClick:!1,preventDefaultWheelX:!0,preventDefaultWheelY:!1},portal:{},noScroll:{disabled:!1},on:{},styles:{},className:""};function _(a,b){return{name:a,component:b}}function aa(a,b){return{module:a,children:b}}function ab(a,b,c){return a.flatMap(a=>{var d;return null!=(d=function a(b,c,d){return b.module.name===c?d(b):b.children?[aa(b.module,b.children.flatMap(b=>{var e;return null!=(e=a(b,c,d))?e:[]}))]:[b]}(a,b,c))?d:[]})}let ac=g.createContext(null),ad=R("useDocument","DocumentContext",ac);function ae({nodeRef:a,children:b}){let c=g.useMemo(()=>{let b=b=>{var c;return(null==(c=b||a.current)?void 0:c.ownerDocument)||document};return{getOwnerDocument:b,getOwnerWindow:a=>{var c;return(null==(c=b(a))?void 0:c.defaultView)||window}}},[a]);return g.createElement(ac.Provider,{value:c},b)}let af=g.createContext(null),ag=R("useEvents","EventsContext",af);function ah({children:a}){let[b]=g.useState({});g.useEffect(()=>()=>{Object.keys(b).forEach(a=>delete b[a])},[b]);let c=g.useMemo(()=>{let a=(a,c)=>{var d;null==(d=b[a])||d.splice(0,b[a].length,...b[a].filter(a=>a!==c))};return{publish:(...[a,c])=>{var d;null==(d=b[a])||d.forEach(a=>a(c))},subscribe:(c,d)=>(b[c]||(b[c]=[]),b[c].push(d),()=>a(c,d)),unsubscribe:a}},[b]);return g.createElement(af.Provider,{value:c},a)}let ai=g.createContext(null),aj=R("useLightboxProps","LightboxPropsContext",ai);function ak({children:a,...b}){return g.createElement(ai.Provider,{value:b},a)}let al=g.createContext(null),am=R("useLightboxState","LightboxStateContext",al),an=g.createContext(null),ao=R("useLightboxDispatch","LightboxDispatchContext",an);function ap(a,b){switch(b.type){case"swipe":{var c;let{slides:d}=a,e=(null==b?void 0:b.increment)||0,f=a.globalIndex+e,g=(c=d.length)>0?(f%c+c)%c:0,h=Y(d,g);return{slides:d,currentIndex:g,globalIndex:f,currentSlide:h,animation:e||b.duration?{increment:e,duration:b.duration,easing:b.easing}:void 0}}case"update":if(b.slides!==a.slides||b.index!==a.currentIndex)return{slides:b.slides,currentIndex:b.index,globalIndex:b.index,currentSlide:Y(b.slides,b.index)};return a;default:throw Error(I)}}function aq({slides:a,index:b,children:c}){let[d,e]=g.useReducer(ap,{slides:a,currentIndex:b,globalIndex:b,currentSlide:Y(a,b)});g.useEffect(()=>{e({type:"update",slides:a,index:b})},[a,b]);let f=g.useMemo(()=>({...d,state:d,dispatch:e}),[d,e]);return g.createElement(an.Provider,{value:e},g.createElement(al.Provider,{value:f},c))}let ar=g.createContext(null),as=R("useTimeouts","TimeoutsContext",ar);function at({children:a}){let[b]=g.useState([]);g.useEffect(()=>()=>{b.forEach(a=>window.clearTimeout(a)),b.splice(0,b.length)},[b]);let c=g.useMemo(()=>{let a=a=>{b.splice(0,b.length,...b.filter(b=>b!==a))};return{setTimeout:(c,d)=>{let e=window.setTimeout(()=>{a(e),c()},d);return b.push(e),e},clearTimeout:b=>{void 0!==b&&(a(b),window.clearTimeout(b))}}},[b]);return g.createElement(ar.Provider,{value:c},a)}let au=g.forwardRef(function({label:a,className:b,icon:c,renderIcon:d,onClick:e,style:f,...h},i){var j;let{styles:k,labels:l}=aj(),m=null!=(j=null==l?void 0:l[a])?j:a;return g.createElement("button",{ref:i,type:"button",title:m,"aria-label":m,className:L(M("button"),b),onClick:e,style:{...f,...k.button},...h},d?d():g.createElement(c,{className:M(F),style:k.icon}))});function av(a,b){var c=g.createElement("g",{fill:"currentColor"},g.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),b);let d=a=>g.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",...a},c);return d.displayName=a,d}let aw=av("Close",g.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),ax=av("Previous",g.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})),ay=av("Next",g.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),az=av("Loading",g.createElement(g.Fragment,null,Array.from({length:8}).map((a,b,c)=>g.createElement("line",{key:b,x1:"12",y1:"6.5",x2:"12",y2:"1.8",strokeLinecap:"round",strokeWidth:"2.6",stroke:"currentColor",strokeOpacity:1/c.length*(b+1),transform:`rotate(${360/c.length*b}, 12, 12)`})))),aA=av("Error",g.createElement("path",{d:"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"})),aB=g.useEffect;function aC(){let[a,b]=g.useState(!1);return g.useEffect(()=>{var a,c;let d=null==(a=window.matchMedia)?void 0:a.call(window,"(prefers-reduced-motion: reduce)");b(null==d?void 0:d.matches);let e=a=>b(a.matches);return null==(c=null==d?void 0:d.addEventListener)||c.call(d,"change",e),()=>{var a;return null==(a=null==d?void 0:d.removeEventListener)?void 0:a.call(d,"change",e)}},[]),a}function aD(a,b){let c=g.useRef(),d=g.useRef(),e=aC();return aB(()=>{var f,g,h;if(a.current&&void 0!==c.current&&!e){let{keyframes:e,duration:i,easing:j,onfinish:k}=b(c.current,a.current.getBoundingClientRect(),function(a){let b=0,c=0,d=0,e=window.getComputedStyle(a).transform.match(/matrix.*\((.+)\)/);if(e){let a=e[1].split(",").map(V);6===a.length?(b=a[4],c=a[5]):16===a.length&&(b=a[12],c=a[13],d=a[14])}return{x:b,y:c,z:d}}(a.current))||{};if(e&&i){null==(f=d.current)||f.cancel(),d.current=void 0;try{d.current=null==(h=(g=a.current).animate)?void 0:h.call(g,e,{duration:i,easing:j})}catch(a){console.error(a)}d.current&&(d.current.onfinish=()=>{d.current=void 0,null==k||k()})}}c.current=void 0}),{prepareAnimation:a=>{c.current=a},isAnimationPlaying:()=>{var a;return(null==(a=d.current)?void 0:a.playState)==="running"}}}function aE(){let a=g.useRef(null),b=g.useRef(),[c,d]=g.useState();return{setContainerRef:g.useCallback(c=>{a.current=c,b.current&&(b.current.disconnect(),b.current=void 0);let e=()=>{if(c){let a=window.getComputedStyle(c),b=a=>parseFloat(a)||0;d({width:Math.round(c.clientWidth-b(a.paddingLeft)-b(a.paddingRight)),height:Math.round(c.clientHeight-b(a.paddingTop)-b(a.paddingBottom))})}else d(void 0)};e(),c&&"undefined"!=typeof ResizeObserver&&(b.current=new ResizeObserver(e),b.current.observe(c))},[]),containerRef:a,containerRect:c}}function aF(){let a=g.useRef(),{setTimeout:b,clearTimeout:c}=as();return g.useCallback((d,e)=>{c(a.current),a.current=b(d,e>0?e:0)},[b,c])}function aG(a){let b=g.useRef(a);return aB(()=>{b.current=a}),g.useCallback((...a)=>{var c;return null==(c=b.current)?void 0:c.call(b,...a)},[])}function aH(a,b){"function"==typeof a?a(b):a&&(a.current=b)}function aI(a,b){return g.useMemo(()=>null==a&&null==b?null:c=>{aH(a,c),aH(b,c)},[a,b])}function aJ(){let[a,b]=g.useState(!1);return aB(()=>{b("rtl"===window.getComputedStyle(window.document.documentElement).direction)},[]),a}function aK(a,b){let c=g.useRef(0),d=aF(),e=aG((...b)=>{c.current=Date.now(),a(b)});return g.useCallback((...a)=>{d(()=>{e(a)},b-(Date.now()-c.current))},[b,e,d])}let aL=P("slide"),aM=P("slide_image");function aN({slide:a,offset:b,render:c,rect:d,imageFit:e,imageProps:f,onClick:h,onLoad:i,onError:j,style:k}){var l,m,r,s,t,u,v;let[w,x]=g.useState(n),{publish:y}=ag(),{setTimeout:z}=as(),A=g.useRef(null);g.useEffect(()=>{0===b&&y(q(w))},[b,w,y]);let B=aG(a=>{("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{a.parentNode&&(x(p),z(()=>{null==i||i(a)},0))})}),C=g.useCallback(a=>{A.current=a,(null==a?void 0:a.complete)&&B(a)},[B]),D=g.useCallback(a=>{B(a.currentTarget)},[B]),E=aG(()=>{x(o),null==j||j()}),G=U(a,e),H=(a,b)=>Number.isFinite(a)?a:b,I=H(Math.max(...(null!=(m=null==(l=a.srcSet)?void 0:l.map(a=>a.width))?m:[]).concat(a.width?[a.width]:[]).filter(Boolean)),(null==(r=A.current)?void 0:r.naturalWidth)||0),J=H(Math.max(...(null!=(t=null==(s=a.srcSet)?void 0:s.map(a=>a.height))?t:[]).concat(a.height?[a.height]:[]).filter(Boolean)),(null==(u=A.current)?void 0:u.naturalHeight)||0),K=I&&J?{maxWidth:`min(${I}px, 100%)`,maxHeight:`min(${J}px, 100%)`}:{maxWidth:"100%",maxHeight:"100%"},N=null==(v=a.srcSet)?void 0:v.sort((a,b)=>a.width-b.width).map(a=>`${a.src} ${a.width}w`).join(", "),{style:O,className:P,...Q}=f||{};return g.createElement(g.Fragment,null,g.createElement("img",{ref:C,onLoad:D,onError:E,onClick:h,draggable:!1,className:L(M(aM()),G&&M(aM("cover")),w!==p&&M(aM("loading")),P),style:{...K,...k,...O},...Q,alt:a.alt,sizes:void 0,srcSet:N,src:a.src}),w!==p&&g.createElement("div",{className:M(aL("placeholder"))},w===n&&((null==c?void 0:c.iconLoading)?c.iconLoading():g.createElement(az,{className:L(M(F),M(aL(n)))})),w===o&&((null==c?void 0:c.iconError)?c.iconError():g.createElement(aA,{className:L(M(F),M(aL(o)))}))))}let aO=g.forwardRef(function({className:a,children:b,...c},d){let e=g.useRef(null);return g.createElement(ae,{nodeRef:e},g.createElement("div",{ref:aI(d,e),className:L(M("root"),a),...c},b))});function aP(a,b,c,d,e){g.useEffect(()=>e?()=>{}:Q(a(y,b),a(z,c),a(A,d),a(B,d),a(C,d)),[a,b,c,d,e])}!function(a){a[a.NONE=0]="NONE",a[a.SWIPE=1]="SWIPE",a[a.PULL=2]="PULL",a[a.ANIMATION=3]="ANIMATION"}(d||(d={})),function(a){a[a.NONE=0]="NONE",a[a.SWIPE=1]="SWIPE",a[a.PULL=2]="PULL"}(e||(e={}));let aQ=P("container"),aR=g.createContext(null),aS=R("useController","ControllerContext",aR),aT=_(j,function({children:a,...b}){var c;let{carousel:f,animation:h,controller:i,on:j,styles:l,render:m}=b,{closeOnPullUp:n,closeOnPullDown:o,preventDefaultWheelX:p,preventDefaultWheelY:q}=i,[s,t]=g.useState(),F=am(),G=ao(),[H,I]=g.useState(d.NONE),J=g.useRef(0),K=g.useRef(0),O=g.useRef(1),{registerSensors:P,subscribeSensors:R}=function(){let[a]=g.useState({}),b=g.useCallback((b,c)=>{var d;null==(d=a[b])||d.forEach(a=>{c.isPropagationStopped()||a(c)})},[a]);return{registerSensors:g.useMemo(()=>({onPointerDown:a=>b(y,a),onPointerMove:a=>b(z,a),onPointerUp:a=>b(A,a),onPointerLeave:a=>b(B,a),onPointerCancel:a=>b(C,a),onKeyDown:a=>b(D,a),onKeyUp:a=>b("onKeyUp",a),onWheel:a=>b(E,a)}),[b]),subscribeSensors:g.useCallback((b,c)=>(a[b]||(a[b]=[]),a[b].unshift(c),()=>{let d=a[b];d&&d.splice(0,d.length,...d.filter(a=>a!==c))}),[a])}}(),{subscribe:T,publish:U}=ag(),V=aF(),X=aF(),Y=aF(),{containerRef:Z,setContainerRef:$,containerRect:_}=aE(),aa=aI(function({preventDefaultWheelX:a,preventDefaultWheelY:b}){let c=g.useRef(null),d=aG(c=>{let d=Math.abs(c.deltaX)>Math.abs(c.deltaY);(d&&a||!d&&b||c.ctrlKey)&&c.preventDefault()});return g.useCallback(a=>{var b;a?a.addEventListener("wheel",d,{passive:!1}):null==(b=c.current)||b.removeEventListener("wheel",d),c.current=a},[d])}({preventDefaultWheelX:p,preventDefaultWheelY:q}),$),ab=g.useRef(null),ac=aI(ab,void 0),{getOwnerDocument:ae}=ad(),af=aJ(),ah=a=>(af?-1:1)*("number"==typeof a?a:1),ai=aG(()=>{var a;return null==(a=Z.current)?void 0:a.focus()}),aj=aG(()=>b),ak=aG(()=>F),al=g.useCallback(a=>U(u,a),[U]),an=g.useCallback(a=>U(v,a),[U]),ap=g.useCallback(()=>U(x),[U]),aq=a=>!(f.finite&&(ah(a)>0&&0===F.currentIndex||0>ah(a)&&F.currentIndex===F.slides.length-1)),ar=a=>{var b;J.current=a,null==(b=Z.current)||b.style.setProperty(N("swipe_offset"),`${Math.round(a)}px`)},at=a=>{var b,c;K.current=a,O.current=Math.min(Math.max(S(1-(o&&a>0?a:n&&a<0?-a:0)/60*.5,2),.5),1),null==(b=Z.current)||b.style.setProperty(N("pull_offset"),`${Math.round(a)}px`),null==(c=Z.current)||c.style.setProperty(N("pull_opacity"),`${O.current}`)},{prepareAnimation:au}=aD(ab,(a,b,c)=>{if(ab.current&&_)return{keyframes:[{transform:`translate(0, ${a.rect.y-b.y+c.y}px)`,opacity:a.opacity},{transform:"translate(0, 0)",opacity:1}],duration:a.duration,easing:h.easing.fade}}),av=(a,b)=>{if(n||o){at(a);let c=0;ab.current&&(c=h.fade*(b?2:1),au({rect:ab.current.getBoundingClientRect(),opacity:O.current,duration:c})),Y(()=>{at(0),I(d.NONE)},c),I(d.ANIMATION),b||ap()}},{prepareAnimation:aw,isAnimationPlaying:ax}=aD(ab,(a,b,c)=>{var d;if(ab.current&&_&&(null==(d=F.animation)?void 0:d.duration)){let d=W(f.spacing),e=(d.percent?d.percent*_.width/100:d.pixel)||0;return{keyframes:[{transform:`translate(${ah(F.globalIndex-a.index)*(_.width+e)+a.rect.x-b.x+c.x}px, 0)`},{transform:"translate(0, 0)"}],duration:F.animation.duration,easing:F.animation.easing}}}),ay=aG(a=>{var b,c;let e=a.offset||0,f=e?h.swipe:null!=(b=h.navigation)?b:h.swipe,g=e||ax()?h.easing.swipe:h.easing.navigation,{direction:i}=a,j=null!=(c=a.count)?c:1,k=d.ANIMATION,l=f*j;if(!i){let b=null==_?void 0:_.width,c=a.duration||0,d=b?f/b*Math.abs(e):f;0!==j?(c<d?l=l/d*Math.max(c,d/5):b&&(l=f/b*(b-Math.abs(e))),i=ah(e)>0?u:v):l=f/2}let m=0;i===u?aq(ah(1))?m=-j:(k=d.NONE,l=f):i===v&&(aq(ah(-1))?m=j:(k=d.NONE,l=f)),X(()=>{ar(0),I(d.NONE)},l=Math.round(l)),ab.current&&aw({rect:ab.current.getBoundingClientRect(),index:F.globalIndex}),I(k),U(w,{type:"swipe",increment:m,duration:l,easing:g})});g.useEffect(()=>{var a,b;(null==(a=F.animation)?void 0:a.increment)&&(null==(b=F.animation)?void 0:b.duration)&&V(()=>G({type:"swipe",increment:0}),F.animation.duration)},[F.animation,G,V]);let az=[R,aq,(null==_?void 0:_.width)||0,h.swipe,()=>I(d.SWIPE),a=>ar(a),(a,b)=>ay({offset:a,duration:b,count:1}),a=>ay({offset:a,count:0})],aA=[()=>{o&&I(d.PULL)},a=>at(a),a=>av(a),a=>av(a,!0)];!function(a,b,c,d,f,h,i,j,k,l,m,n,o,p){let q=g.useRef(0),r=g.useRef([]),s=g.useRef(),t=g.useRef(0),u=g.useRef(e.NONE),v=g.useCallback(a=>{s.current===a.pointerId&&(s.current=void 0,u.current=e.NONE);let b=r.current;b.splice(0,b.length,...b.filter(b=>b.pointerId!==a.pointerId))},[]),w=g.useCallback(a=>{v(a),a.persist(),r.current.push(a)},[v]),x=aG(a=>{w(a)}),y=(a,b)=>l&&a>b||k&&a<-b,z=aG(a=>{if(r.current.find(b=>b.pointerId===a.pointerId)&&s.current===a.pointerId){let a=Date.now()-t.current,b=q.current;u.current===e.SWIPE?Math.abs(b)>.3*c||Math.abs(b)>5&&a<d?i(b,a):j(b):u.current===e.PULL&&(y(b,60)?o(b,a):p(b)),q.current=0,u.current=e.NONE}v(a)});aP(a,x,aG(a=>{let c=r.current.find(b=>b.pointerId===a.pointerId);if(c){let d=s.current===a.pointerId;if(0===a.buttons)return void(d&&0!==q.current?z(a):v(c));let g=a.clientX-c.clientX,i=a.clientY-c.clientY;if(void 0===s.current){let c=b=>{w(a),s.current=a.pointerId,t.current=Date.now(),u.current=b};Math.abs(g)>Math.abs(i)&&Math.abs(g)>30&&b(g)?(c(e.SWIPE),f()):Math.abs(i)>Math.abs(g)&&y(i,30)&&(c(e.PULL),m())}else d&&(u.current===e.SWIPE?(q.current=g,h(g)):u.current===e.PULL&&(q.current=i,n(i)))}}),z)}(...az,n,o,...aA),function(a,b,c,e,f,h,i,j,k){let l=g.useRef(0),m=g.useRef(0),n=g.useRef(),o=g.useRef(),p=g.useRef(0),q=g.useRef(),r=g.useRef(0),{setTimeout:s,clearTimeout:t}=as(),u=g.useCallback(()=>{n.current&&(t(n.current),n.current=void 0)},[t]),v=g.useCallback(()=>{o.current&&(t(o.current),o.current=void 0)},[t]),w=aG(()=>{a!==d.SWIPE&&(l.current=0,r.current=0,u(),v())});g.useEffect(w,[a,w]);let x=aG(a=>{o.current=void 0,l.current===a&&k(l.current)}),y=aG(b=>{if(b.ctrlKey||Math.abs(b.deltaY)>Math.abs(b.deltaX))return;let g=a=>{p.current=a,t(q.current),q.current=a>0?s(()=>{p.current=0,q.current=void 0},300):void 0};if(a===d.NONE){if(Math.abs(b.deltaX)<=1.2*Math.abs(p.current))return void g(b.deltaX);if(!c(-b.deltaX))return;if(m.current+=b.deltaX,u(),Math.abs(m.current)>30)m.current=0,g(0),r.current=Date.now(),h();else{let a=m.current;n.current=s(()=>{n.current=void 0,a===m.current&&(m.current=0)},f)}}else if(a===d.SWIPE){let a=l.current-b.deltaX;if(l.current=a=Math.min(Math.abs(a),e)*Math.sign(a),i(a),v(),Math.abs(a)>.2*e){g(b.deltaX),j(a,Date.now()-r.current);return}o.current=s(()=>x(a),2*f)}else g(b.deltaX)});g.useEffect(()=>b(E,y),[b,y])}(H,...az);let aB=aG(()=>{i.focus&&ae().querySelector(`.${M(k)} .${M(aQ())}`)&&ai()});g.useEffect(aB,[aB]);let aC=aG(()=>{var a;null==(a=j.view)||a.call(j,{index:F.currentIndex})});g.useEffect(aC,[F.globalIndex,aC]),g.useEffect(()=>Q(T(u,a=>ay({direction:u,...a})),T(v,a=>ay({direction:v,...a})),T(w,a=>G(a))),[T,ay,G]);let aH=g.useMemo(()=>({prev:al,next:an,close:ap,focus:ai,slideRect:_?function(a,b){let c=W(b),d=void 0!==c.percent?a.width/100*c.percent:c.pixel;return{width:Math.max(a.width-2*d,0),height:Math.max(a.height-2*d,0)}}(_,f.padding):{width:0,height:0},containerRect:_||{width:0,height:0},subscribeSensors:R,containerRef:Z,setCarouselRef:ac,toolbarWidth:s,setToolbarWidth:t}),[al,an,ap,ai,R,_,Z,ac,s,t,f.padding]);return g.useImperativeHandle(i.ref,()=>({prev:al,next:an,close:ap,focus:ai,getLightboxProps:aj,getLightboxState:ak}),[al,an,ap,ai,aj,ak]),g.createElement("div",{ref:aa,className:L(M(aQ()),M(r)),style:{...H===d.SWIPE?{[N("swipe_offset")]:`${Math.round(J.current)}px`}:null,...H===d.PULL?{[N("pull_offset")]:`${Math.round(K.current)}px`,[N("pull_opacity")]:`${O.current}`}:null,..."none"!==i.touchAction?{[N("controller_touch_action")]:i.touchAction}:null,...l.container},...i.aria?{role:"presentation","aria-live":"polite"}:null,tabIndex:-1,...P},_&&g.createElement(aR.Provider,{value:aH},a,null==(c=m.controls)?void 0:c.call(m)))});function aU(a){return O("slide",a)}function aV({slide:a,offset:b}){var c,d,e,f;let h,i=g.useRef(null),{currentIndex:j}=am(),{slideRect:k,close:l,focus:m}=aS(),{render:n,carousel:{imageFit:o,imageProps:p},on:{click:q},controller:{closeOnBackdropClick:s},styles:{slide:u}}=aj(),{getOwnerDocument:v}=ad(),w=0!==b;return g.useEffect(()=>{var a;w&&(null==(a=i.current)?void 0:a.contains(v().activeElement))&&m()},[w,m,v]),g.createElement("div",{ref:i,className:L(M(aU()),!w&&M(aU("current")),M(r)),...{inert:Z?w:w?"":void 0},onClick:a=>{let b=i.current,c=a.target instanceof HTMLElement?a.target:void 0;s&&c&&b&&(c===b||Array.from(b.children).find(a=>a===c)&&c.classList.contains(M(t)))&&l()},style:u},(!(h=null==(c=n.slide)?void 0:c.call(n,{slide:a,offset:b,rect:k}))&&T(a)&&(h=g.createElement(aN,{slide:a,offset:b,render:n,rect:k,imageFit:o,imageProps:p,onClick:w?void 0:()=>null==q?void 0:q({index:j})})),h?g.createElement(g.Fragment,null,null==(d=n.slideHeader)?void 0:d.call(n,{slide:a}),(null!=(e=n.slideContainer)?e:({children:a})=>a)({slide:a,children:h}),null==(f=n.slideFooter)?void 0:f.call(n,{slide:a})):null))}function aW(){let a=aj().styles.slide;return g.createElement("div",{className:M("slide"),style:a})}let aX=_(i,function({carousel:a}){let{slides:b,currentIndex:c,globalIndex:d}=am(),{setCarouselRef:e}=aS(),f=W(a.spacing),h=W(a.padding),j=function(a,b,c=0){return Math.min(a.preload,Math.max(a.finite?b.length-1:Math.floor(b.length/2),c))}(a,b,1),k=[];if(b.length>0)for(let e=c-j;e<=c+j;e+=1){let f=X(b,e),g=d-c+e,h=a.finite&&(e<0||e>b.length-1);k.push(h?{key:g}:{key:[`${g}`,T(f)?f.src:void 0].filter(Boolean).join("|"),offset:e-c,slide:f})}return g.createElement("div",{ref:e,className:L(M(O(i,void 0)),k.length>0&&M(O(i,"with_slides"))),style:{[`${N(O(i,"slides_count"))}`]:k.length,[`${N(O(i,"spacing_px"))}`]:f.pixel||0,[`${N(O(i,"spacing_percent"))}`]:f.percent||0,[`${N(O(i,"padding_px"))}`]:h.pixel||0,[`${N(O(i,"padding_percent"))}`]:h.percent||0}},k.map(({key:a,slide:b,offset:c})=>b?g.createElement(aV,{key:a,slide:b,offset:c}):g.createElement(aW,{key:a})))});function aY(){let{carousel:a}=aj(),{slides:b,currentIndex:c}=am();return{prevDisabled:0===b.length||a.finite&&0===c,nextDisabled:0===b.length||a.finite&&c===b.length-1}}function aZ({label:a,icon:b,renderIcon:c,action:d,onClick:e,disabled:f,style:h}){return g.createElement(au,{label:a,icon:b,renderIcon:c,className:M(`navigation_${d}`),disabled:f,onClick:e,style:h,...function(a,b=!1){let c=g.useRef();return aB(()=>{b&&c.current&&(c.current=!1,a())},[b,a]),{onFocus:g.useCallback(()=>{c.current=!0},[]),onBlur:g.useCallback(()=>{c.current=!1},[])}}(aS().focus,f)})}let a$=_("navigation",function({render:{buttonPrev:a,buttonNext:b,iconPrev:c,iconNext:d},styles:e}){let{prev:f,next:h,subscribeSensors:i}=aS(),{prevDisabled:j,nextDisabled:k}=aY();return!function(a){var b;let c=aJ(),{publish:d}=ag(),{animation:e}=aj(),{prevDisabled:f,nextDisabled:h}=aY(),i=(null!=(b=e.navigation)?b:e.swipe)/2,j=aK(()=>d(u),i),k=aK(()=>d(v),i),l=aG(a=>{switch(a.key){case"Escape":d(x);break;case"ArrowLeft":(c?h:f)||(c?k:j)();break;case"ArrowRight":(c?f:h)||(c?j:k)()}});g.useEffect(()=>a(D,l),[a,l])}(i),g.createElement(g.Fragment,null,a?a():g.createElement(aZ,{label:"Previous",action:u,icon:ax,renderIcon:c,style:e.navigationPrev,disabled:j,onClick:f}),b?b():g.createElement(aZ,{label:"Next",action:v,icon:ay,renderIcon:d,style:e.navigationNext,disabled:k,onClick:h}))}),a_=M("no_scroll"),a0=M(s);function a1(a,b,c){let d=window.getComputedStyle(a),e=c?"padding-left":"padding-right",f=c?d.paddingLeft:d.paddingRight,g=a.style.getPropertyValue(e);return a.style.setProperty(e,`${(V(f)||0)+b}px`),()=>{g?a.style.setProperty(e,g):a.style.removeProperty(e)}}let a2=_("no-scroll",function({noScroll:{disabled:a},children:b}){let c=aJ(),{getOwnerDocument:d,getOwnerWindow:e}=ad();return g.useEffect(()=>{if(a)return()=>{};let b=[],f=e(),{body:g,documentElement:h}=d(),i=Math.round(f.innerWidth-h.clientWidth);if(i>0){b.push(a1(g,i,c));let a=g.getElementsByTagName("*");for(let d=0;d<a.length;d+=1){let e=a[d];"style"in e&&"fixed"===f.getComputedStyle(e).getPropertyValue("position")&&!e.classList.contains(a0)&&b.push(a1(e,i,c))}}return g.classList.add(a_),()=>{g.classList.remove(a_),b.forEach(a=>a())}},[c,a,d,e]),g.createElement(g.Fragment,null,b)});function a3(a,b,c){let d=a.getAttribute(b);return a.setAttribute(b,c),()=>{d?a.setAttribute(b,d):a.removeAttribute(b)}}let a4=_(k,function({children:a,animation:b,styles:c,className:d,on:e,portal:f,close:h}){let[i,j]=g.useState(!1),[l,m]=g.useState(!1),n=g.useRef([]),o=g.useRef(null),{setTimeout:p}=as(),{subscribe:q}=ag(),r=aC()?0:b.fade;g.useEffect(()=>(j(!0),()=>{j(!1),m(!1)}),[]);let t=aG(()=>{n.current.forEach(a=>a()),n.current=[]}),u=aG(()=>{var a;m(!1),t(),null==(a=e.exiting)||a.call(e),p(()=>{var a;null==(a=e.exited)||a.call(e),h()},r)});g.useEffect(()=>q(x,u),[q,u]);let v=aG(a=>{var b,c,d;a.scrollTop,m(!0),null==(b=e.entering)||b.call(e);let f=null!=(d=null==(c=a.parentNode)?void 0:c.children)?d:[];for(let b=0;b<f.length;b+=1){let c=f[b];-1===["TEMPLATE","SCRIPT","STYLE"].indexOf(c.tagName)&&c!==a&&(n.current.push(a3(c,"inert","")),n.current.push(a3(c,"aria-hidden","true")))}n.current.push(()=>{var a,b;null==(b=null==(a=o.current)?void 0:a.focus)||b.call(a)}),p(()=>{var a;null==(a=e.entered)||a.call(e)},r)}),w=g.useCallback(a=>{a?v(a):t()},[v,t]);return i?(0,J.createPortal)(g.createElement(aO,{ref:w,className:L(d,M(O(k,void 0)),M(s),l&&M(O(k,"open"))),role:"presentation","aria-live":"polite",style:{...b.fade!==$.animation.fade?{[N("fade_animation_duration")]:`${r}ms`}:null,...b.easing.fade!==$.animation.easing.fade?{[N("fade_animation_timing_function")]:b.easing.fade}:null,...c.root},onFocus:a=>{o.current||(o.current=a.relatedTarget)}},a),f.root||document.body):null}),a5=_("root",function({children:a}){return g.createElement(g.Fragment,null,a)}),a6=_(l,function({toolbar:{buttons:a},render:{buttonClose:b,iconClose:c},styles:d}){let{close:e,setToolbarWidth:f}=aS(),{setContainerRef:h,containerRect:i}=aE();return aB(()=>{f(null==i?void 0:i.width)},[f,null==i?void 0:i.width]),g.createElement("div",{ref:h,style:d.toolbar,className:M(O(l,void 0))},null==a?void 0:a.map(a=>a===x?b?b():g.createElement(au,{key:x,label:"Close",icon:aw,renderIcon:c,onClick:e}):a))});function a7({carousel:a,animation:b,render:c,toolbar:d,controller:e,noScroll:f,on:h,plugins:i,slides:k,index:l,...m}){let{animation:n,carousel:o,render:p,toolbar:q,controller:r,noScroll:s,on:t,slides:u,index:v,plugins:w,...x}=$,{config:y,augmentation:z}=function(a,b=[],c=[]){let d=a,e=a=>{let b=[...d];for(;b.length>0;){let c=b.pop();if((null==c?void 0:c.module.name)===a)return!0;(null==c?void 0:c.children)&&b.push(...c.children)}return!1},f=(a,b)=>{if(""===a){d=[aa(b,d)];return}d=ab(d,a,a=>[aa(b,[a])])},g=(a,b)=>{d=ab(d,a,a=>[aa(a.module,[aa(b,a.children)])])},h=(a,b,c)=>{d=ab(d,a,a=>{var d;return[aa(a.module,[...c?[aa(b)]:[],...null!=(d=a.children)?d:[],...c?[]:[aa(b)]])]})},i=(a,b,c)=>{d=ab(d,a,a=>[...c?[aa(b)]:[],a,...c?[]:[aa(b)]])},k=a=>{g(j,a)},l=(a,b)=>{d=ab(d,a,a=>[aa(b,a.children)])},m=a=>{d=ab(d,a,a=>a.children)},n=a=>{c.push(a)};return b.forEach(a=>{a({contains:e,addParent:f,append:g,addChild:h,addSibling:i,addModule:k,replace:l,remove:m,augment:n})}),{config:d,augmentation:a=>c.reduce((a,b)=>b(a),a)}}([aa(a4,[aa(a2,[aa(aT,[aa(aX),aa(a6),aa(a$)])])])],i||w),A=z({animation:function(a,b={}){let{easing:c,...d}=a,{easing:e,...f}=b;return{easing:{...c,...e},...d,...f}}(n,b),carousel:{...o,...a},render:{...p,...c},toolbar:{...q,...d},controller:{...r,...e},noScroll:{...s,...f},on:{...t,...h},...x,...m});return A.open?g.createElement(ak,{...A},g.createElement(aq,{slides:k||u,index:V(l||v)},g.createElement(at,null,g.createElement(ah,null,function a(b,c){var d;return g.createElement(b.module.component,{key:b.module.name,...c},null==(d=b.children)?void 0:d.map(b=>a(b,c)))}(aa(a5,y),A))))):null}let a8={maxZoomPixelRatio:1,zoomInMultiplier:2,doubleTapDelay:300,doubleClickDelay:500,doubleClickMaxStops:2,keyboardMoveDistance:50,wheelZoomDistanceFactor:100,pinchZoomDistanceFactor:100,scrollToZoom:!1},a9=a=>({...a8,...a});function ba(){let{zoom:a}=aj();return a9(a)}function bb(a,b){return((a.clientX-b.clientX)**2+(a.clientY-b.clientY)**2)**.5}function bc(a,b,c=100,d=2){return a*Math.min(1+Math.abs(b/c),d)**Math.sign(b)}let bd=g.createContext(null),be=R("useZoom","ZoomControllerContext",bd);function bf({children:a}){let[b,c]=g.useState(),{slideRect:d}=aS(),{imageRect:e,maxZoom:f}=function(a,b){var c,d;let e={width:0,height:0},f={width:0,height:0},{currentSlide:g}=am(),{imageFit:h}=aj().carousel,{maxZoomPixelRatio:i}=ba();if(a&&g){let j={...g,...b};if(T(j)){let b=U(j,h),g=Math.max(...((null==(c=j.srcSet)?void 0:c.map(a=>a.width))||[]).concat(j.width?[j.width]:[])),k=Math.max(...((null==(d=j.srcSet)?void 0:d.map(a=>a.height))||[]).concat(j.height?[j.height]:[]));g>0&&k>0&&a.width>0&&a.height>0&&(f={width:(f=b?{width:Math.round(Math.min(g,a.width/a.height*k)),height:Math.round(Math.min(k,a.height/a.width*g))}:{width:g,height:k}).width*i,height:f.height*i},e=b?{width:Math.min(a.width,f.width,g),height:Math.min(a.height,f.height,k)}:{width:Math.round(Math.min(a.width,a.height/k*g,g)),height:Math.round(Math.min(a.height,a.width/g*k,k))})}}let j=e.width?Math.max(S(f.width/e.width,5),1):1;return{imageRect:e,maxZoom:j}}(d,null==b?void 0:b.imageDimensions),{zoom:h,offsetX:i,offsetY:j,disabled:k,changeZoom:l,changeOffsets:m,zoomIn:n,zoomOut:o}=function(a,b,c){let[d,e]=g.useState(1),[f,h]=g.useState(0),[i,j]=g.useState(0),k=function(a,b,c,d){let e=g.useRef(),f=g.useRef(),{zoom:h}=aj().animation,i=aC(),j=aG(()=>{var g,j,k;if(null==(g=e.current)||g.cancel(),e.current=void 0,f.current&&(null==d?void 0:d.current)){try{e.current=null==(k=(j=d.current).animate)?void 0:k.call(j,[{transform:f.current},{transform:`scale(${a}) translateX(${b}px) translateY(${c}px)`}],{duration:i?0:null!=h?h:500,easing:e.current?"ease-out":"ease-in-out"})}catch(a){console.error(a)}f.current=void 0,e.current&&(e.current.onfinish=()=>{e.current=void 0})}});return aB(j,[a,b,c,j]),g.useCallback(()=>{f.current=(null==d?void 0:d.current)?window.getComputedStyle(d.current).transform:void 0},[d])}(d,f,i,c),{currentSlide:l,globalIndex:m}=am(),{containerRect:n,slideRect:o}=aS(),{zoomInMultiplier:p}=ba(),q=l&&T(l)?l.src:void 0,r=!q||!(null==c?void 0:c.current);aB(()=>{e(1),h(0),j(0)},[m,q]);let s=g.useCallback((b,c,e)=>{let g=e||d,k=f-(b||0),l=i-(c||0),m=(a.width*g-o.width)/2/g,n=(a.height*g-o.height)/2/g;h(Math.min(Math.abs(k),Math.max(m,0))*Math.sign(k)),j(Math.min(Math.abs(l),Math.max(n,0))*Math.sign(l))},[d,f,i,o,a.width,a.height]),t=g.useCallback((a,c,f,g)=>{let h=S(Math.min(Math.max(a+.001<b?a:b,1),b),5);h!==d&&(c||k(),s(f?f*(1/d-1/h):0,g?g*(1/d-1/h):0,h),e(h))},[d,b,s,k]),u=aG(()=>{d>1&&(d>b&&t(b,!0),s())});aB(u,[n.width,n.height,u]);let v=g.useCallback(()=>t(d*p),[d,p,t]),w=g.useCallback(()=>t(d/p),[d,p,t]);return{zoom:d,offsetX:f,offsetY:i,disabled:r,changeOffsets:s,changeZoom:t,zoomIn:v,zoomOut:w}}(e,f,null==b?void 0:b.zoomWrapperRef),{on:p}=aj(),q=aG(()=>{var a;k||null==(a=p.zoom)||a.call(p,{zoom:h})});g.useEffect(q,[h,q]),function(a,b,c,d,e,f){let h=g.useRef([]),i=g.useRef(0),j=g.useRef(),{globalIndex:k}=am(),{getOwnerWindow:l}=ad(),{containerRef:m,subscribeSensors:n}=aS(),{keyboardMoveDistance:o,zoomInMultiplier:p,wheelZoomDistanceFactor:q,scrollToZoom:r,doubleTapDelay:s,doubleClickDelay:t,doubleClickMaxStops:u,pinchZoomDistanceFactor:v}=ba(),w=g.useCallback(a=>{if(m.current){let{pageX:b,pageY:c}=a,{scrollX:d,scrollY:e}=l(),{left:f,top:g,width:h,height:i}=m.current.getBoundingClientRect();return[b-f-d-h/2,c-g-e-i/2]}return[]},[m,l]),x=aG(b=>{let c=()=>{b.preventDefault(),b.stopPropagation()};if(a>1){let a=(a,b)=>{c(),e(a,b)};"ArrowDown"===b.key?a(0,o):"ArrowUp"===b.key?a(0,-o):"ArrowLeft"===b.key?a(-o,0):"ArrowRight"===b.key&&a(o,0)}let f=a=>{c(),d(a)},g=()=>b.getModifierState("Meta");"+"===b.key||"="===b.key&&g()?f(a*p):"-"===b.key||"_"===b.key&&g()?f(a/p):"0"===b.key&&g()&&f(1)}),y=aG(b=>{if((b.ctrlKey||r)&&Math.abs(b.deltaY)>Math.abs(b.deltaX)){b.stopPropagation(),d(bc(a,-b.deltaY,q),!0,...w(b));return}a>1&&(b.stopPropagation(),r||e(b.deltaX,b.deltaY))}),z=g.useCallback(a=>{let b=h.current;b.splice(0,b.length,...b.filter(b=>b.pointerId!==a.pointerId))},[]),A=g.useCallback(a=>{z(a),a.persist(),h.current.push(a)},[z]),B=aG(c=>{var e;let g=h.current;if("mouse"===c.pointerType&&c.buttons>1||!(null==(e=null==f?void 0:f.current)?void 0:e.contains(c.target)))return;a>1&&c.stopPropagation();let{timeStamp:k}=c;0===g.length&&k-i.current<("touch"===c.pointerType?s:t)?(i.current=0,d(a!==b?a*Math.max(b**(1/u),p):1,!1,...w(c))):i.current=k,A(c),2===g.length&&(j.current=bb(g[0],g[1]))}),C=aG(b=>{let c=h.current,f=c.find(a=>a.pointerId===b.pointerId);if(2===c.length&&j.current){b.stopPropagation(),A(b);let e=bb(c[0],c[1]),f=e-j.current;Math.abs(f)>0&&(d(bc(a,f,v),!0,...c.map(a=>w(a)).reduce((a,b)=>b.map((b,c)=>a[c]+b/2))),j.current=e);return}a>1&&(b.stopPropagation(),f&&(1===c.length&&e((f.clientX-b.clientX)/a,(f.clientY-b.clientY)/a),A(b)))}),F=g.useCallback(a=>{let b=h.current;2===b.length&&b.find(b=>b.pointerId===a.pointerId)&&(j.current=void 0),z(a)},[z]),G=g.useCallback(()=>{let a=h.current;a.splice(0,a.length),i.current=0,j.current=void 0},[]);aP(n,B,C,F,c),g.useEffect(G,[k,G]),g.useEffect(()=>c?()=>{}:Q(G,n(D,x),n(E,y)),[c,n,G,x,y])}(h,f,k,l,m,null==b?void 0:b.zoomWrapperRef);let r=g.useMemo(()=>({zoom:h,maxZoom:f,offsetX:i,offsetY:j,disabled:k,zoomIn:n,zoomOut:o,changeZoom:l}),[h,f,i,j,k,n,o,l]);g.useImperativeHandle(ba().ref,()=>r,[r]);let s=g.useMemo(()=>({...r,setZoomWrapper:c}),[r,c]);return g.createElement(bd.Provider,{value:s},a)}let bg=av("ZoomIn",g.createElement(g.Fragment,null,g.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),g.createElement("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"}))),bh=av("ZoomOut",g.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})),bi=g.forwardRef(function({zoomIn:a,onLoseFocus:b},c){let d=g.useRef(!1),e=g.useRef(!1),{zoom:f,maxZoom:h,zoomIn:i,zoomOut:j,disabled:k}=be(),{render:l}=aj(),m=k||(a?f>=h:f<=1);return g.useEffect(()=>{m&&d.current&&e.current&&b(),m||(d.current=!0)},[m,b]),g.createElement(au,{ref:c,disabled:m,label:a?"Zoom in":"Zoom out",icon:a?bg:bh,renderIcon:a?l.iconZoomIn:l.iconZoomOut,onClick:a?i:j,onFocus:()=>{e.current=!0},onBlur:()=>{e.current=!1}})});function bj(){let a=g.useRef(null),b=g.useRef(null),{focus:c}=aS(),d=g.useCallback(a=>{var b,d;(null==(b=a.current)?void 0:b.disabled)?c():null==(d=a.current)||d.focus()},[c]),e=g.useCallback(()=>d(a),[d]),f=g.useCallback(()=>d(b),[d]);return g.createElement(g.Fragment,null,g.createElement(bi,{zoomIn:!0,ref:a,onLoseFocus:f}),g.createElement(bi,{ref:b,onLoseFocus:e}))}function bk(){let{render:a}=aj(),b=be();return a.buttonZoom?g.createElement(g.Fragment,null,a.buttonZoom(b)):g.createElement(bj,null)}function bl({current:a,preload:b},{type:c,source:d}){switch(c){case"fetch":if(!a)return{current:d};return{current:a,preload:d};case"done":if(d===b)return{current:d};return{current:a,preload:b};default:throw Error(I)}}function bm(a){var b,c;let[{current:d,preload:e},f]=g.useReducer(bl,{}),{slide:h,rect:i,imageFit:j,render:k,interactive:l}=a,m=h.srcSet.sort((a,b)=>a.width-b.width),n=null!=(b=h.width)?b:m[m.length-1].width,o=null!=(c=h.height)?c:m[m.length-1].height,p=U(h,j),q=Math.max(...m.map(a=>a.width)),r=Math.min((p?Math.max:Math.min)(i.width,n*(i.height/o)),q),s=aG(()=>{var a;let b=null!=(a=m.find(a=>a.width>=+r))?a:m[m.length-1];(!d||m.findIndex(a=>a.src===d)<m.findIndex(a=>a===b))&&f({type:"fetch",source:b.src})});aB(s,[i.width,i.height,1,s]);let t=aG(a=>f({type:"done",source:a})),u={WebkitTransform:l?"initial":"translateZ(0)"};return p||Object.assign(u,i.width/i.height<n/o?{width:"100%",height:"auto"}:{width:"auto",height:"100%"}),g.createElement(g.Fragment,null,e&&e!==d&&g.createElement(aN,{key:"preload",...a,slide:{...h,src:e,srcSet:void 0},style:{position:"absolute",visibility:"hidden",...u},onLoad:()=>t(e),render:{...k,iconLoading:()=>null,iconError:()=>null}}),d&&g.createElement(aN,{key:"current",...a,slide:{...h,src:d,srcSet:void 0},style:u}))}function bn({render:a,slide:b,offset:c,rect:d}){var e,f;let[h,i]=g.useState(),j=g.useRef(null),{zoom:k,maxZoom:l,offsetX:m,offsetY:n,setZoomWrapper:o}=be(),p=k>1,{carousel:q,on:s}=aj(),{currentIndex:u}=am();aB(()=>0===c?(o({zoomWrapperRef:j,imageDimensions:h}),()=>o(void 0)):()=>{},[c,h,o]);let v=null==(e=a.slide)?void 0:e.call(a,{slide:b,offset:c,rect:d,zoom:k,maxZoom:l});if(!v&&T(b)){let e={slide:b,offset:c,rect:d,render:a,imageFit:q.imageFit,imageProps:q.imageProps,onClick:0===c?()=>{var a;return null==(a=s.click)?void 0:a.call(s,{index:u})}:void 0};v=((null==(f=b.srcSet)?void 0:f.length)||0)>0?g.createElement(bm,{...e,slide:b,interactive:p,rect:0===c?{width:d.width*k,height:d.height*k}:d}):g.createElement(aN,{onLoad:a=>i({width:a.naturalWidth,height:a.naturalHeight}),...e})}return v?g.createElement("div",{ref:j,className:L(M("fullsize"),M(r),M(t),p&&M("slide_wrapper_interactive")),style:0===c?{transform:`scale(${k}) translateX(${m}px) translateY(${n}px)`}:void 0},v):null}let bo=({augment:a,addModule:b})=>{a(({zoom:a,toolbar:b,render:c,controller:d,...e})=>{let f=a9(a);return{zoom:f,toolbar:function(a,b,c){if(!c)return a;let{buttons:d,...e}=a,f=d.findIndex(a=>a===b),h=g.isValidElement(c)?g.cloneElement(c,{key:b},null):c;if(f>=0){let a=[...d];return a.splice(f,1,h),{buttons:a,...e}}return{buttons:[h,...d],...e}}(b,m,g.createElement(bk,null)),render:{...c,slide:a=>{var b;return T(a.slide)?g.createElement(bn,{render:c,...a}):null==(b=c.slide)?void 0:b.call(c,a)}},controller:{...d,preventDefaultWheelY:f.scrollToZoom},...e}}),b(_(m,bf))};function bp(){let a=(0,h.c3)("Conifer"),[b,c]=(0,g.useState)({status:!1,key:1}),d=a=>{b.key===a?c({status:!1}):c({status:!0,key:a})},[e,i]=(0,g.useState)(!1),j=[{src:`assets/images/product/${a("images.img1")}`},{src:`assets/images/product/${a("images.img2")}`},{src:`assets/images/product/${a("images.img3")}`},{src:`assets/images/product/${a("images.img4")}`},{src:`assets/images/product/${a("images.img5")}`},{src:`assets/images/product/${a("images.img6")}`},{src:`assets/images/product/${a("images.img7")}`},{src:`assets/images/product/${a("images.img8")}`},{src:`assets/images/product/${a("images.img9")}`},{src:`assets/images/product/${a("images.img10")}`},{src:`assets/images/product/${a("images.img11")}`},{src:`assets/images/product/${a("images.img12")}`}],k=a=>{i(!0)};return(0,f.jsx)("div",{className:"container",children:(0,f.jsxs)("div",{className:"row",children:[(0,f.jsx)("div",{className:"col-xl-6 d-flex justify-content-center",style:{padding:0},children:(0,f.jsx)("div",{className:"testimonial-one__content",children:(0,f.jsxs)("div",{className:"faq-one__content-faq conifer-faq",children:[(0,f.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,f.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,f.jsxs)("span",{children:[" ",a("title1")," "]})," ",(0,f.jsxs)("span",{children:[" ",a("title2")," "]})," ",a("title3")]})}),(0,f.jsxs)("div",{className:"accrodion-grp faq-one-accrodion","data-grp-name":"faq-one-accrodion-1",children:[(0,f.jsxs)("div",{className:1==b.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq1.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq1.ans")})})})]}),(0,f.jsxs)("div",{className:2==b.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq2.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq2.ans")})})})]}),(0,f.jsxs)("div",{className:3==b.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq3.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq3.ans")})})})]}),(0,f.jsxs)("div",{className:4==b.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq4.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq4.ans")})})})]}),(0,f.jsxs)("div",{className:5==b.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq5.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq5.ans")})})})]})]})]})})}),(0,f.jsx)("div",{className:"col-xl-6  d-flex justify-content-center",children:(0,f.jsx)("div",{className:"product-one__img",children:(0,f.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,f.jsx)("button",{className:"conifer-btn",onClick:()=>k(0),children:(0,f.jsxs)("a",{children:["Click to View ",(0,f.jsx)("i",{className:"fas fa-expand"})]})}),(0,f.jsx)("img",{onClick:()=>k(0),style:{cursor:"pointer"},src:j[0].src,alt:"Nortern Conifer Wood"}),(0,f.jsx)(a7,{open:e,close:()=>i(!1),slides:j,plugins:[bo],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function bq(){let a=(0,h.c3)("Bamboo"),[b,c]=(0,g.useState)({status:!1,key:1}),d=a=>{b.key===a?c({status:!1}):c({status:!0,key:a})},[e,i]=(0,g.useState)(!1),j=[{src:`assets/images/product/${a("images.img1")}`},{src:`assets/images/product/${a("images.img2")}`},{src:`assets/images/product/${a("images.img3")}`},{src:`assets/images/product/${a("images.img4")}`}],k=a=>{i(!0)};return(0,f.jsx)("div",{className:"container",children:(0,f.jsxs)("div",{className:"row",children:[(0,f.jsx)("div",{className:"col-xl-6",style:{padding:0},children:(0,f.jsx)("div",{className:"testimonial-one__content",children:(0,f.jsxs)("div",{className:"faq-one__content-faq bamboo-faq",children:[(0,f.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,f.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,f.jsx)("span",{style:{color:"#445539"},children:a("title1")})," ",a("title2"),a("title3")]})}),(0,f.jsxs)("div",{className:"accrodion-grp bamboo-one-accrodion","data-grp-name":"bamboo-one-accrodion-1",children:[(0,f.jsxs)("div",{className:1==b.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq1.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq1.ans")})})})]}),(0,f.jsxs)("div",{className:2==b.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq2.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq2.ans")})})})]}),(0,f.jsxs)("div",{className:3==b.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq3.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq3.ans")})})})]}),(0,f.jsxs)("div",{className:4==b.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq4.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq4.ans")})})})]}),(0,f.jsxs)("div",{className:5==b.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq5.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq5.ans")})})})]})]})]})})}),(0,f.jsx)("div",{className:"col-xl-6",children:(0,f.jsx)("div",{className:"product-one__img",children:(0,f.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,f.jsx)("button",{className:"bamboo-btn",onClick:()=>k(0),children:(0,f.jsxs)("a",{children:["Click to View ",(0,f.jsx)("i",{className:"fas fa-expand"})]})}),(0,f.jsx)("img",{onClick:()=>k(0),style:{cursor:"pointer"},src:j[0].src,alt:"Nortern Conifer Wood"}),(0,f.jsx)(a7,{open:e,close:()=>i(!1),slides:j,plugins:[bo],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function br(){let a=(0,h.c3)("Teak"),[b,c]=(0,g.useState)({status:!1,key:1}),d=a=>{b.key===a?c({status:!1}):c({status:!0,key:a})},[e,i]=(0,g.useState)(!1),j=[{src:`assets/images/product/${a("images.img1")}`},{src:`assets/images/product/${a("images.img2")}`},{src:`assets/images/product/${a("images.img3")}`},{src:`assets/images/product/${a("images.img4")}`},{src:`assets/images/product/${a("images.img5")}`},{src:`assets/images/product/${a("images.img6")}`},{src:`assets/images/product/${a("images.img7")}`},{src:`assets/images/product/${a("images.img8")}`},{src:`assets/images/product/${a("images.img9")}`},{src:`assets/images/product/${a("images.img10")}`},{src:`assets/images/product/${a("images.img11")}`},{src:`assets/images/product/${a("images.img12")}`}],k=a=>{i(!0)};return(0,f.jsx)("div",{className:"container",children:(0,f.jsxs)("div",{className:"row",children:[(0,f.jsx)("div",{className:"col-xl-6 ",style:{padding:0},children:(0,f.jsx)("div",{className:"testimonial-one__content",children:(0,f.jsxs)("div",{className:"faq-one__content-faq teak-faq",children:[(0,f.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,f.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,f.jsx)("span",{style:{color:"#BA9B7F"},children:a("title1")})," ",a("title2")," ",a("title3")]})}),(0,f.jsxs)("div",{className:"accrodion-grp teak-one-accrodion","data-grp-name":"teak-one-accrodion-1",children:[(0,f.jsxs)("div",{className:1==b.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq1.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq1.ans")})})})]}),(0,f.jsxs)("div",{className:2==b.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq2.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq2.ans")})})})]}),(0,f.jsxs)("div",{className:3==b.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq3.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq3.ans")})})})]}),(0,f.jsxs)("div",{className:4==b.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq4.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq4.ans")})})})]}),(0,f.jsxs)("div",{className:5==b.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,f.jsx)("div",{className:"accrodion-title",children:(0,f.jsx)("h4",{children:a("faq5.que")})}),(0,f.jsx)("div",{className:"accrodion-content",children:(0,f.jsx)("div",{className:"inner",children:(0,f.jsx)("p",{children:a("faq5.ans")})})})]})]})]})})}),(0,f.jsx)("div",{className:"col-xl-6",children:(0,f.jsx)("div",{className:"product-one__img",children:(0,f.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,f.jsx)("button",{className:"teak-btn",onClick:()=>k(0),children:(0,f.jsxs)("a",{children:["Click to View ",(0,f.jsx)("i",{className:"fas fa-expand"})]})}),(0,f.jsx)("img",{onClick:()=>k(0),style:{cursor:"pointer"},src:j[0].src,alt:"Thai teak"}),(0,f.jsx)(a7,{open:e,close:()=>i(!1),slides:j,plugins:[bo],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function bs(){let a=(0,h.c3)("ProductArea"),b=(0,h.c3)("ProductNavTab"),[c,d]=(0,g.useState)("conifer"),[e,i]=(0,g.useState)("conifer"),j=a=>{d(a)},k=a=>{i(e===a?null:a)};return(0,f.jsx)(f.Fragment,{children:(0,f.jsxs)("section",{className:"product-one",id:"product",children:[(0,f.jsx)("div",{className:"testimonial-one__pattern",style:{backgroundImage:"url(assets/images/pattern/products-v2-pattern.png)"}}),(0,f.jsxs)("div",{className:"container",children:[(0,f.jsx)("div",{className:"row",children:(0,f.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,f.jsxs)("div",{className:"sec-title__tagline",children:[(0,f.jsx)("div",{className:"line"}),(0,f.jsx)("div",{className:"text tg-element-title",children:(0,f.jsx)("h4",{children:a("title")})}),(0,f.jsx)("div",{className:"line2"})]}),(0,f.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[a("sub_title1"),(0,f.jsx)("br",{})," ",(0,f.jsx)("span",{children:a("sub_title2")})]})]})}),(0,f.jsxs)("div",{className:"row",children:[(0,f.jsx)("div",{className:"container",children:(0,f.jsx)("div",{className:"row",children:(0,f.jsx)("div",{className:"col-xl-6",children:(0,f.jsx)("div",{className:"product-menu",children:(0,f.jsxs)("ul",{className:"nav nav-tabs nav-fill",children:[(0,f.jsx)("li",{className:"conifer-menu nav-item",children:(0,f.jsx)("button",{className:`nav-link ${"conifer"===c?"active":""}`,onClick:()=>j("conifer"),children:b("conifer")})}),(0,f.jsx)("li",{className:"bamboo-menu nav-item",children:(0,f.jsx)("button",{className:`nav-link ${"bamboo"===c?"active":""}`,onClick:()=>j("bamboo"),children:b("bamboo")})}),(0,f.jsx)("li",{className:"teak-menu nav-item",children:(0,f.jsx)("button",{className:`nav-link ${"teak"===c?"active":""}`,onClick:()=>j("teak"),children:b("teak")})})]})})})})}),(0,f.jsxs)("div",{className:"product-content",children:["conifer"===c&&(0,f.jsx)(bp,{}),"bamboo"===c&&(0,f.jsx)(bq,{}),"teak"===c&&(0,f.jsx)(br,{})]}),(0,f.jsxs)("div",{className:"accordion",children:[(0,f.jsxs)("div",{className:"accordion-item conifer",children:[(0,f.jsx)("div",{className:"conifer"===e?"accordion-conifer active":"accordion-conifer",onClick:()=>k("conifer"),children:(0,f.jsx)("h5",{children:b("conifer")})}),(0,f.jsx)("div",{className:`accordion-content ${"conifer"===e?"active":""}`,children:(0,f.jsx)(bp,{})})]}),(0,f.jsxs)("div",{className:"accordion-item bamboo",children:[(0,f.jsx)("div",{className:"bamboo"===e?"accordion-bamboo active":"accordion-bamboo",onClick:()=>k("bamboo"),children:(0,f.jsx)("h5",{children:b("bamboo")})}),(0,f.jsx)("div",{className:`accordion-content ${"bamboo"===e?"active":""}`,children:(0,f.jsx)(bq,{})})]}),(0,f.jsxs)("div",{className:"accordion-item teak",children:[(0,f.jsx)("div",{className:"teak"===e?"accordion-teak active":"accordion-teak",onClick:()=>k("teak"),children:(0,f.jsx)("h5",{children:b("teak")})}),(0,f.jsx)("div",{className:`accordion-content ${"teak"===e?"active":""}`,children:(0,f.jsx)(br,{})})]})]})]})]})]})})}c(85129),c(89112)},25208:()=>{},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32036:(a,b,c)=>{"use strict";let d,e,f;c.d(b,{RC:()=>P,qr:()=>Q});var g=c(43210),h=c(49863),i=c(33544);function j(){return d||(d=function(){let a=(0,h.a)(),b=(0,h.g)();return{smoothScroll:b.documentElement&&b.documentElement.style&&"scrollBehavior"in b.documentElement.style,touch:!!("ontouchstart"in a||a.DocumentTouch&&b instanceof a.DocumentTouch)}}()),d}let k=(a,b)=>{if(!a||a.destroyed||!a.params)return;let c=b.closest(a.isElement?"swiper-slide":`.${a.params.slideClass}`);if(c){let b=c.querySelector(`.${a.params.lazyPreloaderClass}`);!b&&a.isElement&&(c.shadowRoot?b=c.shadowRoot.querySelector(`.${a.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{c.shadowRoot&&(b=c.shadowRoot.querySelector(`.${a.params.lazyPreloaderClass}`))&&b.remove()})),b&&b.remove()}},l=(a,b)=>{if(!a.slides[b])return;let c=a.slides[b].querySelector('[loading="lazy"]');c&&c.removeAttribute("loading")},m=a=>{if(!a||a.destroyed||!a.params)return;let b=a.params.lazyPreloadPrevNext,c=a.slides.length;if(!c||!b||b<0)return;b=Math.min(b,c);let d="auto"===a.params.slidesPerView?a.slidesPerViewDynamic():Math.ceil(a.params.slidesPerView),e=a.activeIndex;if(a.params.grid&&a.params.grid.rows>1){let c=[e-b];c.push(...Array.from({length:b}).map((a,b)=>e+d+b)),a.slides.forEach((b,d)=>{c.includes(b.column)&&l(a,d)});return}let f=e+d-1;if(a.params.rewind||a.params.loop)for(let d=e-b;d<=f+b;d+=1){let b=(d%c+c)%c;(b<e||b>f)&&l(a,b)}else for(let d=Math.max(e-b,0);d<=Math.min(f+b,c-1);d+=1)d!==e&&(d>f||d<e)&&l(a,d)};function n(a){let{swiper:b,runCallbacks:c,direction:d,step:e}=a,{activeIndex:f,previousIndex:g}=b,h=d;if(h||(h=f>g?"next":f<g?"prev":"reset"),b.emit(`transition${e}`),c&&f!==g){if("reset"===h)return void b.emit(`slideResetTransition${e}`);b.emit(`slideChangeTransition${e}`),"next"===h?b.emit(`slideNextTransition${e}`):b.emit(`slidePrevTransition${e}`)}}function o(a){let b=(0,h.g)(),c=(0,h.a)(),d=this.touchEventsData;d.evCache.push(a);let{params:e,touches:f,enabled:g}=this;if(!g||!e.simulateTouch&&"mouse"===a.pointerType||this.animating&&e.preventInteractionOnTransition)return;!this.animating&&e.cssMode&&e.loop&&this.loopFix();let j=a;j.originalEvent&&(j=j.originalEvent);let k=j.target;if("wrapper"===e.touchEventsTarget&&!this.wrapperEl.contains(k)||"which"in j&&3===j.which||"button"in j&&j.button>0||d.isTouched&&d.isMoved)return;let l=!!e.noSwipingClass&&""!==e.noSwipingClass,m=a.composedPath?a.composedPath():a.path;l&&j.target&&j.target.shadowRoot&&m&&(k=m[0]);let n=e.noSwipingSelector?e.noSwipingSelector:`.${e.noSwipingClass}`,o=!!(j.target&&j.target.shadowRoot);if(e.noSwiping&&(o?function(a,b){return void 0===b&&(b=this),function b(c){if(!c||c===(0,h.g)()||c===(0,h.a)())return null;c.assignedSlot&&(c=c.assignedSlot);let d=c.closest(a);return d||c.getRootNode?d||b(c.getRootNode().host):null}(b)}(n,k):k.closest(n))){this.allowClick=!0;return}if(e.swipeHandler&&!k.closest(e.swipeHandler))return;f.currentX=j.pageX,f.currentY=j.pageY;let p=f.currentX,q=f.currentY,r=e.edgeSwipeDetection||e.iOSEdgeSwipeDetection,s=e.edgeSwipeThreshold||e.iOSEdgeSwipeThreshold;if(r&&(p<=s||p>=c.innerWidth-s))if("prevent"!==r)return;else a.preventDefault();Object.assign(d,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),f.startX=p,f.startY=q,d.touchStartTime=(0,i.d)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,e.threshold>0&&(d.allowThresholdMove=!1);let t=!0;k.matches(d.focusableElements)&&(t=!1,"SELECT"===k.nodeName&&(d.isTouched=!1)),b.activeElement&&b.activeElement.matches(d.focusableElements)&&b.activeElement!==k&&b.activeElement.blur();let u=t&&this.allowTouchMove&&e.touchStartPreventDefault;(e.touchStartForcePreventDefault||u)&&!k.isContentEditable&&j.preventDefault(),e.freeMode&&e.freeMode.enabled&&this.freeMode&&this.animating&&!e.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",j)}function p(a){let b,c=(0,h.g)(),d=this.touchEventsData,{params:e,touches:f,rtlTranslate:g,enabled:j}=this;if(!j||!e.simulateTouch&&"mouse"===a.pointerType)return;let k=a;if(k.originalEvent&&(k=k.originalEvent),!d.isTouched){d.startMoving&&d.isScrolling&&this.emit("touchMoveOpposite",k);return}let l=d.evCache.findIndex(a=>a.pointerId===k.pointerId);l>=0&&(d.evCache[l]=k);let m=d.evCache.length>1?d.evCache[0]:k,n=m.pageX,o=m.pageY;if(k.preventedByNestedSwiper){f.startX=n,f.startY=o;return}if(!this.allowTouchMove){k.target.matches(d.focusableElements)||(this.allowClick=!1),d.isTouched&&(Object.assign(f,{startX:n,startY:o,prevX:this.touches.currentX,prevY:this.touches.currentY,currentX:n,currentY:o}),d.touchStartTime=(0,i.d)());return}if(e.touchReleaseOnEdges&&!e.loop){if(this.isVertical()){if(o<f.startY&&this.translate<=this.maxTranslate()||o>f.startY&&this.translate>=this.minTranslate()){d.isTouched=!1,d.isMoved=!1;return}}else if(n<f.startX&&this.translate<=this.maxTranslate()||n>f.startX&&this.translate>=this.minTranslate())return}if(c.activeElement&&k.target===c.activeElement&&k.target.matches(d.focusableElements)){d.isMoved=!0,this.allowClick=!1;return}if(d.allowTouchCallbacks&&this.emit("touchMove",k),k.targetTouches&&k.targetTouches.length>1)return;f.currentX=n,f.currentY=o;let p=f.currentX-f.startX,q=f.currentY-f.startY;if(this.params.threshold&&Math.sqrt(p**2+q**2)<this.params.threshold)return;if(void 0===d.isScrolling){let a;this.isHorizontal()&&f.currentY===f.startY||this.isVertical()&&f.currentX===f.startX?d.isScrolling=!1:p*p+q*q>=25&&(a=180*Math.atan2(Math.abs(q),Math.abs(p))/Math.PI,d.isScrolling=this.isHorizontal()?a>e.touchAngle:90-a>e.touchAngle)}if(d.isScrolling&&this.emit("touchMoveOpposite",k),void 0===d.startMoving&&(f.currentX!==f.startX||f.currentY!==f.startY)&&(d.startMoving=!0),d.isScrolling||this.zoom&&this.params.zoom&&this.params.zoom.enabled&&d.evCache.length>1){d.isTouched=!1;return}if(!d.startMoving)return;this.allowClick=!1,!e.cssMode&&k.cancelable&&k.preventDefault(),e.touchMoveStopPropagation&&!e.nested&&k.stopPropagation();let r=this.isHorizontal()?p:q,s=this.isHorizontal()?f.currentX-f.previousX:f.currentY-f.previousY;e.oneWayMovement&&(r=Math.abs(r)*(g?1:-1),s=Math.abs(s)*(g?1:-1)),f.diff=r,r*=e.touchRatio,g&&(r=-r,s=-s);let t=this.touchesDirection;this.swipeDirection=r>0?"prev":"next",this.touchesDirection=s>0?"prev":"next";let u=this.params.loop&&!e.cssMode,v="next"===this.swipeDirection&&this.allowSlideNext||"prev"===this.swipeDirection&&this.allowSlidePrev;if(!d.isMoved){if(u&&v&&this.loopFix({direction:this.swipeDirection}),d.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let a=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});this.wrapperEl.dispatchEvent(a)}d.allowMomentumBounce=!1,e.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",k)}d.isMoved&&t!==this.touchesDirection&&u&&v&&Math.abs(r)>=1&&(this.loopFix({direction:this.swipeDirection,setTranslate:!0}),b=!0),this.emit("sliderMove",k),d.isMoved=!0,d.currentTranslate=r+d.startTranslate;let w=!0,x=e.resistanceRatio;if(e.touchReleaseOnEdges&&(x=0),r>0?(u&&v&&!b&&d.currentTranslate>(e.centeredSlides?this.minTranslate()-this.size/2:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),d.currentTranslate>this.minTranslate()&&(w=!1,e.resistance&&(d.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+d.startTranslate+r)**x))):r<0&&(u&&v&&!b&&d.currentTranslate<(e.centeredSlides?this.maxTranslate()+this.size/2:this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===e.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(e.slidesPerView,10)))}),d.currentTranslate<this.maxTranslate()&&(w=!1,e.resistance&&(d.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-d.startTranslate-r)**x))),w&&(k.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&d.currentTranslate<d.startTranslate&&(d.currentTranslate=d.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&d.currentTranslate>d.startTranslate&&(d.currentTranslate=d.startTranslate),this.allowSlidePrev||this.allowSlideNext||(d.currentTranslate=d.startTranslate),e.threshold>0)if(Math.abs(r)>e.threshold||d.allowThresholdMove){if(!d.allowThresholdMove){d.allowThresholdMove=!0,f.startX=f.currentX,f.startY=f.currentY,d.currentTranslate=d.startTranslate,f.diff=this.isHorizontal()?f.currentX-f.startX:f.currentY-f.startY;return}}else{d.currentTranslate=d.startTranslate;return}e.followFinger&&!e.cssMode&&((e.freeMode&&e.freeMode.enabled&&this.freeMode||e.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),e.freeMode&&e.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(d.currentTranslate),this.setTranslate(d.currentTranslate))}function q(a){let b,c=this,d=c.touchEventsData,e=d.evCache.findIndex(b=>b.pointerId===a.pointerId);if(e>=0&&d.evCache.splice(e,1),["pointercancel","pointerout","pointerleave","contextmenu"].includes(a.type)&&!(["pointercancel","contextmenu"].includes(a.type)&&(c.browser.isSafari||c.browser.isWebView)))return;let{params:f,touches:g,rtlTranslate:h,slidesGrid:j,enabled:k}=c;if(!k||!f.simulateTouch&&"mouse"===a.pointerType)return;let l=a;if(l.originalEvent&&(l=l.originalEvent),d.allowTouchCallbacks&&c.emit("touchEnd",l),d.allowTouchCallbacks=!1,!d.isTouched){d.isMoved&&f.grabCursor&&c.setGrabCursor(!1),d.isMoved=!1,d.startMoving=!1;return}f.grabCursor&&d.isMoved&&d.isTouched&&(!0===c.allowSlideNext||!0===c.allowSlidePrev)&&c.setGrabCursor(!1);let m=(0,i.d)(),n=m-d.touchStartTime;if(c.allowClick){let a=l.path||l.composedPath&&l.composedPath();c.updateClickedSlide(a&&a[0]||l.target,a),c.emit("tap click",l),n<300&&m-d.lastClickTime<300&&c.emit("doubleTap doubleClick",l)}if(d.lastClickTime=(0,i.d)(),(0,i.n)(()=>{c.destroyed||(c.allowClick=!0)}),!d.isTouched||!d.isMoved||!c.swipeDirection||0===g.diff||d.currentTranslate===d.startTranslate){d.isTouched=!1,d.isMoved=!1,d.startMoving=!1;return}if(d.isTouched=!1,d.isMoved=!1,d.startMoving=!1,b=f.followFinger?h?c.translate:-c.translate:-d.currentTranslate,f.cssMode)return;if(f.freeMode&&f.freeMode.enabled)return void c.freeMode.onTouchEnd({currentPos:b});let o=0,p=c.slidesSizesGrid[0];for(let a=0;a<j.length;a+=a<f.slidesPerGroupSkip?1:f.slidesPerGroup){let c=a<f.slidesPerGroupSkip-1?1:f.slidesPerGroup;void 0!==j[a+c]?b>=j[a]&&b<j[a+c]&&(o=a,p=j[a+c]-j[a]):b>=j[a]&&(o=a,p=j[j.length-1]-j[j.length-2])}let q=null,r=null;f.rewind&&(c.isBeginning?r=f.virtual&&f.virtual.enabled&&c.virtual?c.virtual.slides.length-1:c.slides.length-1:c.isEnd&&(q=0));let s=(b-j[o])/p,t=o<f.slidesPerGroupSkip-1?1:f.slidesPerGroup;if(n>f.longSwipesMs){if(!f.longSwipes)return void c.slideTo(c.activeIndex);"next"===c.swipeDirection&&(s>=f.longSwipesRatio?c.slideTo(f.rewind&&c.isEnd?q:o+t):c.slideTo(o)),"prev"===c.swipeDirection&&(s>1-f.longSwipesRatio?c.slideTo(o+t):null!==r&&s<0&&Math.abs(s)>f.longSwipesRatio?c.slideTo(r):c.slideTo(o))}else{if(!f.shortSwipes)return void c.slideTo(c.activeIndex);c.navigation&&(l.target===c.navigation.nextEl||l.target===c.navigation.prevEl)?l.target===c.navigation.nextEl?c.slideTo(o+t):c.slideTo(o):("next"===c.swipeDirection&&c.slideTo(null!==q?q:o+t),"prev"===c.swipeDirection&&c.slideTo(null!==r?r:o))}}function r(){let a=this,{params:b,el:c}=a;if(c&&0===c.offsetWidth)return;b.breakpoints&&a.setBreakpoint();let{allowSlideNext:d,allowSlidePrev:e,snapGrid:f}=a,g=a.virtual&&a.params.virtual.enabled;a.allowSlideNext=!0,a.allowSlidePrev=!0,a.updateSize(),a.updateSlides(),a.updateSlidesClasses();let h=g&&b.loop;"auto"!==b.slidesPerView&&!(b.slidesPerView>1)||!a.isEnd||a.isBeginning||a.params.centeredSlides||h?a.params.loop&&!g?a.slideToLoop(a.realIndex,0,!1,!0):a.slideTo(a.activeIndex,0,!1,!0):a.slideTo(a.slides.length-1,0,!1,!0),a.autoplay&&a.autoplay.running&&a.autoplay.paused&&(clearTimeout(a.autoplay.resizeTimeout),a.autoplay.resizeTimeout=setTimeout(()=>{a.autoplay&&a.autoplay.running&&a.autoplay.paused&&a.autoplay.resume()},500)),a.allowSlidePrev=e,a.allowSlideNext=d,a.params.watchOverflow&&f!==a.snapGrid&&a.checkOverflow()}function s(a){this.enabled&&!this.allowClick&&(this.params.preventClicks&&a.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(a.stopPropagation(),a.stopImmediatePropagation()))}function t(){let{wrapperEl:a,rtlTranslate:b,enabled:c}=this;if(!c)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-a.scrollLeft:this.translate=-a.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let d=this.maxTranslate()-this.minTranslate();(0===d?0:(this.translate-this.minTranslate())/d)!==this.progress&&this.updateProgress(b?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function u(a){k(this,a.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}let v=!1;function w(){}let x=(a,b)=>{let c=(0,h.g)(),{params:d,el:e,wrapperEl:f,device:g}=a,i=!!d.nested,j="on"===b?"addEventListener":"removeEventListener";e[j]("pointerdown",a.onTouchStart,{passive:!1}),c[j]("pointermove",a.onTouchMove,{passive:!1,capture:i}),c[j]("pointerup",a.onTouchEnd,{passive:!0}),c[j]("pointercancel",a.onTouchEnd,{passive:!0}),c[j]("pointerout",a.onTouchEnd,{passive:!0}),c[j]("pointerleave",a.onTouchEnd,{passive:!0}),c[j]("contextmenu",a.onTouchEnd,{passive:!0}),(d.preventClicks||d.preventClicksPropagation)&&e[j]("click",a.onClick,!0),d.cssMode&&f[j]("scroll",a.onScroll),d.updateOnWindowResize?a[b](g.ios||g.android?"resize orientationchange observerUpdate":"resize observerUpdate",r,!0):a[b]("observerUpdate",r,!0),e[j]("load",a.onLoad,{capture:!0})},y=(a,b)=>a.grid&&b.grid&&b.grid.rows>1;var z={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let A={eventsEmitter:{on(a,b,c){let d=this;if(!d.eventsListeners||d.destroyed||"function"!=typeof b)return d;let e=c?"unshift":"push";return a.split(" ").forEach(a=>{d.eventsListeners[a]||(d.eventsListeners[a]=[]),d.eventsListeners[a][e](b)}),d},once(a,b,c){let d=this;if(!d.eventsListeners||d.destroyed||"function"!=typeof b)return d;function e(){d.off(a,e),e.__emitterProxy&&delete e.__emitterProxy;for(var c=arguments.length,f=Array(c),g=0;g<c;g++)f[g]=arguments[g];b.apply(d,f)}return e.__emitterProxy=b,d.on(a,e,c)},onAny(a,b){return!this.eventsListeners||this.destroyed||"function"!=typeof a||0>this.eventsAnyListeners.indexOf(a)&&this.eventsAnyListeners[b?"unshift":"push"](a),this},offAny(a){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let b=this.eventsAnyListeners.indexOf(a);return b>=0&&this.eventsAnyListeners.splice(b,1),this},off(a,b){let c=this;return c.eventsListeners&&!c.destroyed&&c.eventsListeners&&a.split(" ").forEach(a=>{void 0===b?c.eventsListeners[a]=[]:c.eventsListeners[a]&&c.eventsListeners[a].forEach((d,e)=>{(d===b||d.__emitterProxy&&d.__emitterProxy===b)&&c.eventsListeners[a].splice(e,1)})}),c},emit(){let a,b,c,d=this;if(!d.eventsListeners||d.destroyed||!d.eventsListeners)return d;for(var e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];return"string"==typeof f[0]||Array.isArray(f[0])?(a=f[0],b=f.slice(1,f.length),c=d):(a=f[0].events,b=f[0].data,c=f[0].context||d),b.unshift(c),(Array.isArray(a)?a:a.split(" ")).forEach(a=>{d.eventsAnyListeners&&d.eventsAnyListeners.length&&d.eventsAnyListeners.forEach(d=>{d.apply(c,[a,...b])}),d.eventsListeners&&d.eventsListeners[a]&&d.eventsListeners[a].forEach(a=>{a.apply(c,b)})}),d}},update:{updateSize:function(){let a,b,c=this.el;a=void 0!==this.params.width&&null!==this.params.width?this.params.width:c.clientWidth,b=void 0!==this.params.height&&null!==this.params.height?this.params.height:c.clientHeight,0===a&&this.isHorizontal()||0===b&&this.isVertical()||(a=a-parseInt((0,i.l)(c,"padding-left")||0,10)-parseInt((0,i.l)(c,"padding-right")||0,10),b=b-parseInt((0,i.l)(c,"padding-top")||0,10)-parseInt((0,i.l)(c,"padding-bottom")||0,10),Number.isNaN(a)&&(a=0),Number.isNaN(b)&&(b=0),Object.assign(this,{width:a,height:b,size:this.isHorizontal()?a:b}))},updateSlides:function(){let a,b=this;function c(a){return b.isHorizontal()?a:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[a]}function d(a,b){return parseFloat(a.getPropertyValue(c(b))||0)}let e=b.params,{wrapperEl:f,slidesEl:g,size:h,rtlTranslate:j,wrongRTL:k}=b,l=b.virtual&&e.virtual.enabled,m=l?b.virtual.slides.length:b.slides.length,n=(0,i.e)(g,`.${b.params.slideClass}, swiper-slide`),o=l?b.virtual.slides.length:n.length,p=[],q=[],r=[],s=e.slidesOffsetBefore;"function"==typeof s&&(s=e.slidesOffsetBefore.call(b));let t=e.slidesOffsetAfter;"function"==typeof t&&(t=e.slidesOffsetAfter.call(b));let u=b.snapGrid.length,v=b.slidesGrid.length,w=e.spaceBetween,x=-s,y=0,z=0;if(void 0===h)return;"string"==typeof w&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*h:"string"==typeof w&&(w=parseFloat(w)),b.virtualSize=-w,n.forEach(a=>{j?a.style.marginLeft="":a.style.marginRight="",a.style.marginBottom="",a.style.marginTop=""}),e.centeredSlides&&e.cssMode&&((0,i.s)(f,"--swiper-centered-offset-before",""),(0,i.s)(f,"--swiper-centered-offset-after",""));let A=e.grid&&e.grid.rows>1&&b.grid;A&&b.grid.initSlides(o);let B="auto"===e.slidesPerView&&e.breakpoints&&Object.keys(e.breakpoints).filter(a=>void 0!==e.breakpoints[a].slidesPerView).length>0;for(let f=0;f<o;f+=1){let g;if(a=0,n[f]&&(g=n[f]),A&&b.grid.updateSlide(f,g,o,c),!n[f]||"none"!==(0,i.l)(g,"display")){if("auto"===e.slidesPerView){B&&(n[f].style[c("width")]="");let h=getComputedStyle(g),j=g.style.transform,k=g.style.webkitTransform;if(j&&(g.style.transform="none"),k&&(g.style.webkitTransform="none"),e.roundLengths)a=b.isHorizontal()?(0,i.f)(g,"width",!0):(0,i.f)(g,"height",!0);else{let b=d(h,"width"),c=d(h,"padding-left"),e=d(h,"padding-right"),f=d(h,"margin-left"),i=d(h,"margin-right"),j=h.getPropertyValue("box-sizing");if(j&&"border-box"===j)a=b+f+i;else{let{clientWidth:d,offsetWidth:h}=g;a=b+c+e+f+i+(h-d)}}j&&(g.style.transform=j),k&&(g.style.webkitTransform=k),e.roundLengths&&(a=Math.floor(a))}else a=(h-(e.slidesPerView-1)*w)/e.slidesPerView,e.roundLengths&&(a=Math.floor(a)),n[f]&&(n[f].style[c("width")]=`${a}px`);n[f]&&(n[f].swiperSlideSize=a),r.push(a),e.centeredSlides?(x=x+a/2+y/2+w,0===y&&0!==f&&(x=x-h/2-w),0===f&&(x=x-h/2-w),.001>Math.abs(x)&&(x=0),e.roundLengths&&(x=Math.floor(x)),z%e.slidesPerGroup==0&&p.push(x),q.push(x)):(e.roundLengths&&(x=Math.floor(x)),(z-Math.min(b.params.slidesPerGroupSkip,z))%b.params.slidesPerGroup==0&&p.push(x),q.push(x),x=x+a+w),b.virtualSize+=a+w,y=a,z+=1}}if(b.virtualSize=Math.max(b.virtualSize,h)+t,j&&k&&("slide"===e.effect||"coverflow"===e.effect)&&(f.style.width=`${b.virtualSize+w}px`),e.setWrapperSize&&(f.style[c("width")]=`${b.virtualSize+w}px`),A&&b.grid.updateWrapperSize(a,p,c),!e.centeredSlides){let a=[];for(let c=0;c<p.length;c+=1){let d=p[c];e.roundLengths&&(d=Math.floor(d)),p[c]<=b.virtualSize-h&&a.push(d)}p=a,Math.floor(b.virtualSize-h)-Math.floor(p[p.length-1])>1&&p.push(b.virtualSize-h)}if(l&&e.loop){let a=r[0]+w;if(e.slidesPerGroup>1){let c=Math.ceil((b.virtual.slidesBefore+b.virtual.slidesAfter)/e.slidesPerGroup),d=a*e.slidesPerGroup;for(let a=0;a<c;a+=1)p.push(p[p.length-1]+d)}for(let c=0;c<b.virtual.slidesBefore+b.virtual.slidesAfter;c+=1)1===e.slidesPerGroup&&p.push(p[p.length-1]+a),q.push(q[q.length-1]+a),b.virtualSize+=a}if(0===p.length&&(p=[0]),0!==w){let a=b.isHorizontal()&&j?"marginLeft":c("marginRight");n.filter((a,b)=>!e.cssMode||!!e.loop||b!==n.length-1).forEach(b=>{b.style[a]=`${w}px`})}if(e.centeredSlides&&e.centeredSlidesBounds){let a=0;r.forEach(b=>{a+=b+(w||0)});let b=(a-=w)-h;p=p.map(a=>a<=0?-s:a>b?b+t:a)}if(e.centerInsufficientSlides){let a=0;if(r.forEach(b=>{a+=b+(w||0)}),(a-=w)<h){let b=(h-a)/2;p.forEach((a,c)=>{p[c]=a-b}),q.forEach((a,c)=>{q[c]=a+b})}}if(Object.assign(b,{slides:n,snapGrid:p,slidesGrid:q,slidesSizesGrid:r}),e.centeredSlides&&e.cssMode&&!e.centeredSlidesBounds){(0,i.s)(f,"--swiper-centered-offset-before",`${-p[0]}px`),(0,i.s)(f,"--swiper-centered-offset-after",`${b.size/2-r[r.length-1]/2}px`);let a=-b.snapGrid[0],c=-b.slidesGrid[0];b.snapGrid=b.snapGrid.map(b=>b+a),b.slidesGrid=b.slidesGrid.map(a=>a+c)}if(o!==m&&b.emit("slidesLengthChange"),p.length!==u&&(b.params.watchOverflow&&b.checkOverflow(),b.emit("snapGridLengthChange")),q.length!==v&&b.emit("slidesGridLengthChange"),e.watchSlidesProgress&&b.updateSlidesOffset(),!l&&!e.cssMode&&("slide"===e.effect||"fade"===e.effect)){let a=`${e.containerModifierClass}backface-hidden`,c=b.el.classList.contains(a);o<=e.maxBackfaceHiddenSlides?c||b.el.classList.add(a):c&&b.el.classList.remove(a)}},updateAutoHeight:function(a){let b,c=this,d=[],e=c.virtual&&c.params.virtual.enabled,f=0;"number"==typeof a?c.setTransition(a):!0===a&&c.setTransition(c.params.speed);let g=a=>e?c.slides[c.getSlideIndexByData(a)]:c.slides[a];if("auto"!==c.params.slidesPerView&&c.params.slidesPerView>1)if(c.params.centeredSlides)(c.visibleSlides||[]).forEach(a=>{d.push(a)});else for(b=0;b<Math.ceil(c.params.slidesPerView);b+=1){let a=c.activeIndex+b;if(a>c.slides.length&&!e)break;d.push(g(a))}else d.push(g(c.activeIndex));for(b=0;b<d.length;b+=1)if(void 0!==d[b]){let a=d[b].offsetHeight;f=a>f?a:f}(f||0===f)&&(c.wrapperEl.style.height=`${f}px`)},updateSlidesOffset:function(){let a=this.slides,b=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let c=0;c<a.length;c+=1)a[c].swiperSlideOffset=(this.isHorizontal()?a[c].offsetLeft:a[c].offsetTop)-b-this.cssOverflowAdjustment()},updateSlidesProgress:function(a){void 0===a&&(a=this&&this.translate||0);let b=this.params,{slides:c,rtlTranslate:d,snapGrid:e}=this;if(0===c.length)return;void 0===c[0].swiperSlideOffset&&this.updateSlidesOffset();let f=-a;d&&(f=a),c.forEach(a=>{a.classList.remove(b.slideVisibleClass)}),this.visibleSlidesIndexes=[],this.visibleSlides=[];let g=b.spaceBetween;"string"==typeof g&&g.indexOf("%")>=0?g=parseFloat(g.replace("%",""))/100*this.size:"string"==typeof g&&(g=parseFloat(g));for(let a=0;a<c.length;a+=1){let h=c[a],i=h.swiperSlideOffset;b.cssMode&&b.centeredSlides&&(i-=c[0].swiperSlideOffset);let j=(f+(b.centeredSlides?this.minTranslate():0)-i)/(h.swiperSlideSize+g),k=(f-e[0]+(b.centeredSlides?this.minTranslate():0)-i)/(h.swiperSlideSize+g),l=-(f-i),m=l+this.slidesSizesGrid[a];(l>=0&&l<this.size-1||m>1&&m<=this.size||l<=0&&m>=this.size)&&(this.visibleSlides.push(h),this.visibleSlidesIndexes.push(a),c[a].classList.add(b.slideVisibleClass)),h.progress=d?-j:j,h.originalProgress=d?-k:k}},updateProgress:function(a){if(void 0===a){let b=this.rtlTranslate?-1:1;a=this&&this.translate&&this.translate*b||0}let b=this.params,c=this.maxTranslate()-this.minTranslate(),{progress:d,isBeginning:e,isEnd:f,progressLoop:g}=this,h=e,i=f;if(0===c)d=0,e=!0,f=!0;else{d=(a-this.minTranslate())/c;let b=1>Math.abs(a-this.minTranslate()),g=1>Math.abs(a-this.maxTranslate());e=b||d<=0,f=g||d>=1,b&&(d=0),g&&(d=1)}if(b.loop){let b=this.getSlideIndexByData(0),c=this.getSlideIndexByData(this.slides.length-1),d=this.slidesGrid[b],e=this.slidesGrid[c],f=this.slidesGrid[this.slidesGrid.length-1],h=Math.abs(a);(g=h>=d?(h-d)/f:(h+f-e)/f)>1&&(g-=1)}Object.assign(this,{progress:d,progressLoop:g,isBeginning:e,isEnd:f}),(b.watchSlidesProgress||b.centeredSlides&&b.autoHeight)&&this.updateSlidesProgress(a),e&&!h&&this.emit("reachBeginning toEdge"),f&&!i&&this.emit("reachEnd toEdge"),(h&&!e||i&&!f)&&this.emit("fromEdge"),this.emit("progress",d)},updateSlidesClasses:function(){let a,{slides:b,params:c,slidesEl:d,activeIndex:e}=this,f=this.virtual&&c.virtual.enabled,g=a=>(0,i.e)(d,`.${c.slideClass}${a}, swiper-slide${a}`)[0];if(b.forEach(a=>{a.classList.remove(c.slideActiveClass,c.slideNextClass,c.slidePrevClass)}),f)if(c.loop){let b=e-this.virtual.slidesBefore;b<0&&(b=this.virtual.slides.length+b),b>=this.virtual.slides.length&&(b-=this.virtual.slides.length),a=g(`[data-swiper-slide-index="${b}"]`)}else a=g(`[data-swiper-slide-index="${e}"]`);else a=b[e];if(a){a.classList.add(c.slideActiveClass);let d=(0,i.m)(a,`.${c.slideClass}, swiper-slide`)[0];c.loop&&!d&&(d=b[0]),d&&d.classList.add(c.slideNextClass);let e=(0,i.o)(a,`.${c.slideClass}, swiper-slide`)[0];c.loop,e&&e.classList.add(c.slidePrevClass)}this.emitSlidesClasses()},updateActiveIndex:function(a){let b,c,d=this,e=d.rtlTranslate?d.translate:-d.translate,{snapGrid:f,params:g,activeIndex:h,realIndex:i,snapIndex:j}=d,k=a,l=a=>{let b=a-d.virtual.slidesBefore;return b<0&&(b=d.virtual.slides.length+b),b>=d.virtual.slides.length&&(b-=d.virtual.slides.length),b};if(void 0===k&&(k=function(a){let b,{slidesGrid:c,params:d}=a,e=a.rtlTranslate?a.translate:-a.translate;for(let a=0;a<c.length;a+=1)void 0!==c[a+1]?e>=c[a]&&e<c[a+1]-(c[a+1]-c[a])/2?b=a:e>=c[a]&&e<c[a+1]&&(b=a+1):e>=c[a]&&(b=a);return d.normalizeSlideIndex&&(b<0||void 0===b)&&(b=0),b}(d)),f.indexOf(e)>=0)b=f.indexOf(e);else{let a=Math.min(g.slidesPerGroupSkip,k);b=a+Math.floor((k-a)/g.slidesPerGroup)}if(b>=f.length&&(b=f.length-1),k===h){b!==j&&(d.snapIndex=b,d.emit("snapIndexChange")),d.params.loop&&d.virtual&&d.params.virtual.enabled&&(d.realIndex=l(k));return}c=d.virtual&&g.virtual.enabled&&g.loop?l(k):d.slides[k]?parseInt(d.slides[k].getAttribute("data-swiper-slide-index")||k,10):k,Object.assign(d,{previousSnapIndex:j,snapIndex:b,previousRealIndex:i,realIndex:c,previousIndex:h,activeIndex:k}),d.initialized&&m(d),d.emit("activeIndexChange"),d.emit("snapIndexChange"),(d.initialized||d.params.runCallbacksOnInit)&&(i!==c&&d.emit("realIndexChange"),d.emit("slideChange"))},updateClickedSlide:function(a,b){let c,d=this.params,e=a.closest(`.${d.slideClass}, swiper-slide`);!e&&this.isElement&&b&&b.length>1&&b.includes(a)&&[...b.slice(b.indexOf(a)+1,b.length)].forEach(a=>{!e&&a.matches&&a.matches(`.${d.slideClass}, swiper-slide`)&&(e=a)});let f=!1;if(e){for(let a=0;a<this.slides.length;a+=1)if(this.slides[a]===e){f=!0,c=a;break}}if(e&&f)this.clickedSlide=e,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(e.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=c;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}d.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(a){void 0===a&&(a=this.isHorizontal()?"x":"y");let{params:b,rtlTranslate:c,translate:d,wrapperEl:e}=this;if(b.virtualTranslate)return c?-d:d;if(b.cssMode)return d;let f=(0,i.h)(e,a);return f+=this.cssOverflowAdjustment(),c&&(f=-f),f||0},setTranslate:function(a,b){let{rtlTranslate:c,params:d,wrapperEl:e,progress:f}=this,g=0,h=0;this.isHorizontal()?g=c?-a:a:h=a,d.roundLengths&&(g=Math.floor(g),h=Math.floor(h)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?g:h,d.cssMode?e[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-g:-h:d.virtualTranslate||(this.isHorizontal()?g-=this.cssOverflowAdjustment():h-=this.cssOverflowAdjustment(),e.style.transform=`translate3d(${g}px, ${h}px, 0px)`);let i=this.maxTranslate()-this.minTranslate();(0===i?0:(a-this.minTranslate())/i)!==f&&this.updateProgress(a),this.emit("setTranslate",this.translate,b)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(a,b,c,d,e){let f;void 0===a&&(a=0),void 0===b&&(b=this.params.speed),void 0===c&&(c=!0),void 0===d&&(d=!0);let g=this,{params:h,wrapperEl:j}=g;if(g.animating&&h.preventInteractionOnTransition)return!1;let k=g.minTranslate(),l=g.maxTranslate();if(f=d&&a>k?k:d&&a<l?l:a,g.updateProgress(f),h.cssMode){let a=g.isHorizontal();if(0===b)j[a?"scrollLeft":"scrollTop"]=-f;else{if(!g.support.smoothScroll)return(0,i.p)({swiper:g,targetPosition:-f,side:a?"left":"top"}),!0;j.scrollTo({[a?"left":"top"]:-f,behavior:"smooth"})}return!0}return 0===b?(g.setTransition(0),g.setTranslate(f),c&&(g.emit("beforeTransitionStart",b,e),g.emit("transitionEnd"))):(g.setTransition(b),g.setTranslate(f),c&&(g.emit("beforeTransitionStart",b,e),g.emit("transitionStart")),g.animating||(g.animating=!0,g.onTranslateToWrapperTransitionEnd||(g.onTranslateToWrapperTransitionEnd=function(a){g&&!g.destroyed&&a.target===this&&(g.wrapperEl.removeEventListener("transitionend",g.onTranslateToWrapperTransitionEnd),g.onTranslateToWrapperTransitionEnd=null,delete g.onTranslateToWrapperTransitionEnd,c&&g.emit("transitionEnd"))}),g.wrapperEl.addEventListener("transitionend",g.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(a,b){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${a}ms`,this.wrapperEl.style.transitionDelay=0===a?"0ms":""),this.emit("setTransition",a,b)},transitionStart:function(a,b){void 0===a&&(a=!0);let{params:c}=this;c.cssMode||(c.autoHeight&&this.updateAutoHeight(),n({swiper:this,runCallbacks:a,direction:b,step:"Start"}))},transitionEnd:function(a,b){void 0===a&&(a=!0);let{params:c}=this;this.animating=!1,c.cssMode||(this.setTransition(0),n({swiper:this,runCallbacks:a,direction:b,step:"End"}))}},slide:{slideTo:function(a,b,c,d,e){let f;void 0===a&&(a=0),void 0===b&&(b=this.params.speed),void 0===c&&(c=!0),"string"==typeof a&&(a=parseInt(a,10));let g=this,h=a;h<0&&(h=0);let{params:j,snapGrid:k,slidesGrid:l,previousIndex:m,activeIndex:n,rtlTranslate:o,wrapperEl:p,enabled:q}=g;if(g.animating&&j.preventInteractionOnTransition||!q&&!d&&!e)return!1;let r=Math.min(g.params.slidesPerGroupSkip,h),s=r+Math.floor((h-r)/g.params.slidesPerGroup);s>=k.length&&(s=k.length-1);let t=-k[s];if(j.normalizeSlideIndex)for(let a=0;a<l.length;a+=1){let b=-Math.floor(100*t),c=Math.floor(100*l[a]),d=Math.floor(100*l[a+1]);void 0!==l[a+1]?b>=c&&b<d-(d-c)/2?h=a:b>=c&&b<d&&(h=a+1):b>=c&&(h=a)}if(g.initialized&&h!==n&&(!g.allowSlideNext&&(o?t>g.translate&&t>g.minTranslate():t<g.translate&&t<g.minTranslate())||!g.allowSlidePrev&&t>g.translate&&t>g.maxTranslate()&&(n||0)!==h))return!1;if(h!==(m||0)&&c&&g.emit("beforeSlideChangeStart"),g.updateProgress(t),f=h>n?"next":h<n?"prev":"reset",o&&-t===g.translate||!o&&t===g.translate)return g.updateActiveIndex(h),j.autoHeight&&g.updateAutoHeight(),g.updateSlidesClasses(),"slide"!==j.effect&&g.setTranslate(t),"reset"!==f&&(g.transitionStart(c,f),g.transitionEnd(c,f)),!1;if(j.cssMode){let a=g.isHorizontal(),c=o?t:-t;if(0===b){let b=g.virtual&&g.params.virtual.enabled;b&&(g.wrapperEl.style.scrollSnapType="none",g._immediateVirtual=!0),b&&!g._cssModeVirtualInitialSet&&g.params.initialSlide>0?(g._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{p[a?"scrollLeft":"scrollTop"]=c})):p[a?"scrollLeft":"scrollTop"]=c,b&&requestAnimationFrame(()=>{g.wrapperEl.style.scrollSnapType="",g._immediateVirtual=!1})}else{if(!g.support.smoothScroll)return(0,i.p)({swiper:g,targetPosition:c,side:a?"left":"top"}),!0;p.scrollTo({[a?"left":"top"]:c,behavior:"smooth"})}return!0}return g.setTransition(b),g.setTranslate(t),g.updateActiveIndex(h),g.updateSlidesClasses(),g.emit("beforeTransitionStart",b,d),g.transitionStart(c,f),0===b?g.transitionEnd(c,f):g.animating||(g.animating=!0,g.onSlideToWrapperTransitionEnd||(g.onSlideToWrapperTransitionEnd=function(a){g&&!g.destroyed&&a.target===this&&(g.wrapperEl.removeEventListener("transitionend",g.onSlideToWrapperTransitionEnd),g.onSlideToWrapperTransitionEnd=null,delete g.onSlideToWrapperTransitionEnd,g.transitionEnd(c,f))}),g.wrapperEl.addEventListener("transitionend",g.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(a,b,c,d){void 0===a&&(a=0),void 0===b&&(b=this.params.speed),void 0===c&&(c=!0),"string"==typeof a&&(a=parseInt(a,10));let e=a;return this.params.loop&&(this.virtual&&this.params.virtual.enabled?e+=this.virtual.slidesBefore:e=this.getSlideIndexByData(e)),this.slideTo(e,b,c,d)},slideNext:function(a,b,c){void 0===a&&(a=this.params.speed),void 0===b&&(b=!0);let d=this,{enabled:e,params:f,animating:g}=d;if(!e)return d;let h=f.slidesPerGroup;"auto"===f.slidesPerView&&1===f.slidesPerGroup&&f.slidesPerGroupAuto&&(h=Math.max(d.slidesPerViewDynamic("current",!0),1));let i=d.activeIndex<f.slidesPerGroupSkip?1:h,j=d.virtual&&f.virtual.enabled;if(f.loop){if(g&&!j&&f.loopPreventsSliding)return!1;if(d.loopFix({direction:"next"}),d._clientLeft=d.wrapperEl.clientLeft,d.activeIndex===d.slides.length-1&&f.cssMode)return requestAnimationFrame(()=>{d.slideTo(d.activeIndex+i,a,b,c)}),!0}return f.rewind&&d.isEnd?d.slideTo(0,a,b,c):d.slideTo(d.activeIndex+i,a,b,c)},slidePrev:function(a,b,c){void 0===a&&(a=this.params.speed),void 0===b&&(b=!0);let d=this,{params:e,snapGrid:f,slidesGrid:g,rtlTranslate:h,enabled:i,animating:j}=d;if(!i)return d;let k=d.virtual&&e.virtual.enabled;if(e.loop){if(j&&!k&&e.loopPreventsSliding)return!1;d.loopFix({direction:"prev"}),d._clientLeft=d.wrapperEl.clientLeft}function l(a){return a<0?-Math.floor(Math.abs(a)):Math.floor(a)}let m=l(h?d.translate:-d.translate),n=f.map(a=>l(a)),o=f[n.indexOf(m)-1];if(void 0===o&&e.cssMode){let a;f.forEach((b,c)=>{m>=b&&(a=c)}),void 0!==a&&(o=f[a>0?a-1:a])}let p=0;if(void 0!==o&&((p=g.indexOf(o))<0&&(p=d.activeIndex-1),"auto"===e.slidesPerView&&1===e.slidesPerGroup&&e.slidesPerGroupAuto&&(p=Math.max(p=p-d.slidesPerViewDynamic("previous",!0)+1,0))),e.rewind&&d.isBeginning){let e=d.params.virtual&&d.params.virtual.enabled&&d.virtual?d.virtual.slides.length-1:d.slides.length-1;return d.slideTo(e,a,b,c)}return e.loop&&0===d.activeIndex&&e.cssMode?(requestAnimationFrame(()=>{d.slideTo(p,a,b,c)}),!0):d.slideTo(p,a,b,c)},slideReset:function(a,b,c){return void 0===a&&(a=this.params.speed),void 0===b&&(b=!0),this.slideTo(this.activeIndex,a,b,c)},slideToClosest:function(a,b,c,d){void 0===a&&(a=this.params.speed),void 0===b&&(b=!0),void 0===d&&(d=.5);let e=this.activeIndex,f=Math.min(this.params.slidesPerGroupSkip,e),g=f+Math.floor((e-f)/this.params.slidesPerGroup),h=this.rtlTranslate?this.translate:-this.translate;if(h>=this.snapGrid[g]){let a=this.snapGrid[g];h-a>(this.snapGrid[g+1]-a)*d&&(e+=this.params.slidesPerGroup)}else{let a=this.snapGrid[g-1];h-a<=(this.snapGrid[g]-a)*d&&(e-=this.params.slidesPerGroup)}return e=Math.min(e=Math.max(e,0),this.slidesGrid.length-1),this.slideTo(e,a,b,c)},slideToClickedSlide:function(){let a,b=this,{params:c,slidesEl:d}=b,e="auto"===c.slidesPerView?b.slidesPerViewDynamic():c.slidesPerView,f=b.clickedIndex,g=b.isElement?"swiper-slide":`.${c.slideClass}`;if(c.loop){if(b.animating)return;a=parseInt(b.clickedSlide.getAttribute("data-swiper-slide-index"),10),c.centeredSlides?f<b.loopedSlides-e/2||f>b.slides.length-b.loopedSlides+e/2?(b.loopFix(),f=b.getSlideIndex((0,i.e)(d,`${g}[data-swiper-slide-index="${a}"]`)[0]),(0,i.n)(()=>{b.slideTo(f)})):b.slideTo(f):f>b.slides.length-e?(b.loopFix(),f=b.getSlideIndex((0,i.e)(d,`${g}[data-swiper-slide-index="${a}"]`)[0]),(0,i.n)(()=>{b.slideTo(f)})):b.slideTo(f)}else b.slideTo(f)}},loop:{loopCreate:function(a){let{params:b,slidesEl:c}=this;!b.loop||this.virtual&&this.params.virtual.enabled||((0,i.e)(c,`.${b.slideClass}, swiper-slide`).forEach((a,b)=>{a.setAttribute("data-swiper-slide-index",b)}),this.loopFix({slideRealIndex:a,direction:b.centeredSlides?void 0:"next"}))},loopFix:function(a){let{slideRealIndex:b,slideTo:c=!0,direction:d,setTranslate:e,activeSlideIndex:f,byController:g,byMousewheel:h}=void 0===a?{}:a,i=this;if(!i.params.loop)return;i.emit("beforeLoopFix");let{slides:j,allowSlidePrev:k,allowSlideNext:l,slidesEl:m,params:n}=i;if(i.allowSlidePrev=!0,i.allowSlideNext=!0,i.virtual&&n.virtual.enabled){c&&(n.centeredSlides||0!==i.snapIndex?n.centeredSlides&&i.snapIndex<n.slidesPerView?i.slideTo(i.virtual.slides.length+i.snapIndex,0,!1,!0):i.snapIndex===i.snapGrid.length-1&&i.slideTo(i.virtual.slidesBefore,0,!1,!0):i.slideTo(i.virtual.slides.length,0,!1,!0)),i.allowSlidePrev=k,i.allowSlideNext=l,i.emit("loopFix");return}let o="auto"===n.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)),p=n.loopedSlides||o;p%n.slidesPerGroup!=0&&(p+=n.slidesPerGroup-p%n.slidesPerGroup),i.loopedSlides=p;let q=[],r=[],s=i.activeIndex;void 0===f?f=i.getSlideIndex(i.slides.filter(a=>a.classList.contains(n.slideActiveClass))[0]):s=f;let t="next"===d||!d,u="prev"===d||!d,v=0,w=0;if(f<p){v=Math.max(p-f,n.slidesPerGroup);for(let a=0;a<p-f;a+=1){let b=a-Math.floor(a/j.length)*j.length;q.push(j.length-b-1)}}else if(f>i.slides.length-2*p){w=Math.max(f-(i.slides.length-2*p),n.slidesPerGroup);for(let a=0;a<w;a+=1){let b=a-Math.floor(a/j.length)*j.length;r.push(b)}}if(u&&q.forEach(a=>{i.slides[a].swiperLoopMoveDOM=!0,m.prepend(i.slides[a]),i.slides[a].swiperLoopMoveDOM=!1}),t&&r.forEach(a=>{i.slides[a].swiperLoopMoveDOM=!0,m.append(i.slides[a]),i.slides[a].swiperLoopMoveDOM=!1}),i.recalcSlides(),"auto"===n.slidesPerView&&i.updateSlides(),n.watchSlidesProgress&&i.updateSlidesOffset(),c){if(q.length>0&&u)if(void 0===b){let a=i.slidesGrid[s],b=i.slidesGrid[s+v]-a;h?i.setTranslate(i.translate-b):(i.slideTo(s+v,0,!1,!0),e&&(i.touches[i.isHorizontal()?"startX":"startY"]+=b,i.touchEventsData.currentTranslate=i.translate))}else e&&(i.slideToLoop(b,0,!1,!0),i.touchEventsData.currentTranslate=i.translate);else if(r.length>0&&t)if(void 0===b){let a=i.slidesGrid[s],b=i.slidesGrid[s-w]-a;h?i.setTranslate(i.translate-b):(i.slideTo(s-w,0,!1,!0),e&&(i.touches[i.isHorizontal()?"startX":"startY"]+=b,i.touchEventsData.currentTranslate=i.translate))}else i.slideToLoop(b,0,!1,!0)}if(i.allowSlidePrev=k,i.allowSlideNext=l,i.controller&&i.controller.control&&!g){let a={slideRealIndex:b,direction:d,setTranslate:e,activeSlideIndex:f,byController:!0};Array.isArray(i.controller.control)?i.controller.control.forEach(b=>{!b.destroyed&&b.params.loop&&b.loopFix({...a,slideTo:b.params.slidesPerView===n.slidesPerView&&c})}):i.controller.control instanceof i.constructor&&i.controller.control.params.loop&&i.controller.control.loopFix({...a,slideTo:i.controller.control.params.slidesPerView===n.slidesPerView&&c})}i.emit("loopFix")},loopDestroy:function(){let{params:a,slidesEl:b}=this;if(!a.loop||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let c=[];this.slides.forEach(a=>{c[void 0===a.swiperSlideIndex?+a.getAttribute("data-swiper-slide-index"):a.swiperSlideIndex]=a}),this.slides.forEach(a=>{a.removeAttribute("data-swiper-slide-index")}),c.forEach(a=>{b.append(a)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(a){let b=this;if(!b.params.simulateTouch||b.params.watchOverflow&&b.isLocked||b.params.cssMode)return;let c="container"===b.params.touchEventsTarget?b.el:b.wrapperEl;b.isElement&&(b.__preventObserver__=!0),c.style.cursor="move",c.style.cursor=a?"grabbing":"grab",b.isElement&&requestAnimationFrame(()=>{b.__preventObserver__=!1})},unsetGrabCursor:function(){let a=this;a.params.watchOverflow&&a.isLocked||a.params.cssMode||(a.isElement&&(a.__preventObserver__=!0),a["container"===a.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",a.isElement&&requestAnimationFrame(()=>{a.__preventObserver__=!1}))}},events:{attachEvents:function(){let a=(0,h.g)(),{params:b}=this;this.onTouchStart=o.bind(this),this.onTouchMove=p.bind(this),this.onTouchEnd=q.bind(this),b.cssMode&&(this.onScroll=t.bind(this)),this.onClick=s.bind(this),this.onLoad=u.bind(this),v||(a.addEventListener("touchstart",w),v=!0),x(this,"on")},detachEvents:function(){x(this,"off")}},breakpoints:{setBreakpoint:function(){let a=this,{realIndex:b,initialized:c,params:d,el:e}=a,f=d.breakpoints;if(!f||f&&0===Object.keys(f).length)return;let g=a.getBreakpoint(f,a.params.breakpointsBase,a.el);if(!g||a.currentBreakpoint===g)return;let h=(g in f?f[g]:void 0)||a.originalParams,j=y(a,d),k=y(a,h),l=d.enabled;j&&!k?(e.classList.remove(`${d.containerModifierClass}grid`,`${d.containerModifierClass}grid-column`),a.emitContainerClasses()):!j&&k&&(e.classList.add(`${d.containerModifierClass}grid`),(h.grid.fill&&"column"===h.grid.fill||!h.grid.fill&&"column"===d.grid.fill)&&e.classList.add(`${d.containerModifierClass}grid-column`),a.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(b=>{if(void 0===h[b])return;let c=d[b]&&d[b].enabled,e=h[b]&&h[b].enabled;c&&!e&&a[b].disable(),!c&&e&&a[b].enable()});let m=h.direction&&h.direction!==d.direction,n=d.loop&&(h.slidesPerView!==d.slidesPerView||m),o=d.loop;m&&c&&a.changeDirection(),(0,i.q)(a.params,h);let p=a.params.enabled,q=a.params.loop;Object.assign(a,{allowTouchMove:a.params.allowTouchMove,allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev}),l&&!p?a.disable():!l&&p&&a.enable(),a.currentBreakpoint=g,a.emit("_beforeBreakpoint",h),c&&(n?(a.loopDestroy(),a.loopCreate(b),a.updateSlides()):!o&&q?(a.loopCreate(b),a.updateSlides()):o&&!q&&a.loopDestroy()),a.emit("breakpoint",h)},getBreakpoint:function(a,b,c){if(void 0===b&&(b="window"),!a||"container"===b&&!c)return;let d=!1,e=(0,h.a)(),f="window"===b?e.innerHeight:c.clientHeight,g=Object.keys(a).map(a=>"string"==typeof a&&0===a.indexOf("@")?{value:f*parseFloat(a.substr(1)),point:a}:{value:a,point:a});g.sort((a,b)=>parseInt(a.value,10)-parseInt(b.value,10));for(let a=0;a<g.length;a+=1){let{point:f,value:h}=g[a];"window"===b?e.matchMedia(`(min-width: ${h}px)`).matches&&(d=f):h<=c.clientWidth&&(d=f)}return d||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:a,params:b}=this,{slidesOffsetBefore:c}=b;if(c){let a=this.slides.length-1,b=this.slidesGrid[a]+this.slidesSizesGrid[a]+2*c;this.isLocked=this.size>b}else this.isLocked=1===this.snapGrid.length;!0===b.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===b.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),a&&a!==this.isLocked&&(this.isEnd=!1),a!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:a,params:b,rtl:c,el:d,device:e}=this,f=function(a,b){let c=[];return a.forEach(a=>{"object"==typeof a?Object.keys(a).forEach(d=>{a[d]&&c.push(b+d)}):"string"==typeof a&&c.push(b+a)}),c}(["initialized",b.direction,{"free-mode":this.params.freeMode&&b.freeMode.enabled},{autoheight:b.autoHeight},{rtl:c},{grid:b.grid&&b.grid.rows>1},{"grid-column":b.grid&&b.grid.rows>1&&"column"===b.grid.fill},{android:e.android},{ios:e.ios},{"css-mode":b.cssMode},{centered:b.cssMode&&b.centeredSlides},{"watch-progress":b.watchSlidesProgress}],b.containerModifierClass);a.push(...f),d.classList.add(...a),this.emitContainerClasses()},removeClasses:function(){let{el:a,classNames:b}=this;a.classList.remove(...b),this.emitContainerClasses()}}},B={};class C{constructor(){let a,b;for(var c=arguments.length,d=Array(c),g=0;g<c;g++)d[g]=arguments[g];1===d.length&&d[0].constructor&&"Object"===Object.prototype.toString.call(d[0]).slice(8,-1)?b=d[0]:[a,b]=d,b||(b={}),b=(0,i.q)({},b),a&&!b.el&&(b.el=a);let k=(0,h.g)();if(b.el&&"string"==typeof b.el&&k.querySelectorAll(b.el).length>1){let a=[];return k.querySelectorAll(b.el).forEach(c=>{let d=(0,i.q)({},b,{el:c});a.push(new C(d))}),a}let l=this;l.__swiper__=!0,l.support=j(),l.device=function(a){return void 0===a&&(a={}),e||(e=function(a){let{userAgent:b}=void 0===a?{}:a,c=j(),d=(0,h.a)(),e=d.navigator.platform,f=b||d.navigator.userAgent,g={ios:!1,android:!1},i=d.screen.width,k=d.screen.height,l=f.match(/(Android);?[\s\/]+([\d.]+)?/),m=f.match(/(iPad).*OS\s([\d_]+)/),n=f.match(/(iPod)(.*OS\s([\d_]+))?/),o=!m&&f.match(/(iPhone\sOS|iOS)\s([\d_]+)/),p="MacIntel"===e;return!m&&p&&c.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${i}x${k}`)>=0&&((m=f.match(/(Version)\/([\d.]+)/))||(m=[0,1,"13_0_0"]),p=!1),l&&"Win32"!==e&&(g.os="android",g.android=!0),(m||o||n)&&(g.os="ios",g.ios=!0),g}(a)),e}({userAgent:b.userAgent}),f||(f=function(){let a=(0,h.a)(),b=!1;function c(){let b=a.navigator.userAgent.toLowerCase();return b.indexOf("safari")>=0&&0>b.indexOf("chrome")&&0>b.indexOf("android")}if(c()){let c=String(a.navigator.userAgent);if(c.includes("Version/")){let[a,d]=c.split("Version/")[1].split(" ")[0].split(".").map(a=>Number(a));b=a<16||16===a&&d<2}}return{isSafari:b||c(),needPerspectiveFix:b,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(a.navigator.userAgent)}}()),l.browser=f,l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],b.modules&&Array.isArray(b.modules)&&l.modules.push(...b.modules);let m={};l.modules.forEach(a=>{a({params:b,swiper:l,extendParams:function(a,b){return function(c){void 0===c&&(c={});let d=Object.keys(c)[0],e=c[d];return"object"!=typeof e||null===e?void(0,i.q)(b,c):(!0===a[d]&&(a[d]={enabled:!0}),"navigation"===d&&a[d]&&a[d].enabled&&!a[d].prevEl&&!a[d].nextEl&&(a[d].auto=!0),["pagination","scrollbar"].indexOf(d)>=0&&a[d]&&a[d].enabled&&!a[d].el&&(a[d].auto=!0),d in a&&"enabled"in e)?void("object"==typeof a[d]&&!("enabled"in a[d])&&(a[d].enabled=!0),!a[d]&&(a[d]={enabled:!1}),(0,i.q)(b,c)):void(0,i.q)(b,c)}}(b,m),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});let n=(0,i.q)({},z,m);return l.params=(0,i.q)({},n,B,b),l.originalParams=(0,i.q)({},l.params),l.passedParams=(0,i.q)({},b),l.params&&l.params.on&&Object.keys(l.params.on).forEach(a=>{l.on(a,l.params.on[a])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:a,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===l.params.direction,isVertical:()=>"vertical"===l.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getSlideIndex(a){let{slidesEl:b,params:c}=this,d=(0,i.e)(b,`.${c.slideClass}, swiper-slide`),e=(0,i.g)(d[0]);return(0,i.g)(a)-e}getSlideIndexByData(a){return this.getSlideIndex(this.slides.filter(b=>+b.getAttribute("data-swiper-slide-index")===a)[0])}recalcSlides(){let{slidesEl:a,params:b}=this;this.slides=(0,i.e)(a,`.${b.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(a,b){a=Math.min(Math.max(a,0),1);let c=this.minTranslate(),d=(this.maxTranslate()-c)*a+c;this.translateTo(d,void 0===b?0:b),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let a=this;if(!a.params._emitClasses||!a.el)return;let b=a.el.className.split(" ").filter(b=>0===b.indexOf("swiper")||0===b.indexOf(a.params.containerModifierClass));a.emit("_containerClasses",b.join(" "))}getSlideClasses(a){let b=this;return b.destroyed?"":a.className.split(" ").filter(a=>0===a.indexOf("swiper-slide")||0===a.indexOf(b.params.slideClass)).join(" ")}emitSlidesClasses(){let a=this;if(!a.params._emitClasses||!a.el)return;let b=[];a.slides.forEach(c=>{let d=a.getSlideClasses(c);b.push({slideEl:c,classNames:d}),a.emit("_slideClass",c,d)}),a.emit("_slideClasses",b)}slidesPerViewDynamic(a,b){void 0===a&&(a="current"),void 0===b&&(b=!1);let{params:c,slides:d,slidesGrid:e,slidesSizesGrid:f,size:g,activeIndex:h}=this,i=1;if("number"==typeof c.slidesPerView)return c.slidesPerView;if(c.centeredSlides){let a,b=d[h]?d[h].swiperSlideSize:0;for(let c=h+1;c<d.length;c+=1)d[c]&&!a&&(b+=d[c].swiperSlideSize,i+=1,b>g&&(a=!0));for(let c=h-1;c>=0;c-=1)d[c]&&!a&&(b+=d[c].swiperSlideSize,i+=1,b>g&&(a=!0))}else if("current"===a)for(let a=h+1;a<d.length;a+=1)(b?e[a]+f[a]-e[h]<g:e[a]-e[h]<g)&&(i+=1);else for(let a=h-1;a>=0;a-=1)e[h]-e[a]<g&&(i+=1);return i}update(){let a,b=this;if(!b||b.destroyed)return;let{snapGrid:c,params:d}=b;function e(){let a=Math.min(Math.max(b.rtlTranslate?-1*b.translate:b.translate,b.maxTranslate()),b.minTranslate());b.setTranslate(a),b.updateActiveIndex(),b.updateSlidesClasses()}if(d.breakpoints&&b.setBreakpoint(),[...b.el.querySelectorAll('[loading="lazy"]')].forEach(a=>{a.complete&&k(b,a)}),b.updateSize(),b.updateSlides(),b.updateProgress(),b.updateSlidesClasses(),d.freeMode&&d.freeMode.enabled&&!d.cssMode)e(),d.autoHeight&&b.updateAutoHeight();else{if(("auto"===d.slidesPerView||d.slidesPerView>1)&&b.isEnd&&!d.centeredSlides){let c=b.virtual&&d.virtual.enabled?b.virtual.slides:b.slides;a=b.slideTo(c.length-1,0,!1,!0)}else a=b.slideTo(b.activeIndex,0,!1,!0);a||e()}d.watchOverflow&&c!==b.snapGrid&&b.checkOverflow(),b.emit("update")}changeDirection(a,b){void 0===b&&(b=!0);let c=this.params.direction;return a||(a="horizontal"===c?"vertical":"horizontal"),a===c||"horizontal"!==a&&"vertical"!==a||(this.el.classList.remove(`${this.params.containerModifierClass}${c}`),this.el.classList.add(`${this.params.containerModifierClass}${a}`),this.emitContainerClasses(),this.params.direction=a,this.slides.forEach(b=>{"vertical"===a?b.style.width="":b.style.height=""}),this.emit("changeDirection"),b&&this.update()),this}changeLanguageDirection(a){(!this.rtl||"rtl"!==a)&&(this.rtl||"ltr"!==a)&&(this.rtl="rtl"===a,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(a){let b=this;if(b.mounted)return!0;let c=a||b.params.el;if("string"==typeof c&&(c=document.querySelector(c)),!c)return!1;c.swiper=b,c.parentNode&&c.parentNode.host&&"SWIPER-CONTAINER"===c.parentNode.host.nodeName&&(b.isElement=!0);let d=()=>`.${(b.params.wrapperClass||"").trim().split(" ").join(".")}`,e=c&&c.shadowRoot&&c.shadowRoot.querySelector?c.shadowRoot.querySelector(d()):(0,i.e)(c,d())[0];return!e&&b.params.createElements&&(e=(0,i.c)("div",b.params.wrapperClass),c.append(e),(0,i.e)(c,`.${b.params.slideClass}`).forEach(a=>{e.append(a)})),Object.assign(b,{el:c,wrapperEl:e,slidesEl:b.isElement&&!c.parentNode.host.slideSlots?c.parentNode.host:e,hostEl:b.isElement?c.parentNode.host:c,mounted:!0,rtl:"rtl"===c.dir.toLowerCase()||"rtl"===(0,i.l)(c,"direction"),rtlTranslate:"horizontal"===b.params.direction&&("rtl"===c.dir.toLowerCase()||"rtl"===(0,i.l)(c,"direction")),wrongRTL:"-webkit-box"===(0,i.l)(e,"display")}),!0}init(a){let b=this;if(b.initialized||!1===b.mount(a))return b;b.emit("beforeInit"),b.params.breakpoints&&b.setBreakpoint(),b.addClasses(),b.updateSize(),b.updateSlides(),b.params.watchOverflow&&b.checkOverflow(),b.params.grabCursor&&b.enabled&&b.setGrabCursor(),b.params.loop&&b.virtual&&b.params.virtual.enabled?b.slideTo(b.params.initialSlide+b.virtual.slidesBefore,0,b.params.runCallbacksOnInit,!1,!0):b.slideTo(b.params.initialSlide,0,b.params.runCallbacksOnInit,!1,!0),b.params.loop&&b.loopCreate(),b.attachEvents();let c=[...b.el.querySelectorAll('[loading="lazy"]')];return b.isElement&&c.push(...b.hostEl.querySelectorAll('[loading="lazy"]')),c.forEach(a=>{a.complete?k(b,a):a.addEventListener("load",a=>{k(b,a.target)})}),m(b),b.initialized=!0,m(b),b.emit("init"),b.emit("afterInit"),b}destroy(a,b){void 0===a&&(a=!0),void 0===b&&(b=!0);let c=this,{params:d,el:e,wrapperEl:f,slides:g}=c;return void 0===c.params||c.destroyed||(c.emit("beforeDestroy"),c.initialized=!1,c.detachEvents(),d.loop&&c.loopDestroy(),b&&(c.removeClasses(),e.removeAttribute("style"),f.removeAttribute("style"),g&&g.length&&g.forEach(a=>{a.classList.remove(d.slideVisibleClass,d.slideActiveClass,d.slideNextClass,d.slidePrevClass),a.removeAttribute("style"),a.removeAttribute("data-swiper-slide-index")})),c.emit("destroy"),Object.keys(c.eventsListeners).forEach(a=>{c.off(a)}),!1!==a&&(c.el.swiper=null,(0,i.r)(c)),c.destroyed=!0),null}static extendDefaults(a){(0,i.q)(B,a)}static get extendedDefaults(){return B}static get defaults(){return z}static installModule(a){C.prototype.__modules__||(C.prototype.__modules__=[]);let b=C.prototype.__modules__;"function"==typeof a&&0>b.indexOf(a)&&b.push(a)}static use(a){return Array.isArray(a)?a.forEach(a=>C.installModule(a)):C.installModule(a),C}}Object.keys(A).forEach(a=>{Object.keys(A[a]).forEach(b=>{C.prototype[b]=A[a][b]})}),C.use([function(a){let{swiper:b,on:c,emit:d}=a,e=(0,h.a)(),f=null,g=null,i=()=>{b&&!b.destroyed&&b.initialized&&(d("beforeResize"),d("resize"))},j=()=>{b&&!b.destroyed&&b.initialized&&d("orientationchange")};c("init",()=>{if(b.params.resizeObserver&&void 0!==e.ResizeObserver)return void(b&&!b.destroyed&&b.initialized&&(f=new ResizeObserver(a=>{g=e.requestAnimationFrame(()=>{let{width:c,height:d}=b,e=c,f=d;a.forEach(a=>{let{contentBoxSize:c,contentRect:d,target:g}=a;g&&g!==b.el||(e=d?d.width:(c[0]||c).inlineSize,f=d?d.height:(c[0]||c).blockSize)}),(e!==c||f!==d)&&i()})})).observe(b.el));e.addEventListener("resize",i),e.addEventListener("orientationchange",j)}),c("destroy",()=>{g&&e.cancelAnimationFrame(g),f&&f.unobserve&&b.el&&(f.unobserve(b.el),f=null),e.removeEventListener("resize",i),e.removeEventListener("orientationchange",j)})},function(a){let{swiper:b,extendParams:c,on:d,emit:e}=a,f=[],g=(0,h.a)(),j=function(a,c){void 0===c&&(c={});let d=new(g.MutationObserver||g.WebkitMutationObserver)(a=>{if(b.__preventObserver__)return;if(1===a.length)return void e("observerUpdate",a[0]);let c=function(){e("observerUpdate",a[0])};g.requestAnimationFrame?g.requestAnimationFrame(c):g.setTimeout(c,0)});d.observe(a,{attributes:void 0===c.attributes||c.attributes,childList:void 0===c.childList||c.childList,characterData:void 0===c.characterData||c.characterData}),f.push(d)};c({observer:!1,observeParents:!1,observeSlideChildren:!1}),d("init",()=>{if(b.params.observer){if(b.params.observeParents){let a=(0,i.a)(b.hostEl);for(let b=0;b<a.length;b+=1)j(a[b])}j(b.hostEl,{childList:b.params.observeSlideChildren}),j(b.wrapperEl,{attributes:!1})}}),d("destroy",()=>{f.forEach(a=>{a.disconnect()}),f.splice(0,f.length)})}]);let D=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function E(a){return"object"==typeof a&&null!==a&&a.constructor&&"Object"===Object.prototype.toString.call(a).slice(8,-1)&&!a.__swiper__}function F(a,b){let c=["__proto__","constructor","prototype"];Object.keys(b).filter(a=>0>c.indexOf(a)).forEach(c=>{void 0===a[c]?a[c]=b[c]:E(b[c])&&E(a[c])&&Object.keys(b[c]).length>0?b[c].__swiper__?a[c]=b[c]:F(a[c],b[c]):a[c]=b[c]})}function G(a){return void 0===a&&(a={}),a.navigation&&void 0===a.navigation.nextEl&&void 0===a.navigation.prevEl}function H(a){return void 0===a&&(a={}),a.pagination&&void 0===a.pagination.el}function I(a){return void 0===a&&(a={}),a.scrollbar&&void 0===a.scrollbar.el}function J(a){void 0===a&&(a="");let b=a.split(" ").map(a=>a.trim()).filter(a=>!!a),c=[];return b.forEach(a=>{0>c.indexOf(a)&&c.push(a)}),c.join(" ")}function K(){return(K=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function L(a){return a.type&&a.type.displayName&&a.type.displayName.includes("SwiperSlide")}function M(a,b){return"undefined"==typeof window?(0,g.useEffect)(a,b):(0,g.useLayoutEffect)(a,b)}let N=(0,g.createContext)(null),O=(0,g.createContext)(null),P=(0,g.forwardRef)(function(a,b){var c;let{className:d,tag:e="div",wrapperTag:f="div",children:h,onSwiper:i,...j}=void 0===a?{}:a,k=!1,[l,m]=(0,g.useState)("swiper"),[n,o]=(0,g.useState)(null),[p,q]=(0,g.useState)(!1),r=(0,g.useRef)(!1),s=(0,g.useRef)(null),t=(0,g.useRef)(null),u=(0,g.useRef)(null),v=(0,g.useRef)(null),w=(0,g.useRef)(null),x=(0,g.useRef)(null),y=(0,g.useRef)(null),A=(0,g.useRef)(null),{params:B,passedParams:N,rest:P,events:Q}=function(a,b){void 0===a&&(a={}),void 0===b&&(b=!0);let c={on:{}},d={},e={};F(c,z),c._emitClasses=!0,c.init=!1;let f={},g=D.map(a=>a.replace(/_/,""));return Object.keys(Object.assign({},a)).forEach(h=>{void 0!==a[h]&&(g.indexOf(h)>=0?E(a[h])?(c[h]={},e[h]={},F(c[h],a[h]),F(e[h],a[h])):(c[h]=a[h],e[h]=a[h]):0===h.search(/on[A-Z]/)&&"function"==typeof a[h]?b?d[`${h[2].toLowerCase()}${h.substr(3)}`]=a[h]:c.on[`${h[2].toLowerCase()}${h.substr(3)}`]=a[h]:f[h]=a[h])}),["navigation","pagination","scrollbar"].forEach(a=>{!0===c[a]&&(c[a]={}),!1===c[a]&&delete c[a]}),{params:c,passedParams:e,rest:f,events:d}}(j),{slides:R,slots:S}=function(a){let b=[],c={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return g.Children.toArray(a).forEach(a=>{if(L(a))b.push(a);else if(a.props&&a.props.slot&&c[a.props.slot])c[a.props.slot].push(a);else if(a.props&&a.props.children){let d=function a(b){let c=[];return g.Children.toArray(b).forEach(b=>{L(b)?c.push(b):b.props&&b.props.children&&a(b.props.children).forEach(a=>c.push(a))}),c}(a.props.children);d.length>0?d.forEach(a=>b.push(a)):c["container-end"].push(a)}else c["container-end"].push(a)}),{slides:b,slots:c}}(h),T=()=>{q(!p)};Object.assign(B.on,{_containerClasses(a,b){m(b)}});let U=()=>{Object.assign(B.on,Q),k=!0;let a={...B};if(delete a.wrapperClass,t.current=new C(a),t.current.virtual&&t.current.params.virtual.enabled){t.current.virtual.slides=R;let a={cache:!1,slides:R,renderExternal:o,renderExternalUpdate:!1};F(t.current.params.virtual,a),F(t.current.originalParams.virtual,a)}};return s.current||U(),t.current&&t.current.on("_beforeBreakpoint",T),(0,g.useEffect)(()=>()=>{t.current&&t.current.off("_beforeBreakpoint",T)}),(0,g.useEffect)(()=>{!r.current&&t.current&&(t.current.emitSlidesClasses(),r.current=!0)}),M(()=>{if(b&&(b.current=s.current),s.current)return t.current.destroyed&&U(),!function(a,b){let{el:c,nextEl:d,prevEl:e,paginationEl:f,scrollbarEl:g,swiper:h}=a;G(b)&&d&&e&&(h.params.navigation.nextEl=d,h.originalParams.navigation.nextEl=d,h.params.navigation.prevEl=e,h.originalParams.navigation.prevEl=e),H(b)&&f&&(h.params.pagination.el=f,h.originalParams.pagination.el=f),I(b)&&g&&(h.params.scrollbar.el=g,h.originalParams.scrollbar.el=g),h.init(c)}({el:s.current,nextEl:w.current,prevEl:x.current,paginationEl:y.current,scrollbarEl:A.current,swiper:t.current},B),i&&i(t.current),()=>{t.current&&!t.current.destroyed&&t.current.destroy(!0,!1)}},[]),M(()=>{!k&&Q&&t.current&&Object.keys(Q).forEach(a=>{t.current.on(a,Q[a])});let a=function(a,b,c,d,e){let f=[];if(!b)return f;let g=a=>{0>f.indexOf(a)&&f.push(a)};if(c&&d){let a=d.map(e),b=c.map(e);a.join("")!==b.join("")&&g("children"),d.length!==c.length&&g("children")}return D.filter(a=>"_"===a[0]).map(a=>a.replace(/_/,"")).forEach(c=>{if(c in a&&c in b)if(E(a[c])&&E(b[c])){let d=Object.keys(a[c]),e=Object.keys(b[c]);d.length!==e.length?g(c):(d.forEach(d=>{a[c][d]!==b[c][d]&&g(c)}),e.forEach(d=>{a[c][d]!==b[c][d]&&g(c)}))}else a[c]!==b[c]&&g(c)}),f}(N,u.current,R,v.current,a=>a.key);return u.current=N,v.current=R,a.length&&t.current&&!t.current.destroyed&&function(a){let b,c,d,e,f,g,h,i,{swiper:j,slides:k,passedParams:l,changedParams:m,nextEl:n,prevEl:o,scrollbarEl:p,paginationEl:q}=a,r=m.filter(a=>"children"!==a&&"direction"!==a&&"wrapperClass"!==a),{params:s,pagination:t,navigation:u,scrollbar:v,virtual:w,thumbs:x}=j;m.includes("thumbs")&&l.thumbs&&l.thumbs.swiper&&s.thumbs&&!s.thumbs.swiper&&(b=!0),m.includes("controller")&&l.controller&&l.controller.control&&s.controller&&!s.controller.control&&(c=!0),m.includes("pagination")&&l.pagination&&(l.pagination.el||q)&&(s.pagination||!1===s.pagination)&&t&&!t.el&&(d=!0),m.includes("scrollbar")&&l.scrollbar&&(l.scrollbar.el||p)&&(s.scrollbar||!1===s.scrollbar)&&v&&!v.el&&(e=!0),m.includes("navigation")&&l.navigation&&(l.navigation.prevEl||o)&&(l.navigation.nextEl||n)&&(s.navigation||!1===s.navigation)&&u&&!u.prevEl&&!u.nextEl&&(f=!0);let y=a=>{j[a]&&(j[a].destroy(),"navigation"===a?(j.isElement&&(j[a].prevEl.remove(),j[a].nextEl.remove()),s[a].prevEl=void 0,s[a].nextEl=void 0,j[a].prevEl=void 0,j[a].nextEl=void 0):(j.isElement&&j[a].el.remove(),s[a].el=void 0,j[a].el=void 0))};m.includes("loop")&&j.isElement&&(s.loop&&!l.loop?g=!0:!s.loop&&l.loop?h=!0:i=!0),r.forEach(a=>{if(E(s[a])&&E(l[a]))F(s[a],l[a]),("navigation"===a||"pagination"===a||"scrollbar"===a)&&"enabled"in l[a]&&!l[a].enabled&&y(a);else{let b=l[a];(!0===b||!1===b)&&("navigation"===a||"pagination"===a||"scrollbar"===a)?!1===b&&y(a):s[a]=l[a]}}),r.includes("controller")&&!c&&j.controller&&j.controller.control&&s.controller&&s.controller.control&&(j.controller.control=s.controller.control),m.includes("children")&&k&&w&&s.virtual.enabled&&(w.slides=k,w.update(!0)),m.includes("children")&&k&&s.loop&&(i=!0),b&&x.init()&&x.update(!0),c&&(j.controller.control=s.controller.control),d&&(j.isElement&&(!q||"string"==typeof q)&&((q=document.createElement("div")).classList.add("swiper-pagination"),q.part.add("pagination"),j.el.appendChild(q)),q&&(s.pagination.el=q),t.init(),t.render(),t.update()),e&&(j.isElement&&(!p||"string"==typeof p)&&((p=document.createElement("div")).classList.add("swiper-scrollbar"),p.part.add("scrollbar"),j.el.appendChild(p)),p&&(s.scrollbar.el=p),v.init(),v.updateSize(),v.setTranslate()),f&&(j.isElement&&(n&&"string"!=typeof n||((n=document.createElement("div")).classList.add("swiper-button-next"),n.innerHTML=j.hostEl.constructor.nextButtonSvg,n.part.add("button-next"),j.el.appendChild(n)),o&&"string"!=typeof o||((o=document.createElement("div")).classList.add("swiper-button-prev"),o.innerHTML=j.hostEl.constructor.prevButtonSvg,o.part.add("button-prev"),j.el.appendChild(o))),n&&(s.navigation.nextEl=n),o&&(s.navigation.prevEl=o),u.init(),u.update()),m.includes("allowSlideNext")&&(j.allowSlideNext=l.allowSlideNext),m.includes("allowSlidePrev")&&(j.allowSlidePrev=l.allowSlidePrev),m.includes("direction")&&j.changeDirection(l.direction,!1),(g||i)&&j.loopDestroy(),(h||i)&&j.loopCreate(),j.update()}({swiper:t.current,slides:R,passedParams:N,changedParams:a,nextEl:w.current,prevEl:x.current,scrollbarEl:A.current,paginationEl:y.current}),()=>{Q&&t.current&&Object.keys(Q).forEach(a=>{t.current.off(a,Q[a])})}}),M(()=>{var a;(a=t.current)&&!a.destroyed&&a.params.virtual&&(!a.params.virtual||a.params.virtual.enabled)&&(a.updateSlides(),a.updateProgress(),a.updateSlidesClasses(),a.parallax&&a.params.parallax&&a.params.parallax.enabled&&a.parallax.setTranslate())},[n]),g.createElement(e,K({ref:s,className:J(`${l}${d?` ${d}`:""}`)},P),g.createElement(O.Provider,{value:t.current},S["container-start"],g.createElement(f,{className:(void 0===(c=B.wrapperClass)&&(c=""),c)?c.includes("swiper-wrapper")?c:`swiper-wrapper ${c}`:"swiper-wrapper"},S["wrapper-start"],B.virtual?function(a,b,c){if(!c)return null;let d=a=>{let c=a;return a<0?c=b.length+a:c>=b.length&&(c-=b.length),c},e=a.isHorizontal()?{[a.rtlTranslate?"right":"left"]:`${c.offset}px`}:{top:`${c.offset}px`},{from:f,to:h}=c,i=a.params.loop?-b.length:0,j=a.params.loop?2*b.length:b.length,k=[];for(let a=i;a<j;a+=1)a>=f&&a<=h&&k.push(b[d(a)]);return k.map((b,c)=>g.cloneElement(b,{swiper:a,style:e,key:`slide-${c}`}))}(t.current,R,n):R.map((a,b)=>g.cloneElement(a,{swiper:t.current,swiperSlideIndex:b})),S["wrapper-end"]),G(B)&&g.createElement(g.Fragment,null,g.createElement("div",{ref:x,className:"swiper-button-prev"}),g.createElement("div",{ref:w,className:"swiper-button-next"})),I(B)&&g.createElement("div",{ref:A,className:"swiper-scrollbar"}),H(B)&&g.createElement("div",{ref:y,className:"swiper-pagination"}),S["container-end"]))});P.displayName="Swiper";let Q=(0,g.forwardRef)(function(a,b){let{tag:c="div",children:d,className:e="",swiper:f,zoom:h,lazy:i,virtualIndex:j,swiperSlideIndex:k,...l}=void 0===a?{}:a,m=(0,g.useRef)(null),[n,o]=(0,g.useState)("swiper-slide"),[p,q]=(0,g.useState)(!1);function r(a,b,c){b===m.current&&o(c)}M(()=>{if(void 0!==k&&(m.current.swiperSlideIndex=k),b&&(b.current=m.current),m.current&&f){if(f.destroyed){"swiper-slide"!==n&&o("swiper-slide");return}return f.on("_slideClass",r),()=>{f&&f.off("_slideClass",r)}}}),M(()=>{f&&m.current&&!f.destroyed&&o(f.getSlideClasses(m.current))},[f]);let s={isActive:n.indexOf("swiper-slide-active")>=0,isVisible:n.indexOf("swiper-slide-visible")>=0,isPrev:n.indexOf("swiper-slide-prev")>=0,isNext:n.indexOf("swiper-slide-next")>=0},t=()=>"function"==typeof d?d(s):d;return g.createElement(c,K({ref:m,className:J(`${n}${e?` ${e}`:""}`),"data-swiper-slide-index":j,onLoad:()=>{q(!0)}},l),h&&g.createElement(N.Provider,{value:s},g.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof h?h:void 0},t(),i&&!p&&g.createElement("div",{className:"swiper-lazy-preloader"}))),!h&&g.createElement(N.Provider,{value:s},t(),i&&!p&&g.createElement("div",{className:"swiper-lazy-preloader"})))});Q.displayName="SwiperSlide"},33544:(a,b,c)=>{"use strict";c.d(b,{a:()=>r,c:()=>m,d:()=>g,e:()=>l,f:()=>s,g:()=>q,h:()=>h,l:()=>p,m:()=>o,n:()=>f,o:()=>n,p:()=>k,q:()=>function a(){let b=Object(arguments.length<=0?void 0:arguments[0]),c=["__proto__","constructor","prototype"];for(let d=1;d<arguments.length;d+=1){let e=d<0||arguments.length<=d?void 0:arguments[d];if(null!=e&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(e instanceof HTMLElement):!e||1!==e.nodeType&&11!==e.nodeType)){let d=Object.keys(Object(e)).filter(a=>0>c.indexOf(a));for(let c=0,f=d.length;c<f;c+=1){let f=d[c],g=Object.getOwnPropertyDescriptor(e,f);void 0!==g&&g.enumerable&&(i(b[f])&&i(e[f])?e[f].__swiper__?b[f]=e[f]:a(b[f],e[f]):!i(b[f])&&i(e[f])?(b[f]={},e[f].__swiper__?b[f]=e[f]:a(b[f],e[f])):b[f]=e[f])}}}return b},r:()=>e,s:()=>j});var d=c(49863);function e(a){Object.keys(a).forEach(b=>{try{a[b]=null}catch(a){}try{delete a[b]}catch(a){}})}function f(a,b){return void 0===b&&(b=0),setTimeout(a,b)}function g(){return Date.now()}function h(a,b){let c,e,f;void 0===b&&(b="x");let g=(0,d.a)(),h=function(a){let b,c=(0,d.a)();return c.getComputedStyle&&(b=c.getComputedStyle(a,null)),!b&&a.currentStyle&&(b=a.currentStyle),b||(b=a.style),b}(a);return g.WebKitCSSMatrix?((e=h.transform||h.webkitTransform).split(",").length>6&&(e=e.split(", ").map(a=>a.replace(",",".")).join(", ")),f=new g.WebKitCSSMatrix("none"===e?"":e)):c=(f=h.MozTransform||h.OTransform||h.MsTransform||h.msTransform||h.transform||h.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===b&&(e=g.WebKitCSSMatrix?f.m41:16===c.length?parseFloat(c[12]):parseFloat(c[4])),"y"===b&&(e=g.WebKitCSSMatrix?f.m42:16===c.length?parseFloat(c[13]):parseFloat(c[5])),e||0}function i(a){return"object"==typeof a&&null!==a&&a.constructor&&"Object"===Object.prototype.toString.call(a).slice(8,-1)}function j(a,b,c){a.style.setProperty(b,c)}function k(a){let b,{swiper:c,targetPosition:e,side:f}=a,g=(0,d.a)(),h=-c.translate,i=null,j=c.params.speed;c.wrapperEl.style.scrollSnapType="none",g.cancelAnimationFrame(c.cssModeFrameID);let k=e>h?"next":"prev",l=(a,b)=>"next"===k&&a>=b||"prev"===k&&a<=b,m=()=>{b=new Date().getTime(),null===i&&(i=b);let a=h+(.5-Math.cos(Math.max(Math.min((b-i)/j,1),0)*Math.PI)/2)*(e-h);if(l(a,e)&&(a=e),c.wrapperEl.scrollTo({[f]:a}),l(a,e)){c.wrapperEl.style.overflow="hidden",c.wrapperEl.style.scrollSnapType="",setTimeout(()=>{c.wrapperEl.style.overflow="",c.wrapperEl.scrollTo({[f]:a})}),g.cancelAnimationFrame(c.cssModeFrameID);return}c.cssModeFrameID=g.requestAnimationFrame(m)};m()}function l(a,b){return void 0===b&&(b=""),[...a.children].filter(a=>a.matches(b))}function m(a,b){void 0===b&&(b=[]);let c=document.createElement(a);return c.classList.add(...Array.isArray(b)?b:[b]),c}function n(a,b){let c=[];for(;a.previousElementSibling;){let d=a.previousElementSibling;b?d.matches(b)&&c.push(d):c.push(d),a=d}return c}function o(a,b){let c=[];for(;a.nextElementSibling;){let d=a.nextElementSibling;b?d.matches(b)&&c.push(d):c.push(d),a=d}return c}function p(a,b){return(0,d.a)().getComputedStyle(a,null).getPropertyValue(b)}function q(a){let b,c=a;if(c){for(b=0;null!==(c=c.previousSibling);)1===c.nodeType&&(b+=1);return b}}function r(a,b){let c=[],d=a.parentElement;for(;d;)b?d.matches(b)&&c.push(d):c.push(d),d=d.parentElement;return c}function s(a,b,c){let e=(0,d.a)();return c?a["width"===b?"offsetWidth":"offsetHeight"]+parseFloat(e.getComputedStyle(a,null).getPropertyValue("width"===b?"margin-right":"margin-top"))+parseFloat(e.getComputedStyle(a,null).getPropertyValue("width"===b?"margin-left":"margin-bottom")):a.offsetWidth}},33873:a=>{"use strict";a.exports=require("path")},34232:(a,b,c)=>{Promise.resolve().then(c.bind(c,87488)),Promise.resolve().then(c.bind(c,39710)),Promise.resolve().then(c.bind(c,80994))},39710:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/FacebookMSG.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/FacebookMSG.js","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45180:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/About.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/About.js","default")},49050:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(77618);function i(){let[a,b]=(0,e.useState)(!1),c=(0,h.c3)("ConsentCookies");return a?(0,d.jsx)("div",{className:"cookies-consent2",children:(0,d.jsxs)("div",{className:"cookies-consent__message",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:c("message")})," ",(0,d.jsx)("span",{children:c("moreInfo")})," ",(0,d.jsx)(g(),{href:"/privacy-policy",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)("span",{children:c("privacyPolicy")})}),(0,d.jsx)("span",{children:c("and")}),(0,d.jsx)(g(),{href:"/cookies-policy",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)("span",{children:c("cookiePolicy")})}),(0,d.jsx)("span",{children:c("period")})]}),(0,d.jsx)("button",{onClick:()=>{localStorage.setItem("cookies-consent","true"),b(!1)},className:"cookies-consent__button cookies-consent__button--accept",children:(0,d.jsx)("span",{children:c("accept")})})]})}):null}c(25208)},49863:(a,b,c)=>{"use strict";function d(a){return null!==a&&"object"==typeof a&&"constructor"in a&&a.constructor===Object}function e(a,b){void 0===a&&(a={}),void 0===b&&(b={}),Object.keys(b).forEach(c=>{void 0===a[c]?a[c]=b[c]:d(b[c])&&d(a[c])&&Object.keys(b[c]).length>0&&e(a[c],b[c])})}c.d(b,{a:()=>i,g:()=>g});let f={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function g(){let a="undefined"!=typeof document?document:{};return e(a,f),a}let h={document:f,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:a=>"undefined"==typeof setTimeout?(a(),null):setTimeout(a,0),cancelAnimationFrame(a){"undefined"!=typeof setTimeout&&clearTimeout(a)}};function i(){let a="undefined"!=typeof window?window:{};return e(a,h),a}},50342:(a,b,c)=>{Promise.resolve().then(c.bind(c,96268)),Promise.resolve().then(c.bind(c,33856)),Promise.resolve().then(c.bind(c,45196))},51061:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(37413),e=c(42024),f=c(92118),g=c(52863);let{Link:h,redirect:i,usePathname:j,useRouter:k,getPathname:l}=(0,f.A)(g.D);function m(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(e.default,{headerStyle:3,footerStyle:4,breadcrumbTitle:"Page Not Found",children:(0,d.jsx)("section",{className:"error-page",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"error-page__inner text-center",children:[(0,d.jsx)("div",{className:"error-page__img float-bob-y",children:(0,d.jsx)("img",{src:"/assets/images/resources/error-page-img1.png",alt:""})}),(0,d.jsxs)("div",{className:"error-page__content",children:[(0,d.jsx)("h2",{children:"Oops! Page Not Found!"}),(0,d.jsx)("p",{children:"The page you are looking for does not exist. It might have been moved or deleted."}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(h,{className:"thm-btn",href:"/",children:["Back To Home",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})})})})})}},51494:(a,b,c)=>{Promise.resolve().then(c.bind(c,96268)),Promise.resolve().then(c.bind(c,10196)),Promise.resolve().then(c.bind(c,91165)),Promise.resolve().then(c.bind(c,58182)),Promise.resolve().then(c.bind(c,71364)),Promise.resolve().then(c.bind(c,22346)),Promise.resolve().then(c.bind(c,76212)),Promise.resolve().then(c.bind(c,83746))},52863:(a,b,c)=>{"use strict";c.d(b,{D:()=>d});let d=(0,c(55946).A)({locales:["th","en","ru","ch"],defaultLocale:"th"})},55866:()=>{},58182:(a,b,c)=>{"use strict";c.d(b,{default:()=>p});var d=c(60687),e=c(43210),f=c(6042),g=c(32036);c(2822),c(55866);var h=c(77618),i=c(85814),j=c.n(i);function k(){let a=(0,h.c3)("BranchData"),[b,c]=(0,e.useState)(!1);return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"pricing-two__single",children:(0,d.jsxs)("div",{className:"pricing-two__single-inner",children:[(0,d.jsxs)("div",{className:"table-header",children:[(0,d.jsx)("div",{className:"img-box",children:(0,d.jsx)("img",{src:"assets/images/branch/store_1.png",alt:""})}),(0,d.jsx)("div",{className:"title-box text-center",children:(0,d.jsx)("h2",{children:a("branch_1")})})]}),(0,d.jsx)("div",{className:"table-content",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-map"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:a("add_1")})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-phone-alt"})}),(0,d.jsx)("div",{className:"text-box",children:b?(0,d.jsx)(j(),{href:`tel:${a("phone_1")}`,children:a("phone_1")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{c(!b)},children:[a("phone_1").slice(0,7)," ****"]})})]})]})})]})})})}function l(){let a=(0,h.c3)("BranchData"),[b,c]=(0,e.useState)(!1);return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"pricing-one__single",children:(0,d.jsxs)("div",{className:"pricing-one__single-inner",children:[(0,d.jsxs)("div",{className:"table-header",children:[(0,d.jsx)("div",{className:"img-box",children:(0,d.jsx)("img",{src:"assets/images/branch/store_2.png",alt:""})}),(0,d.jsx)("div",{className:"title-box text-center",children:(0,d.jsx)("h2",{children:a("branch_2")})})]}),(0,d.jsx)("div",{className:"table-content",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-map"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:a("add_2")})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-phone-alt"})}),(0,d.jsx)("div",{className:"text-box",children:b?(0,d.jsx)(j(),{href:`tel:${a("phone_2")}`,children:a("phone_2")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{c(!b)},children:[a("phone_2").slice(0,7)," ****"]})})]})]})})]})})})}function m(){let a=(0,h.c3)("BranchData"),[b,c]=(0,e.useState)(!1),[f,g]=(0,e.useState)(!1);return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"pricing-two__single",children:(0,d.jsxs)("div",{className:"pricing-two__single-inner",children:[(0,d.jsxs)("div",{className:"table-header",children:[(0,d.jsx)("div",{className:"img-box",children:(0,d.jsx)("img",{src:"assets/images/branch/store_3.png",alt:""})}),(0,d.jsx)("div",{className:"title-box text-center",children:(0,d.jsx)("h2",{children:a("branch_3")})})]}),(0,d.jsx)("div",{className:"table-content",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-map"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:a("add_3")})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-phone-alt"})}),(0,d.jsxs)("div",{className:"text-box",children:[b?(0,d.jsx)(j(),{href:`tel:${a("phone_3")}`,children:a("phone_3")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{c(!b)},children:[a("phone_3").slice(0,7)," ****"]}),","," ",f?(0,d.jsx)(j(),{href:`tel:${a("mobile_3")}`,children:a("mobile_3")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{g(!f)},children:[a("mobile_3").slice(0,7)," ****"]})]})]})]})})]})})})}function n(){let a=(0,h.c3)("BranchData"),[b,c]=(0,e.useState)(!1),[f,g]=(0,e.useState)(!1);return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"pricing-one__single",children:(0,d.jsxs)("div",{className:"pricing-one__single-inner",children:[(0,d.jsxs)("div",{className:"table-header",children:[(0,d.jsx)("div",{className:"img-box",children:(0,d.jsx)("img",{src:"assets/images/branch/store_4.png",alt:""})}),(0,d.jsx)("div",{className:"title-box text-center",children:(0,d.jsx)("h2",{children:a("branch_4")})})]}),(0,d.jsx)("div",{className:"table-content",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-map"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:a("add_4")})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-phone-alt"})}),(0,d.jsxs)("div",{className:"text-box",children:[b?(0,d.jsx)(j(),{href:`tel:${a("phone_4")}`,children:a("phone_4")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{c(!b)},children:[a("phone_4").slice(0,7)," ****"]}),","," ",f?(0,d.jsx)(j(),{href:`tel:${a("mobile_4")}`,children:a("mobile_4")}):(0,d.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{g(!f)},children:[a("mobile_4").slice(0,7)," ****"]})]})]})]})})]})})})}let o={modules:[f.Ij,f.Vx],slidesPerView:3,spaceBetween:30,loop:!0,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},breakpoints:{320:{slidesPerView:1},575:{slidesPerView:1},767:{slidesPerView:2},991:{slidesPerView:2},1199:{slidesPerView:2},1350:{slidesPerView:2}}};function p(){let a=(0,h.c3)("BranchArea");return(0,h.c3)("BranchData"),(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("section",{className:"branch-one",id:"branch",children:[(0,d.jsx)("div",{className:"pricing-one__pattern",style:{backgroundImage:"url(assets/images/pattern/pricing-v1-pattern.png)"}}),(0,d.jsxs)("div",{className:"container",children:[(0,d.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,d.jsxs)("div",{className:"sec-title__tagline",children:[(0,d.jsx)("div",{className:"line"}),(0,d.jsx)("div",{className:"text tg-element-title",children:(0,d.jsx)("h4",{children:a("title")})}),(0,d.jsx)("div",{className:"line2"})]}),(0,d.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[a("sub_title1")," ",(0,d.jsx)("br",{})," ",a("sub_title2")," ",(0,d.jsx)("span",{children:a("sub_title3")})]})]}),(0,d.jsx)("div",{className:"row justify-content-center",children:(0,d.jsxs)("div",{className:"col-xl-9 col-lg-9 col-md-12 col-sm-8",children:[(0,d.jsxs)(g.RC,{...o,className:"swiper swiper-initialized swiper-horizontal service-one__carousel owl-carousel owl-theme owl-dot-style1 swiper-backface-hidden",children:[(0,d.jsx)(g.qr,{style:{cursor:"pointer"},children:(0,d.jsx)("div",{className:"wow fadeInUp","data-wow-delay":".3s",children:(0,d.jsx)(k,{})})}),(0,d.jsx)(g.qr,{style:{cursor:"pointer"},children:(0,d.jsx)("div",{className:"wow fadeInDown","data-wow-delay":".3s",children:(0,d.jsx)(l,{})})}),(0,d.jsx)(g.qr,{style:{cursor:"pointer"},children:(0,d.jsx)("div",{className:"wow fadeInUp","data-wow-delay":".3s",children:(0,d.jsx)(m,{})})}),(0,d.jsx)(g.qr,{style:{cursor:"pointer"},children:(0,d.jsx)("div",{className:"wow fadeInDown","data-wow-delay":".3s",children:(0,d.jsx)(n,{})})})]}),(0,d.jsx)("div",{className:"swiper-button-prev"}),(0,d.jsx)("div",{className:"swiper-button-next"})]})})]})]})})}},61250:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Service.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Service.js","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66159:()=>{},66730:()=>{},68798:(a,b,c)=>{Promise.resolve().then(c.bind(c,42024)),Promise.resolve().then(c.bind(c,45180)),Promise.resolve().then(c.bind(c,92711)),Promise.resolve().then(c.bind(c,11433)),Promise.resolve().then(c.bind(c,5934)),Promise.resolve().then(c.bind(c,95951)),Promise.resolve().then(c.bind(c,61250)),Promise.resolve().then(c.bind(c,80692))},69080:(a,b,c)=>{Promise.resolve().then(c.bind(c,49050)),Promise.resolve().then(c.bind(c,97516)),Promise.resolve().then(c.bind(c,45196))},71364:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c(77618);function i(){let a=(0,h.c3)("HeaderBtn"),b=(0,h.c3)("ContactArea"),c=(0,h.c3)("InputForm"),[e,i]=(0,g.useState)(""),[j,k]=(0,g.useState)(""),[l,m]=(0,g.useState)(""),[n,o]=(0,g.useState)(""),[p,q]=(0,g.useState)(""),[r,s]=(0,g.useState)(!1),[t,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(null),[x,y]=(0,g.useState)(null),[z,A]=(0,g.useState)(null),[B,C]=(0,g.useState)(null),[D,E]=(0,g.useState)(null),[F,G]=(0,g.useState)(null),H=async a=>{a.preventDefault(),s(!0),w(null);let b=!1;if(e?y(null):(y("Name is required."),b=!0),j?A(null):(A("Email is required."),b=!0),l?C(null):(C("Phone No. is required."),b=!0),n?E(null):(E("Line ID is required."),b=!0),p?G(null):(G("Message is required."),b=!0),b)return void s(!1);let c=new FormData;c.append("name",e),c.append("email",j),c.append("phone",l),c.append("idline",n),c.append("massege",p);try{let a=await fetch("https://script.google.com/macros/s/AKfycbxvM-rtnmkhW3XpEk1HC9Vfb6cLwv_boDpDT10wImaRh-CUI3hiLWZGla4h0BMwZp9_/exec",{method:"POST",body:c});if(!a.ok){let b=await a.text();throw Error(`HTTP error! status: ${a.status}, message: ${b}`)}u(!0),i(""),k(""),m(""),o(""),q("")}catch(a){console.error("Submission failed! Please try again.",a),w(a.message),u(!1)}finally{s(!1)}};return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("section",{className:"contact-one",id:"contact",children:[(0,d.jsx)("div",{className:"contact-one__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/Contact-Us BG-pattern-01.png",alt:""})}),(0,d.jsx)("div",{className:"shape1 float-bob-y",children:(0,d.jsx)("img",{src:"assets/images/shapes/why-choose-v1-shape1.png",alt:""})}),(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-6",children:(0,d.jsxs)("div",{className:"contact-one__content",children:[(0,d.jsxs)("div",{className:"sec-title tg-heading-subheading animation-style2",children:[(0,d.jsxs)("div",{className:"sec-title__tagline",children:[(0,d.jsx)("div",{className:"line"}),(0,d.jsx)("div",{className:"text tg-element-title",children:(0,d.jsx)("h4",{children:b("title")})}),(0,d.jsx)("div",{className:"line2"})]}),(0,d.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[b("sub_title1")," ",(0,d.jsxs)("span",{children:[" ",b("sub_title2")," "]})," ",b("sub_title3")," ",(0,d.jsxs)("span",{children:[" ",b("sub_title4")," "]})," ",b("sub_title5")]})]}),(0,d.jsx)("div",{className:"contact-one__title-box",children:(0,d.jsx)("h4",{children:b("header")})}),(0,d.jsx)("div",{className:"contact-one__content-list",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-phone"}),b("sub_header1")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-phone",style:{opacity:"0"}})," ",(0,d.jsx)("span",{className:"text-box",children:(0,d.jsx)(f(),{href:`tel:${a("phone_no")}`,children:a("phone_no")})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-envelope-open-text"})," ",b("sub_header2")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-phone",style:{opacity:"0"}})," ",(0,d.jsx)("span",{className:"text-box",children:(0,d.jsx)(f(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-share-alt"})," ",b("sub_header3")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{className:"fa fa-share-alt",style:{opacity:"0"}})," ",(0,d.jsxs)("span",{className:"social-list",style:{paddingLeft:"0.06em"},children:[(0,d.jsx)(f(),{className:"fab fa-facebook-square",href:"https://www.facebook.com/Sakwwth",target:"_blank"}),(0,d.jsx)(f(),{className:"fab fa-youtube",href:"https://www.youtube.com/@sakwoodworks",target:"_blank"}),(0,d.jsx)(f(),{className:"fab fa-tiktok",href:"https://www.tiktok.com/@sakwoodworks",target:"_blank"}),(0,d.jsx)(f(),{className:"fab fa-instagram",href:"https://www.instagram.com/sakwoodworks",target:"_blank"}),(0,d.jsx)(f(),{className:"fab fa-line",href:"https://lin.ee/smwoT3j",target:"_blank"})]})]})]})]})})]})}),(0,d.jsx)("div",{className:"col-xl-6",children:(0,d.jsxs)("div",{className:"contact-one__form-box wow fadeInRight","data-wow-delay":"0ms","data-wow-duration":"1500ms",children:[(0,d.jsxs)("div",{className:"title-box",children:[(0,d.jsxs)("h2",{children:[" ",c("title")," "]}),(0,d.jsxs)("p",{children:[" ",c("desc")," "]})]}),(0,d.jsx)("form",{className:"contact-form-validated why-choose-one__form",onSubmit:H,children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-3",children:[(0,d.jsxs)("div",{className:"input-box",children:[(0,d.jsx)("input",{type:"text",placeholder:`${c("name")}`,value:e,onChange:a=>i(a.target.value),required:""}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-user"})})]}),x&&(0,d.jsx)("p",{className:"error-message",children:x})]}),(0,d.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-4",children:[(0,d.jsxs)("div",{className:"input-box",children:[(0,d.jsx)("input",{type:"email",placeholder:`${c("email")}`,required:"",value:j,onChange:a=>k(a.target.value)}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})})]}),z&&(0,d.jsx)("p",{className:"error-message",children:z})]}),(0,d.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-4",children:[(0,d.jsxs)("div",{className:"input-box",children:[(0,d.jsx)("input",{type:"text",placeholder:`${c("phone")}`,required:"",value:l,onChange:a=>m(a.target.value)}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone2"})})]}),B&&(0,d.jsx)("p",{className:"error-message",children:B})]}),(0,d.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-3",children:[(0,d.jsxs)("div",{className:"input-box",children:[(0,d.jsx)("input",{type:"text",placeholder:`${c("idline")}`,required:"",value:n,onChange:a=>o(a.target.value)}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fab fa-line"})})]}),D&&(0,d.jsx)("p",{className:"error-message",children:D})]}),(0,d.jsxs)("div",{className:"col-xl-12 mb-3",children:[(0,d.jsxs)("div",{className:"input-box",children:[(0,d.jsx)("textarea",{placeholder:`${c("massege")}`,value:p,onChange:a=>q(a.target.value)}),(0,d.jsx)("div",{className:"icon style2",children:(0,d.jsx)("span",{className:"icon-pen"})})]}),F&&(0,d.jsx)("p",{className:"error-message",children:F})]}),(0,d.jsx)("div",{className:"col-xl-12",children:(0,d.jsxs)("div",{className:"why-choose-one__form-btn",children:[(0,d.jsxs)("button",{type:"submit",className:"thm-btn",disabled:r,children:[r?"Submitting...":`${c("submit")}`,(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]}),t&&(0,d.jsx)("p",{style:{color:"green"},children:c("success")}),v&&(0,d.jsx)("p",{className:"error-message",children:c("error")})]})})]})})]})})]})})]})})}c(70189)},72920:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,88920)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/page.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,92529)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/layout.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,51061)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/not-found.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,32648)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/layout.js"],loading:[()=>Promise.resolve().then(c.bind(c,24793)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/loading.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,31528)),"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/not-found.js"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/app/[locale]/page.js"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},72996:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(35471),e=c(14967),f=c(52863);let g=(0,d.A)(async({requestLocale:a})=>{let b=await a,d=(0,e.EL)(f.D.locales,b)?b:f.D.defaultLocale;return{locale:d,messages:(await c(76565)(`./${d}.json`)).default}})},76212:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687);c(85814);var e=c(6042),f=c(32036),g=c(77618);let h={modules:[e.Ij,e.dK,e.Vx],slidesPerView:3,spaceBetween:30,autoplay:{delay:6e3,disableOnInteraction:!1},loop:!0,navigation:{nextEl:".h1n",prevEl:".h1p"},pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{320:{slidesPerView:1},575:{slidesPerView:1},767:{slidesPerView:2},991:{slidesPerView:2},1199:{slidesPerView:3},1350:{slidesPerView:3}}};function i(){let a=(0,g.c3)("ServicesArea"),b=(0,g.c3)("ServicesCard");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("section",{className:"service-one",id:"service",children:[(0,d.jsx)("div",{className:"service-one__pattern",style:{backgroundImage:"url(assets/images/pattern/service-v1-pattern.jpg)"}}),(0,d.jsxs)("div",{className:"container",children:[(0,d.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,d.jsxs)("div",{className:"sec-title__tagline",children:[(0,d.jsx)("div",{className:"line"}),(0,d.jsxs)("div",{className:"text tg-element-title",children:[(0,d.jsx)("h4",{children:a("title")}),""]}),(0,d.jsx)("div",{className:"line2"})]}),(0,d.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[a("sub_title1"),(0,d.jsx)("br",{}),(0,d.jsx)("span",{children:a("sub_title2")})," ",a("sub_title3")]})]}),(0,d.jsx)("div",{className:"row",children:(0,d.jsx)("div",{className:"",children:(0,d.jsxs)(f.RC,{...h,className:"service-one__carousel owl-carousel owl-theme owl-dot-style1",children:[(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-Delivery-img.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service1.title")}),(0,d.jsx)("p",{children:b("service1.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsxs)("span",{children:[" ",(0,d.jsx)("img",{src:"assets/images/services/Delivery.svg"})]})})]})}),(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-Packing-img2.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service2.title")}),(0,d.jsx)("p",{children:b("service2.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsxs)("span",{children:[" ",(0,d.jsx)("img",{src:"assets/images/services/Packing.svg"})]})})]})}),(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-CCA-img2.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service3.title")}),(0,d.jsx)("p",{children:b("service3.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsxs)("span",{children:[" ",(0,d.jsx)("img",{src:"assets/images/services/CCA.svg"})]})})]})}),(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-Wholesale-img4.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service4.title")}),(0,d.jsx)("p",{children:b("service4.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsxs)("span",{children:[" ",(0,d.jsx)("img",{src:"assets/images/services/Wholesale-service.svg"})]})})]})}),(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-Cutting-img3.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service5.title")}),(0,d.jsx)("p",{children:b("service5.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsxs)("span",{children:[" ",(0,d.jsx)("img",{src:"assets/images/services/Cutting.svg"})]})})]})}),(0,d.jsx)(f.qr,{style:{cursor:"pointer"},children:(0,d.jsxs)("div",{className:"service-one__single",children:[(0,d.jsxs)("div",{className:"service-one__single-inner",children:[(0,d.jsx)("div",{className:"service-one__single-img",children:(0,d.jsx)("img",{src:"assets/images/services/service-Get Advice-img3.png",alt:"#"})}),(0,d.jsxs)("div",{className:"service-one__single-content",children:[(0,d.jsx)("h2",{children:b("service6.title")}),(0,d.jsx)("p",{children:b("service6.sm_des")}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)("a",{children:[(0,d.jsx)("span",{className:"icon-right-arrow",children:" "}),b("swipe")," ",(0,d.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{children:(0,d.jsx)("img",{src:"assets/images/services/Advice.svg"})})})]})})]})})})]})]})})}},76565:(a,b,c)=>{var d={"./ch.json":[17852,852],"./en.json":[87368,368],"./ru.json":[71270,270],"./th.json":[88599,599]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,19))}e.keys=()=>Object.keys(d),e.id=76565,a.exports=e},80692:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Store.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Store.js","default")},83746:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(60687),e=c(85814),f=c.n(e),g=c(6042),h=c(32036),i=c(77618);let j={modules:[g.Ij,g.dK,g.Vx],slidesPerView:6,spaceBetween:30,autoplay:{delay:0,disableOnInteraction:!1},speed:4e3,loop:!0,navigation:{nextEl:".srn",prevEl:".srp"},pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{320:{slidesPerView:2},575:{slidesPerView:3},767:{slidesPerView:4},991:{slidesPerView:4},1199:{slidesPerView:5},1350:{slidesPerView:6}}};function k(){let a=(0,i.c3)("BrandArea"),b=(0,i.c3)("BrandData");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("section",{className:"store-one",id:"store",children:[(0,d.jsx)("div",{className:"store-one__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/store-pattern.png",alt:""})}),(0,d.jsxs)("div",{className:"container",children:[(0,d.jsx)("div",{className:"row",children:(0,d.jsx)("div",{className:"why-choose-one__content",children:(0,d.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,d.jsxs)("div",{className:"sec-title__tagline",children:[(0,d.jsx)("div",{className:"line"}),(0,d.jsx)("div",{className:"text tg-element-title",children:(0,d.jsx)("h4",{children:a("title")})}),(0,d.jsx)("div",{className:"line2"})]}),(0,d.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[a("sub_title1")," ",(0,d.jsx)("br",{})," ",(0,d.jsxs)("span",{children:[" ",a("sub_title2")," "]})," ",a("sub_title3")," ",(0,d.jsxs)("span",{children:[" ",a("sub_title4")," "]})]})]})})}),(0,d.jsx)("div",{className:"row"}),(0,d.jsxs)(h.RC,{...j,className:"brand-one__carousel owl-carousel owl-theme",children:[(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://shopee.co.th/sakwoodworks",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand01.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_1")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.thaiwatsadu.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand04.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_4")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.lazada.co.th/shop/sak-woodworks/",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand02.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_2")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.homepro.co.th/search?searchtype=&q=sak+woodworks",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand03.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_3")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://nocnoc.com/sl/SAK-WoodWorks/267885?area=pdp-sellerProfile-10409699",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand05.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_5")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.bnbhome.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand06.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_6")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"http://th1268030936ctqi.trustpass.alibaba.com",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand07.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_7")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"#",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand08.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_8")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://shopee.co.th/sakwoodworks",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand01.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_1")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.thaiwatsadu.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand04.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_4")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.lazada.co.th/shop/sak-woodworks/",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand02.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_2")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.homepro.co.th/search?searchtype=&q=sak+woodworks",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand03.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_3")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://nocnoc.com/sl/SAK-WoodWorks/267885?area=pdp-sellerProfile-10409699",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand05.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_5")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"https://www.bnbhome.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand06.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_6")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"http://th1268030936ctqi.trustpass.alibaba.com",target:"_blank",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand07.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_7")})})]})})})}),(0,d.jsx)(h.qr,{children:(0,d.jsx)("div",{className:"brand-one__single text-center",children:(0,d.jsx)("div",{className:"brand-one__single-inner",children:(0,d.jsxs)(f(),{href:"#",children:[(0,d.jsx)("div",{className:"brand-one__image",children:(0,d.jsx)("img",{src:"assets/images/brand/brand08.svg",alt:""})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:b("brand_8")})})]})})})})]})]})]})})}},83998:(a,b,c)=>{Promise.resolve().then(c.bind(c,42024)),Promise.resolve().then(c.bind(c,75788)),Promise.resolve().then(c.bind(c,80994))},85129:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87488:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/CookiesConsent.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/CookiesConsent.js","default")},88920:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(37413),e=c(42024),f=c(92711),g=c(45180),h=c(61250),i=c(11433),j=c(95951),k=c(80692),l=c(5934);function m(){return(0,d.jsx)(e.default,{headerStyle:3,footerStyle:4,children:(0,d.jsxs)("div",{className:"main-content",children:[(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{}),(0,d.jsx)(j.default,{}),(0,d.jsx)(h.default,{}),(0,d.jsx)(i.default,{}),(0,d.jsx)(k.default,{}),(0,d.jsx)(l.default,{})]})})}},89112:()=>{},91165:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687);c(85814);var e=c(6042),f=c(32036),g=c(77618);let h={modules:[e.Ij,e.dK,e.Vx],slidesPerView:1,spaceBetween:0,autoplay:{delay:8e3,disableOnInteraction:!1},loop:!0,navigation:{nextEl:".h1n",prevEl:".h1p"},pagination:{el:".swiper-pagination",clickable:!0}};function i(){let a=(0,g.c3)("HeroTitle"),b=(0,g.c3)("HeroSlider");return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("section",{className:"slider-one",children:(0,d.jsx)("div",{className:"",children:(0,d.jsxs)(f.RC,{...h,className:"slider-one__carousel owl-carousel owl-theme",children:[(0,d.jsx)(f.qr,{children:(0,d.jsxs)("div",{className:"slider-one__single",children:[(0,d.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner1.png)"}}),(0,d.jsx)("div",{className:"shape1"}),(0,d.jsx)("div",{className:"shape2"}),(0,d.jsx)("div",{className:"shape3"}),(0,d.jsx)("div",{className:"shape4"}),(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"slider-one__single-inner",children:(0,d.jsxs)("div",{className:"slider-one__single-content",children:[(0,d.jsxs)("div",{className:"tagline",children:[(0,d.jsx)("div",{className:"round"}),(0,d.jsx)("div",{className:"text",children:(0,d.jsx)("span",{children:a("title1")})})]}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h2",{children:b("slide1")})})]})})})]})}),(0,d.jsx)(f.qr,{children:(0,d.jsxs)("div",{className:"slider-one__single",children:[(0,d.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner2.png)"}}),(0,d.jsx)("div",{className:"shape1"}),(0,d.jsx)("div",{className:"shape2"}),(0,d.jsx)("div",{className:"shape3"}),(0,d.jsx)("div",{className:"shape4"}),(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"slider-one__single-inner",children:(0,d.jsxs)("div",{className:"slider-one__single-content",children:[(0,d.jsxs)("div",{className:"tagline",children:[(0,d.jsx)("div",{className:"round"}),(0,d.jsx)("div",{className:"text",children:(0,d.jsx)("span",{children:a("title2")})})]}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h2",{children:b("slide2")})})]})})})]})}),(0,d.jsx)(f.qr,{children:(0,d.jsxs)("div",{className:"slider-one__single",children:[(0,d.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner3.png)"}}),(0,d.jsx)("div",{className:"shape1"}),(0,d.jsx)("div",{className:"shape2"}),(0,d.jsx)("div",{className:"shape3"}),(0,d.jsx)("div",{className:"shape4"}),(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"slider-one__single-inner",children:(0,d.jsxs)("div",{className:"slider-one__single-content",children:[(0,d.jsxs)("div",{className:"tagline",children:[(0,d.jsx)("div",{className:"round"}),(0,d.jsx)("div",{className:"text",children:(0,d.jsx)("span",{children:a("title3")})})]}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h2",{children:b("slide3")})})]})})})]})}),(0,d.jsx)(f.qr,{children:(0,d.jsxs)("div",{className:"slider-one__single",children:[(0,d.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner4.png)"}}),(0,d.jsx)("div",{className:"shape1"}),(0,d.jsx)("div",{className:"shape2"}),(0,d.jsx)("div",{className:"shape3"}),(0,d.jsx)("div",{className:"shape4"}),(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"slider-one__single-inner",children:(0,d.jsxs)("div",{className:"slider-one__single-content",children:[(0,d.jsxs)("div",{className:"tagline",children:[(0,d.jsx)("div",{className:"round"}),(0,d.jsx)("div",{className:"text",children:(0,d.jsx)("span",{children:a("title4")})})]}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h2",{children:b("slide4")})})]})})})]})}),(0,d.jsx)(f.qr,{children:(0,d.jsxs)("div",{className:"slider-one__single",children:[(0,d.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner5.png)"}}),(0,d.jsx)("div",{className:"shape1"}),(0,d.jsx)("div",{className:"shape2"}),(0,d.jsx)("div",{className:"shape3"}),(0,d.jsx)("div",{className:"shape4"}),(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"slider-one__single-inner",children:(0,d.jsxs)("div",{className:"slider-one__single-content",children:[(0,d.jsxs)("div",{className:"tagline",children:[(0,d.jsx)("div",{className:"round"}),(0,d.jsx)("div",{className:"text",children:(0,d.jsx)("span",{children:a("title5")})})]}),(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("h2",{children:b("slide5")})})]})})})]})})]})})})})}},92529:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,generateMetadata:()=>r});var d=c(37413);c(31858),c(66159),c(36478),c(83448),c(48488);var e=c(96794),f=c.n(e),g=c(29443),h=c.n(g),i=c(64207),j=c.n(i),k=c(14967),l=c(97663),m=c(39916),n=c(52863),o=c(87488),p=c(39710),q=c(42037);async function r(){let a=await (0,q.A)("metaData");return{title:a("title"),description:a("desc")}}async function s({children:a,params:b}){let{locale:c}=await b;(0,k.EL)(n.D.locales,c)||(0,m.notFound)();let e="th"===c?f().variable:"ch"===c?h().variable:j().variable;return(0,d.jsxs)("html",{lang:c,className:e,children:[(0,d.jsx)("head",{children:(0,d.jsx)("meta",{name:"google-site-verification",content:"YIeMcBwBUeAMJXLnLktlyISAHUHcCIcREi_ToKT2mFo"})}),(0,d.jsx)("body",{children:(0,d.jsxs)(l.A,{locale:c,children:[(0,d.jsx)(p.default,{}),(0,d.jsx)(o.default,{}),a]})})]})}},92711:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Banner.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Banner.js","default")},95951:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Product-accordian.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/sections/index/Product-accordian.js","default")},97516:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f);function h(){let[a,b]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"chatContainer",children:[(0,d.jsx)("button",{onClick:()=>{b(!a)},className:"chatButton",children:a?(0,d.jsx)("i",{className:"fas fa-times close"}):(0,d.jsx)("i",{className:"fa fa-comments"})}),a&&(0,d.jsxs)("div",{className:"iconConatiner",children:[(0,d.jsx)(g(),{className:"fab fa-facebook-f msgIcon",href:"https://m.me/207440262444547",target:"_blank"}),(0,d.jsx)(g(),{className:"fab fa-line lineIcon",href:"https://lin.ee/UZ46BIS",target:"_blank"})]})]})}c(66730)}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,860,122,443],()=>b(b.s=72920));module.exports=c})();
{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|trpc|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|trpc|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "8UgSRSstV0rXJWjo0Z752", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SEdGi2w19YaN/RXuMGSzBji3E5WpBgL4kkTnyObWE1I=", "__NEXT_PREVIEW_MODE_ID": "daf12b4ac0f9eb8f4bebba212843bd44", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a2dd553664852607d82391d9b6134453e1ac5c9c54085ea215b9d5e6c8c95359", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a2f18cbb1e59d163683ff785cc88412839287c3b016a834d07d561cca4a6eac8"}}}, "functions": {}, "sortedMiddleware": ["/"]}
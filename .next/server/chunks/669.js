exports.id=669,exports.ids=[669],exports.modules={25056:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687);c(43210);var e=c(77618),f=c(85814),g=c.n(f);function h(){let a=(0,e.c3)("PrivacyPolicy");return(0,d.jsxs)("div",{className:"privacy-content",children:[(0,d.jsx)("h2",{children:(0,d.jsx)("span",{children:a("title2")})}),(0,d.jsxs)("span",{children:[(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section7.title")})}),(0,d.jsx)("a",{children:"●"})," ",(0,d.jsx)("span",{children:a("section7.content1")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section8.title")})}),(0,d.jsx)("a",{children:"●"})," ",(0,d.jsx)("span",{children:a("section8.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:["・ ",(0,d.jsx)("span",{children:a("section8.list1")})]}),(0,d.jsxs)("li",{children:["・ ",(0,d.jsx)("span",{children:a("section8.list2")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section8.list3")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section8.list4")})]})]}),(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section9.title")})}),(0,d.jsx)("a",{children:"●"})," ",(0,d.jsx)("span",{children:a("section9.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section9.list1")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section9.list2")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section9.list3")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section9.list4")})]}),(0,d.jsxs)("li",{children:["・",(0,d.jsx)("span",{children:a("section9.list5")})]})]}),(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section10.title")})}),(0,d.jsx)("span",{children:a("section10.content1")})," ",(0,d.jsx)("br",{})]}),(0,d.jsxs)("span",{style:{color:"#325ae1"},children:[(0,d.jsx)("a",{children:a("contact.email")}),a("contact.info1")]})," ",(0,d.jsx)("br",{})," ",(0,d.jsxs)("span",{style:{color:"#325ae1"},children:[(0,d.jsx)("a",{children:a("contact.phone")})," ",(0,d.jsx)(g(),{href:`tel:${a("contact.info2")}`,children:a("contact.info2")})]})," ",(0,d.jsx)("span",{children:a("contact.hours")})," ",(0,d.jsx)("br",{}),(0,d.jsxs)("span",{children:[(0,d.jsx)("a",{style:{color:"#325ae1"},children:a("contact.address")})," ",a("contact.info3")]})]})}c(62486)},25208:()=>{},31319:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687);c(43210);var e=c(77618),f=c(85814),g=c.n(f);function h(){let a=(0,e.c3)("PrivacyPolicy");return(0,d.jsxs)("div",{className:"privacy-content",children:[(0,d.jsx)("h2",{children:(0,d.jsx)("span",{children:a("title1")})}),(0,d.jsxs)("span",{children:[(0,d.jsx)("span",{children:a("intro1")})," ",(0,d.jsxs)("a",{children:[a("name")," "]})," ",(0,d.jsx)("span",{children:a("intro2")})," ",(0,d.jsx)("a",{children:a("company")})," ",(0,d.jsx)("span",{children:a("intro3")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("span",{children:a("intro4")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("span",{children:a("intro5")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("span",{children:a("intro6")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section1.title")})}),(0,d.jsx)("a",{children:"1.1."})," ",(0,d.jsx)("span",{children:a("section1.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:"1.2."})," ",(0,d.jsx)("span",{children:a("section1.content2")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section2.title")})}),(0,d.jsx)("a",{children:"2.1."})," ",(0,d.jsx)("span",{children:a("section2.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:["2.1.1 ",(0,d.jsx)("span",{children:a("section2.list1")})]}),(0,d.jsxs)("li",{children:["2.1.2 ",(0,d.jsx)("span",{children:a("section2.list2")})]}),(0,d.jsxs)("li",{children:["2.1.3 ",(0,d.jsx)("span",{children:a("section2.list3")})]}),(0,d.jsxs)("li",{children:["2.1.4 ",(0,d.jsx)("span",{children:a("section2.list4")})]}),(0,d.jsxs)("li",{children:["2.1.5 ",(0,d.jsx)("span",{children:a("section2.list5")})]}),(0,d.jsxs)("li",{children:["2.1.6 ",(0,d.jsx)("span",{children:a("section2.list6")})]}),(0,d.jsxs)("li",{children:["2.1.7 ",(0,d.jsx)("span",{children:a("section2.list7")})]}),(0,d.jsxs)("li",{children:["2.1.8 ",(0,d.jsx)("span",{children:a("section2.list8")})]})]}),(0,d.jsx)("a",{children:"2.2"})," ",(0,d.jsx)("span",{children:a("section2.content2")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:"2.3"})," ",(0,d.jsx)("span",{children:a("section2.content3")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:"2.4"})," ",(0,d.jsx)("span",{children:a("section2.content4")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section3.title")})}),(0,d.jsx)("a",{children:"3.1"})," ",(0,d.jsx)("span",{children:a("section3.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:"3.2"})," ",(0,d.jsx)("span",{children:a("section3.content2")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section4.title")})}),(0,d.jsx)("a",{children:"4.1"})," ",(0,d.jsx)("span",{children:a("section4.content1")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section5.title")})}),(0,d.jsx)("a",{children:"5.1"})," ",(0,d.jsx)("span",{children:a("section5.content1")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section6.title")})}),(0,d.jsx)("a",{children:"6.1"})," ",(0,d.jsx)("span",{children:a("section6.content1")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:a("section6.howWillNotify")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:a("section6.subtitle1")})," ",(0,d.jsx)("span",{children:a("section6.option1")})," ",(0,d.jsx)("br",{}),(0,d.jsx)("a",{children:a("section6.or")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("a",{children:a("section6.subtitle2")})," ",(0,d.jsx)("span",{children:a("section6.option2")})," ",(0,d.jsx)("br",{})," ",(0,d.jsx)("br",{}),(0,d.jsx)("h3",{children:(0,d.jsx)("span",{children:a("section10.title")})}),(0,d.jsx)("span",{children:a("section10.content1")})," ",(0,d.jsx)("br",{})]}),(0,d.jsxs)("span",{style:{color:"#325ae1"},children:[(0,d.jsx)("a",{children:a("contact.email")}),a("contact.info1")]})," ",(0,d.jsx)("br",{})," ",(0,d.jsxs)("span",{style:{color:"#325ae1"},children:[(0,d.jsx)("a",{children:a("contact.phone")})," ",(0,d.jsx)(g(),{href:`tel:${a("contact.info2")}`,children:a("contact.info2")})]})," ",(0,d.jsx)("span",{children:a("contact.hours")})," ",(0,d.jsx)("br",{}),(0,d.jsxs)("span",{children:[(0,d.jsx)("a",{style:{color:"#325ae1"},children:a("contact.address")})," ",a("contact.info3")]})]})}c(62486)},34232:(a,b,c)=>{Promise.resolve().then(c.bind(c,87488)),Promise.resolve().then(c.bind(c,39710)),Promise.resolve().then(c.bind(c,80994))},39710:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/FacebookMSG.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/FacebookMSG.js","default")},49050:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(77618);function i(){let[a,b]=(0,e.useState)(!1),c=(0,h.c3)("ConsentCookies");return a?(0,d.jsx)("div",{className:"cookies-consent2",children:(0,d.jsxs)("div",{className:"cookies-consent__message",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:c("message")})," ",(0,d.jsx)("span",{children:c("moreInfo")})," ",(0,d.jsx)(g(),{href:"/privacy-policy",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)("span",{children:c("privacyPolicy")})}),(0,d.jsx)("span",{children:c("and")}),(0,d.jsx)(g(),{href:"/cookies-policy",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)("span",{children:c("cookiePolicy")})}),(0,d.jsx)("span",{children:c("period")})]}),(0,d.jsx)("button",{onClick:()=>{localStorage.setItem("cookies-consent","true"),b(!1)},className:"cookies-consent__button cookies-consent__button--accept",children:(0,d.jsx)("span",{children:c("accept")})})]})}):null}c(25208)},50342:(a,b,c)=>{Promise.resolve().then(c.bind(c,96268)),Promise.resolve().then(c.bind(c,33856)),Promise.resolve().then(c.bind(c,45196))},51061:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(37413),e=c(42024),f=c(92118),g=c(52863);let{Link:h,redirect:i,usePathname:j,useRouter:k,getPathname:l}=(0,f.A)(g.D);function m(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(e.default,{headerStyle:3,footerStyle:4,breadcrumbTitle:"Page Not Found",children:(0,d.jsx)("section",{className:"error-page",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"error-page__inner text-center",children:[(0,d.jsx)("div",{className:"error-page__img float-bob-y",children:(0,d.jsx)("img",{src:"/assets/images/resources/error-page-img1.png",alt:""})}),(0,d.jsxs)("div",{className:"error-page__content",children:[(0,d.jsx)("h2",{children:"Oops! Page Not Found!"}),(0,d.jsx)("p",{children:"The page you are looking for does not exist. It might have been moved or deleted."}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(h,{className:"thm-btn",href:"/",children:["Back To Home",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})})})})})}},52863:(a,b,c)=>{"use strict";c.d(b,{D:()=>d});let d=(0,c(55946).A)({locales:["th","en","ru","ch"],defaultLocale:"th"})},62486:()=>{},66159:()=>{},66730:()=>{},69080:(a,b,c)=>{Promise.resolve().then(c.bind(c,49050)),Promise.resolve().then(c.bind(c,97516)),Promise.resolve().then(c.bind(c,45196))},72996:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(35471),e=c(14967),f=c(52863);let g=(0,d.A)(async({requestLocale:a})=>{let b=await a,d=(0,e.EL)(f.D.locales,b)?b:f.D.defaultLocale;return{locale:d,messages:(await c(76565)(`./${d}.json`)).default}})},76565:(a,b,c)=>{var d={"./ch.json":[17852,852],"./en.json":[87368,368],"./ru.json":[71270,270],"./th.json":[88599,599]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,19))}e.keys=()=>Object.keys(d),e.id=76565,a.exports=e},83998:(a,b,c)=>{Promise.resolve().then(c.bind(c,42024)),Promise.resolve().then(c.bind(c,75788)),Promise.resolve().then(c.bind(c,80994))},87488:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/CookiesConsent.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/elements/CookiesConsent.js","default")},92529:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,generateMetadata:()=>r});var d=c(37413);c(31858),c(66159),c(36478),c(83448),c(48488);var e=c(96794),f=c.n(e),g=c(29443),h=c.n(g),i=c(64207),j=c.n(i),k=c(14967),l=c(97663),m=c(39916),n=c(52863),o=c(87488),p=c(39710),q=c(42037);async function r(){let a=await (0,q.A)("metaData");return{title:a("title"),description:a("desc")}}async function s({children:a,params:b}){let{locale:c}=await b;(0,k.EL)(n.D.locales,c)||(0,m.notFound)();let e="th"===c?f().variable:"ch"===c?h().variable:j().variable;return(0,d.jsxs)("html",{lang:c,className:e,children:[(0,d.jsx)("head",{children:(0,d.jsx)("meta",{name:"google-site-verification",content:"YIeMcBwBUeAMJXLnLktlyISAHUHcCIcREi_ToKT2mFo"})}),(0,d.jsx)("body",{children:(0,d.jsxs)(l.A,{locale:c,children:[(0,d.jsx)(p.default,{}),(0,d.jsx)(o.default,{}),a]})})]})}},97516:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f);function h(){let[a,b]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"chatContainer",children:[(0,d.jsx)("button",{onClick:()=>{b(!a)},className:"chatButton",children:a?(0,d.jsx)("i",{className:"fas fa-times close"}):(0,d.jsx)("i",{className:"fa fa-comments"})}),a&&(0,d.jsxs)("div",{className:"iconConatiner",children:[(0,d.jsx)(g(),{className:"fab fa-facebook-f msgIcon",href:"https://m.me/207440262444547",target:"_blank"}),(0,d.jsx)(g(),{className:"fab fa-line lineIcon",href:"https://lin.ee/UZ46BIS",target:"_blank"})]})]})}c(66730)}};
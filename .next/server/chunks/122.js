exports.id=122,exports.ids=[122],exports.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},2144:(a,b,c)=>{"use strict";c.d(b,{b:()=>az,d:()=>aw,e:()=>aB,f:()=>au,g:()=>av,i:()=>aD,r:()=>aC});var d,e,f,g,h,i,j,k=function(a,b){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(a,b){a.__proto__=b}||function(a,b){for(var c in b)Object.prototype.hasOwnProperty.call(b,c)&&(a[c]=b[c])})(a,b)};function l(a,b){if("function"!=typeof b&&null!==b)throw TypeError("Class extends value "+String(b)+" is not a constructor or null");function c(){this.constructor=a}k(a,b),a.prototype=null===b?Object.create(b):(c.prototype=b.prototype,new c)}var m=function(){return(m=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};Object.create;function n(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))}function o(a,b){var c=b&&b.cache?b.cache:t,d=b&&b.serializer?b.serializer:r;return(b&&b.strategy?b.strategy:function(a,b){var c,d,e=1===a.length?p:q;return c=b.cache.create(),d=b.serializer,e.bind(this,a,c,d)})(a,{cache:c,serializer:d})}function p(a,b,c,d){var e=null==d||"number"==typeof d||"boolean"==typeof d?d:c(d),f=b.get(e);return void 0===f&&(f=a.call(this,d),b.set(e,f)),f}function q(a,b,c){var d=Array.prototype.slice.call(arguments,3),e=c(d),f=b.get(e);return void 0===f&&(f=a.apply(this,d),b.set(e,f)),f}Object.create,"function"==typeof SuppressedError&&SuppressedError;var r=function(){return JSON.stringify(arguments)},s=function(){function a(){this.cache=Object.create(null)}return a.prototype.get=function(a){return this.cache[a]},a.prototype.set=function(a,b){this.cache[a]=b},a}(),t={create:function(){return new s}},u={variadic:function(a,b){var c,d;return c=b.cache.create(),d=b.serializer,q.bind(this,a,c,d)},monadic:function(a,b){var c,d;return c=b.cache.create(),d=b.serializer,p.bind(this,a,c,d)}};function v(a){return a.type===e.literal}function w(a){return a.type===e.number}function x(a){return a.type===e.date}function y(a){return a.type===e.time}function z(a){return a.type===e.select}function A(a){return a.type===e.plural}function B(a){return a.type===e.tag}function C(a){return!!(a&&"object"==typeof a&&a.type===f.number)}function D(a){return!!(a&&"object"==typeof a&&a.type===f.dateTime)}!function(a){a[a.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",a[a.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",a[a.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",a[a.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",a[a.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",a[a.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",a[a.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",a[a.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",a[a.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",a[a.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",a[a.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",a[a.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",a[a.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",a[a.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",a[a.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",a[a.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",a[a.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",a[a.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",a[a.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",a[a.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",a[a.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",a[a.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",a[a.INVALID_TAG=23]="INVALID_TAG",a[a.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",a[a.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",a[a.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(d||(d={})),function(a){a[a.literal=0]="literal",a[a.argument=1]="argument",a[a.number=2]="number",a[a.date=3]="date",a[a.time=4]="time",a[a.select=5]="select",a[a.plural=6]="plural",a[a.pound=7]="pound",a[a.tag=8]="tag"}(e||(e={})),function(a){a[a.number=0]="number",a[a.dateTime=1]="dateTime"}(f||(f={}));var E=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,F=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,G=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,H=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,I=/^(@+)?(\+|#+)?[rs]?$/g,J=/(\*)(0+)|(#+)(0+)|(0+)/g,K=/^(0+)$/;function L(a){var b={};return"r"===a[a.length-1]?b.roundingPriority="morePrecision":"s"===a[a.length-1]&&(b.roundingPriority="lessPrecision"),a.replace(I,function(a,c,d){return"string"!=typeof d?(b.minimumSignificantDigits=c.length,b.maximumSignificantDigits=c.length):"+"===d?b.minimumSignificantDigits=c.length:"#"===c[0]?b.maximumSignificantDigits=c.length:(b.minimumSignificantDigits=c.length,b.maximumSignificantDigits=c.length+("string"==typeof d?d.length:0)),""}),b}function M(a){switch(a){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function N(a){var b=M(a);return b||{}}var O={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},P=new RegExp("^".concat(E.source,"*")),Q=new RegExp("".concat(E.source,"*$"));function R(a,b){return{start:a,end:b}}var S=!!String.prototype.startsWith&&"_a".startsWith("a",1),T=!!String.fromCodePoint,U=!!Object.fromEntries,V=!!String.prototype.codePointAt,W=!!String.prototype.trimStart,X=!!String.prototype.trimEnd,Y=Number.isSafeInteger?Number.isSafeInteger:function(a){return"number"==typeof a&&isFinite(a)&&Math.floor(a)===a&&0x1fffffffffffff>=Math.abs(a)},Z=!0;try{Z=(null==(g=ae("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:g[0])==="a"}catch(a){Z=!1}var $=S?function(a,b,c){return a.startsWith(b,c)}:function(a,b,c){return a.slice(c,c+b.length)===b},_=T?String.fromCodePoint:function(){for(var a,b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];for(var d="",e=b.length,f=0;e>f;){if((a=b[f++])>1114111)throw RangeError(a+" is not a valid code point");d+=a<65536?String.fromCharCode(a):String.fromCharCode(((a-=65536)>>10)+55296,a%1024+56320)}return d},aa=U?Object.fromEntries:function(a){for(var b={},c=0;c<a.length;c++){var d=a[c],e=d[0],f=d[1];b[e]=f}return b},ab=V?function(a,b){return a.codePointAt(b)}:function(a,b){var c,d=a.length;if(!(b<0)&&!(b>=d)){var e=a.charCodeAt(b);return e<55296||e>56319||b+1===d||(c=a.charCodeAt(b+1))<56320||c>57343?e:(e-55296<<10)+(c-56320)+65536}},ac=W?function(a){return a.trimStart()}:function(a){return a.replace(P,"")},ad=X?function(a){return a.trimEnd()}:function(a){return a.replace(Q,"")};function ae(a,b){return new RegExp(a,b)}if(Z){var af=ae("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");h=function(a,b){var c;return af.lastIndex=b,null!=(c=af.exec(a)[1])?c:""}}else h=function(a,b){for(var c=[];;){var d,e=ab(a,b);if(void 0===e||ai(e)||(d=e)>=33&&d<=35||36===d||d>=37&&d<=39||40===d||41===d||42===d||43===d||44===d||45===d||d>=46&&d<=47||d>=58&&d<=59||d>=60&&d<=62||d>=63&&d<=64||91===d||92===d||93===d||94===d||96===d||123===d||124===d||125===d||126===d||161===d||d>=162&&d<=165||166===d||167===d||169===d||171===d||172===d||174===d||176===d||177===d||182===d||187===d||191===d||215===d||247===d||d>=8208&&d<=8213||d>=8214&&d<=8215||8216===d||8217===d||8218===d||d>=8219&&d<=8220||8221===d||8222===d||8223===d||d>=8224&&d<=8231||d>=8240&&d<=8248||8249===d||8250===d||d>=8251&&d<=8254||d>=8257&&d<=8259||8260===d||8261===d||8262===d||d>=8263&&d<=8273||8274===d||8275===d||d>=8277&&d<=8286||d>=8592&&d<=8596||d>=8597&&d<=8601||d>=8602&&d<=8603||d>=8604&&d<=8607||8608===d||d>=8609&&d<=8610||8611===d||d>=8612&&d<=8613||8614===d||d>=8615&&d<=8621||8622===d||d>=8623&&d<=8653||d>=8654&&d<=8655||d>=8656&&d<=8657||8658===d||8659===d||8660===d||d>=8661&&d<=8691||d>=8692&&d<=8959||d>=8960&&d<=8967||8968===d||8969===d||8970===d||8971===d||d>=8972&&d<=8991||d>=8992&&d<=8993||d>=8994&&d<=9e3||9001===d||9002===d||d>=9003&&d<=9083||9084===d||d>=9085&&d<=9114||d>=9115&&d<=9139||d>=9140&&d<=9179||d>=9180&&d<=9185||d>=9186&&d<=9254||d>=9255&&d<=9279||d>=9280&&d<=9290||d>=9291&&d<=9311||d>=9472&&d<=9654||9655===d||d>=9656&&d<=9664||9665===d||d>=9666&&d<=9719||d>=9720&&d<=9727||d>=9728&&d<=9838||9839===d||d>=9840&&d<=10087||10088===d||10089===d||10090===d||10091===d||10092===d||10093===d||10094===d||10095===d||10096===d||10097===d||10098===d||10099===d||10100===d||10101===d||d>=10132&&d<=10175||d>=10176&&d<=10180||10181===d||10182===d||d>=10183&&d<=10213||10214===d||10215===d||10216===d||10217===d||10218===d||10219===d||10220===d||10221===d||10222===d||10223===d||d>=10224&&d<=10239||d>=10240&&d<=10495||d>=10496&&d<=10626||10627===d||10628===d||10629===d||10630===d||10631===d||10632===d||10633===d||10634===d||10635===d||10636===d||10637===d||10638===d||10639===d||10640===d||10641===d||10642===d||10643===d||10644===d||10645===d||10646===d||10647===d||10648===d||d>=10649&&d<=10711||10712===d||10713===d||10714===d||10715===d||d>=10716&&d<=10747||10748===d||10749===d||d>=10750&&d<=11007||d>=11008&&d<=11055||d>=11056&&d<=11076||d>=11077&&d<=11078||d>=11079&&d<=11084||d>=11085&&d<=11123||d>=11124&&d<=11125||d>=11126&&d<=11157||11158===d||d>=11159&&d<=11263||d>=11776&&d<=11777||11778===d||11779===d||11780===d||11781===d||d>=11782&&d<=11784||11785===d||11786===d||11787===d||11788===d||11789===d||d>=11790&&d<=11798||11799===d||d>=11800&&d<=11801||11802===d||11803===d||11804===d||11805===d||d>=11806&&d<=11807||11808===d||11809===d||11810===d||11811===d||11812===d||11813===d||11814===d||11815===d||11816===d||11817===d||d>=11818&&d<=11822||11823===d||d>=11824&&d<=11833||d>=11834&&d<=11835||d>=11836&&d<=11839||11840===d||11841===d||11842===d||d>=11843&&d<=11855||d>=11856&&d<=11857||11858===d||d>=11859&&d<=11903||d>=12289&&d<=12291||12296===d||12297===d||12298===d||12299===d||12300===d||12301===d||12302===d||12303===d||12304===d||12305===d||d>=12306&&d<=12307||12308===d||12309===d||12310===d||12311===d||12312===d||12313===d||12314===d||12315===d||12316===d||12317===d||d>=12318&&d<=12319||12320===d||12336===d||64830===d||64831===d||d>=65093&&d<=65094)break;c.push(e),b+=e>=65536?2:1}return _.apply(void 0,c)};var ag=function(){function a(a,b){void 0===b&&(b={}),this.message=a,this.position={offset:0,line:1,column:1},this.ignoreTag=!!b.ignoreTag,this.locale=b.locale,this.requiresOtherClause=!!b.requiresOtherClause,this.shouldParseSkeletons=!!b.shouldParseSkeletons}return a.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},a.prototype.parseMessage=function(a,b,c){for(var f=[];!this.isEOF();){var g=this.char();if(123===g){var h=this.parseArgument(a,c);if(h.err)return h;f.push(h.val)}else if(125===g&&a>0)break;else if(35===g&&("plural"===b||"selectordinal"===b)){var i=this.clonePosition();this.bump(),f.push({type:e.pound,location:R(i,this.clonePosition())})}else if(60!==g||this.ignoreTag||47!==this.peek())if(60===g&&!this.ignoreTag&&ah(this.peek()||0)){var h=this.parseTag(a,b);if(h.err)return h;f.push(h.val)}else{var h=this.parseLiteral(a,b);if(h.err)return h;f.push(h.val)}else if(!c)return this.error(d.UNMATCHED_CLOSING_TAG,R(this.clonePosition(),this.clonePosition()));else break}return{val:f,err:null}},a.prototype.parseTag=function(a,b){var c=this.clonePosition();this.bump();var f=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:e.literal,value:"<".concat(f,"/>"),location:R(c,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(d.INVALID_TAG,R(c,this.clonePosition()));var g=this.parseMessage(a+1,b,!0);if(g.err)return g;var h=g.val,i=this.clonePosition();if(!this.bumpIf("</"))return this.error(d.UNCLOSED_TAG,R(c,this.clonePosition()));if(this.isEOF()||!ah(this.char()))return this.error(d.INVALID_TAG,R(i,this.clonePosition()));var j=this.clonePosition();return f!==this.parseTagName()?this.error(d.UNMATCHED_CLOSING_TAG,R(j,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:e.tag,value:f,children:h,location:R(c,this.clonePosition())},err:null}:this.error(d.INVALID_TAG,R(i,this.clonePosition()))},a.prototype.parseTagName=function(){var a,b=this.offset();for(this.bump();!this.isEOF()&&(45===(a=this.char())||46===a||a>=48&&a<=57||95===a||a>=97&&a<=122||a>=65&&a<=90||183==a||a>=192&&a<=214||a>=216&&a<=246||a>=248&&a<=893||a>=895&&a<=8191||a>=8204&&a<=8205||a>=8255&&a<=8256||a>=8304&&a<=8591||a>=11264&&a<=12271||a>=12289&&a<=55295||a>=63744&&a<=64975||a>=65008&&a<=65533||a>=65536&&a<=983039);)this.bump();return this.message.slice(b,this.offset())},a.prototype.parseLiteral=function(a,b){for(var c=this.clonePosition(),d="";;){var f=this.tryParseQuote(b);if(f){d+=f;continue}var g=this.tryParseUnquoted(a,b);if(g){d+=g;continue}var h=this.tryParseLeftAngleBracket();if(h){d+=h;continue}break}var i=R(c,this.clonePosition());return{val:{type:e.literal,value:d,location:i},err:null}},a.prototype.tryParseLeftAngleBracket=function(){var a;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(ah(a=this.peek()||0)||47===a)?null:(this.bump(),"<")},a.prototype.tryParseQuote=function(a){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===a||"selectordinal"===a)break;return null;default:return null}this.bump();var b=[this.char()];for(this.bump();!this.isEOF();){var c=this.char();if(39===c)if(39===this.peek())b.push(39),this.bump();else{this.bump();break}else b.push(c);this.bump()}return _.apply(void 0,b)},a.prototype.tryParseUnquoted=function(a,b){if(this.isEOF())return null;var c=this.char();return 60===c||123===c||35===c&&("plural"===b||"selectordinal"===b)||125===c&&a>0?null:(this.bump(),_(c))},a.prototype.parseArgument=function(a,b){var c=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(d.EXPECT_ARGUMENT_CLOSING_BRACE,R(c,this.clonePosition()));if(125===this.char())return this.bump(),this.error(d.EMPTY_ARGUMENT,R(c,this.clonePosition()));var f=this.parseIdentifierIfPossible().value;if(!f)return this.error(d.MALFORMED_ARGUMENT,R(c,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(d.EXPECT_ARGUMENT_CLOSING_BRACE,R(c,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:e.argument,value:f,location:R(c,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(d.EXPECT_ARGUMENT_CLOSING_BRACE,R(c,this.clonePosition()));return this.parseArgumentOptions(a,b,f,c);default:return this.error(d.MALFORMED_ARGUMENT,R(c,this.clonePosition()))}},a.prototype.parseIdentifierIfPossible=function(){var a=this.clonePosition(),b=this.offset(),c=h(this.message,b),d=b+c.length;return this.bumpTo(d),{value:c,location:R(a,this.clonePosition())}},a.prototype.parseArgumentOptions=function(a,b,c,g){var h,i=this.clonePosition(),j=this.parseIdentifierIfPossible().value,k=this.clonePosition();switch(j){case"":return this.error(d.EXPECT_ARGUMENT_TYPE,R(i,k));case"number":case"date":case"time":this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var n=this.clonePosition(),o=this.parseSimpleArgStyleIfPossible();if(o.err)return o;var p=ad(o.val);if(0===p.length)return this.error(d.EXPECT_ARGUMENT_STYLE,R(this.clonePosition(),this.clonePosition()));l={style:p,styleLocation:R(n,this.clonePosition())}}var q=this.tryParseArgumentClose(g);if(q.err)return q;var r=R(g,this.clonePosition());if(l&&$(null==l?void 0:l.style,"::",0)){var s=ac(l.style.slice(2));if("number"===j){var o=this.parseNumberSkeletonFromString(s,l.styleLocation);if(o.err)return o;return{val:{type:e.number,value:c,location:r,style:o.val},err:null}}if(0===s.length)return this.error(d.EXPECT_DATE_TIME_SKELETON,r);var t,u=s;this.locale&&(u=function(a,b){for(var c="",d=0;d<a.length;d++){var e=a.charAt(d);if("j"===e){for(var f=0;d+1<a.length&&a.charAt(d+1)===e;)f++,d++;var g=1+(1&f),h=f<2?1:3+(f>>1),i=function(a){var b,c=a.hourCycle;if(void 0===c&&a.hourCycles&&a.hourCycles.length&&(c=a.hourCycles[0]),c)switch(c){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var d=a.language;return"root"!==d&&(b=a.maximize().region),(O[b||""]||O[d||""]||O["".concat(d,"-001")]||O["001"])[0]}(b);for(("H"==i||"k"==i)&&(h=0);h-- >0;)c+="a";for(;g-- >0;)c=i+c}else"J"===e?c+="H":c+=e}return c}(s,this.locale));var p={type:f.dateTime,pattern:u,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?(t={},u.replace(F,function(a){var b=a.length;switch(a[0]){case"G":t.era=4===b?"long":5===b?"narrow":"short";break;case"y":t.year=2===b?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][b-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][b-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=4===b?"long":5===b?"narrow":"short";break;case"e":if(b<4)throw RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][b-4];break;case"c":if(b<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][b-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][b-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][b-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][b-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][b-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][b-1];break;case"s":t.second=["numeric","2-digit"][b-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=b<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t):{}};return{val:{type:"date"===j?e.date:e.time,value:c,location:r,style:p},err:null}}return{val:{type:"number"===j?e.number:"date"===j?e.date:e.time,value:c,location:r,style:null!=(h=null==l?void 0:l.style)?h:null},err:null};case"plural":case"selectordinal":case"select":var v=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(d.EXPECT_SELECT_ARGUMENT_OPTIONS,R(v,m({},v)));this.bumpSpace();var w=this.parseIdentifierIfPossible(),x=0;if("select"!==j&&"offset"===w.value){if(!this.bumpIf(":"))return this.error(d.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,R(this.clonePosition(),this.clonePosition()));this.bumpSpace();var o=this.tryParseDecimalInteger(d.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,d.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(o.err)return o;this.bumpSpace(),w=this.parseIdentifierIfPossible(),x=o.val}var y=this.tryParsePluralOrSelectOptions(a,j,b,w);if(y.err)return y;var q=this.tryParseArgumentClose(g);if(q.err)return q;var z=R(g,this.clonePosition());if("select"===j)return{val:{type:e.select,value:c,options:aa(y.val),location:z},err:null};return{val:{type:e.plural,value:c,options:aa(y.val),offset:x,pluralType:"plural"===j?"cardinal":"ordinal",location:z},err:null};default:return this.error(d.INVALID_ARGUMENT_TYPE,R(i,k))}},a.prototype.tryParseArgumentClose=function(a){return this.isEOF()||125!==this.char()?this.error(d.EXPECT_ARGUMENT_CLOSING_BRACE,R(a,this.clonePosition())):(this.bump(),{val:!0,err:null})},a.prototype.parseSimpleArgStyleIfPossible=function(){for(var a=0,b=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var c=this.clonePosition();if(!this.bumpUntil("'"))return this.error(d.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,R(c,this.clonePosition()));this.bump();break;case 123:a+=1,this.bump();break;case 125:if(!(a>0))return{val:this.message.slice(b.offset,this.offset()),err:null};a-=1;break;default:this.bump()}return{val:this.message.slice(b.offset,this.offset()),err:null}},a.prototype.parseNumberSkeletonFromString=function(a,b){var c=[];try{c=function(a){if(0===a.length)throw Error("Number skeleton cannot be empty");for(var b=a.split(G).filter(function(a){return a.length>0}),c=[],d=0;d<b.length;d++){var e=b[d].split("/");if(0===e.length)throw Error("Invalid number skeleton");for(var f=e[0],g=e.slice(1),h=0;h<g.length;h++)if(0===g[h].length)throw Error("Invalid number skeleton");c.push({stem:f,options:g})}return c}(a)}catch(a){return this.error(d.INVALID_NUMBER_SKELETON,b)}return{val:{type:f.number,tokens:c,location:b,parsedOptions:this.shouldParseSkeletons?function(a){for(var b={},c=0;c<a.length;c++){var d=a[c];switch(d.stem){case"percent":case"%":b.style="percent";continue;case"%x100":b.style="percent",b.scale=100;continue;case"currency":b.style="currency",b.currency=d.options[0];continue;case"group-off":case",_":b.useGrouping=!1;continue;case"precision-integer":case".":b.maximumFractionDigits=0;continue;case"measure-unit":case"unit":b.style="unit",b.unit=d.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":b.notation="compact",b.compactDisplay="short";continue;case"compact-long":case"KK":b.notation="compact",b.compactDisplay="long";continue;case"scientific":b=m(m(m({},b),{notation:"scientific"}),d.options.reduce(function(a,b){return m(m({},a),N(b))},{}));continue;case"engineering":b=m(m(m({},b),{notation:"engineering"}),d.options.reduce(function(a,b){return m(m({},a),N(b))},{}));continue;case"notation-simple":b.notation="standard";continue;case"unit-width-narrow":b.currencyDisplay="narrowSymbol",b.unitDisplay="narrow";continue;case"unit-width-short":b.currencyDisplay="code",b.unitDisplay="short";continue;case"unit-width-full-name":b.currencyDisplay="name",b.unitDisplay="long";continue;case"unit-width-iso-code":b.currencyDisplay="symbol";continue;case"scale":b.scale=parseFloat(d.options[0]);continue;case"rounding-mode-floor":b.roundingMode="floor";continue;case"rounding-mode-ceiling":b.roundingMode="ceil";continue;case"rounding-mode-down":b.roundingMode="trunc";continue;case"rounding-mode-up":b.roundingMode="expand";continue;case"rounding-mode-half-even":b.roundingMode="halfEven";continue;case"rounding-mode-half-down":b.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":b.roundingMode="halfExpand";continue;case"integer-width":if(d.options.length>1)throw RangeError("integer-width stems only accept a single optional option");d.options[0].replace(J,function(a,c,d,e,f,g){if(c)b.minimumIntegerDigits=d.length;else if(e&&f)throw Error("We currently do not support maximum integer digits");else if(g)throw Error("We currently do not support exact integer digits");return""});continue}if(K.test(d.stem)){b.minimumIntegerDigits=d.stem.length;continue}if(H.test(d.stem)){if(d.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");d.stem.replace(H,function(a,c,d,e,f,g){return"*"===d?b.minimumFractionDigits=c.length:e&&"#"===e[0]?b.maximumFractionDigits=e.length:f&&g?(b.minimumFractionDigits=f.length,b.maximumFractionDigits=f.length+g.length):(b.minimumFractionDigits=c.length,b.maximumFractionDigits=c.length),""});var e=d.options[0];"w"===e?b=m(m({},b),{trailingZeroDisplay:"stripIfInteger"}):e&&(b=m(m({},b),L(e)));continue}if(I.test(d.stem)){b=m(m({},b),L(d.stem));continue}var f=M(d.stem);f&&(b=m(m({},b),f));var g=function(a){var b;if("E"===a[0]&&"E"===a[1]?(b={notation:"engineering"},a=a.slice(2)):"E"===a[0]&&(b={notation:"scientific"},a=a.slice(1)),b){var c=a.slice(0,2);if("+!"===c?(b.signDisplay="always",a=a.slice(2)):"+?"===c&&(b.signDisplay="exceptZero",a=a.slice(2)),!K.test(a))throw Error("Malformed concise eng/scientific notation");b.minimumIntegerDigits=a.length}return b}(d.stem);g&&(b=m(m({},b),g))}return b}(c):{}},err:null}},a.prototype.tryParsePluralOrSelectOptions=function(a,b,c,e){for(var f,g=!1,h=[],i=new Set,j=e.value,k=e.location;;){if(0===j.length){var l=this.clonePosition();if("select"!==b&&this.bumpIf("=")){var m=this.tryParseDecimalInteger(d.EXPECT_PLURAL_ARGUMENT_SELECTOR,d.INVALID_PLURAL_ARGUMENT_SELECTOR);if(m.err)return m;k=R(l,this.clonePosition()),j=this.message.slice(l.offset,this.offset())}else break}if(i.has(j))return this.error("select"===b?d.DUPLICATE_SELECT_ARGUMENT_SELECTOR:d.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,k);"other"===j&&(g=!0),this.bumpSpace();var n=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===b?d.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:d.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,R(this.clonePosition(),this.clonePosition()));var o=this.parseMessage(a+1,b,c);if(o.err)return o;var p=this.tryParseArgumentClose(n);if(p.err)return p;h.push([j,{value:o.val,location:R(n,this.clonePosition())}]),i.add(j),this.bumpSpace(),j=(f=this.parseIdentifierIfPossible()).value,k=f.location}return 0===h.length?this.error("select"===b?d.EXPECT_SELECT_ARGUMENT_SELECTOR:d.EXPECT_PLURAL_ARGUMENT_SELECTOR,R(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!g?this.error(d.MISSING_OTHER_CLAUSE,R(this.clonePosition(),this.clonePosition())):{val:h,err:null}},a.prototype.tryParseDecimalInteger=function(a,b){var c=1,d=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(c=-1);for(var e=!1,f=0;!this.isEOF();){var g=this.char();if(g>=48&&g<=57)e=!0,f=10*f+(g-48),this.bump();else break}var h=R(d,this.clonePosition());return e?Y(f*=c)?{val:f,err:null}:this.error(b,h):this.error(a,h)},a.prototype.offset=function(){return this.position.offset},a.prototype.isEOF=function(){return this.offset()===this.message.length},a.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},a.prototype.char=function(){var a=this.position.offset;if(a>=this.message.length)throw Error("out of bound");var b=ab(this.message,a);if(void 0===b)throw Error("Offset ".concat(a," is at invalid UTF-16 code unit boundary"));return b},a.prototype.error=function(a,b){return{val:null,err:{kind:a,message:this.message,location:b}}},a.prototype.bump=function(){if(!this.isEOF()){var a=this.char();10===a?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=a<65536?1:2)}},a.prototype.bumpIf=function(a){if($(this.message,a,this.offset())){for(var b=0;b<a.length;b++)this.bump();return!0}return!1},a.prototype.bumpUntil=function(a){var b=this.offset(),c=this.message.indexOf(a,b);return c>=0?(this.bumpTo(c),!0):(this.bumpTo(this.message.length),!1)},a.prototype.bumpTo=function(a){if(this.offset()>a)throw Error("targetOffset ".concat(a," must be greater than or equal to the current offset ").concat(this.offset()));for(a=Math.min(a,this.message.length);;){var b=this.offset();if(b===a)break;if(b>a)throw Error("targetOffset ".concat(a," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},a.prototype.bumpSpace=function(){for(;!this.isEOF()&&ai(this.char());)this.bump()},a.prototype.peek=function(){if(this.isEOF())return null;var a=this.char(),b=this.offset(),c=this.message.charCodeAt(b+(a>=65536?2:1));return null!=c?c:null},a}();function ah(a){return a>=97&&a<=122||a>=65&&a<=90}function ai(a){return a>=9&&a<=13||32===a||133===a||a>=8206&&a<=8207||8232===a||8233===a}function aj(a,b){void 0===b&&(b={});var c=new ag(a,b=m({shouldParseSkeletons:!0,requiresOtherClause:!0},b)).parse();if(c.err){var e=SyntaxError(d[c.err.kind]);throw e.location=c.err.location,e.originalMessage=c.err.message,e}return(null==b?void 0:b.captureLocation)||function a(b){b.forEach(function(b){if(delete b.location,z(b)||A(b))for(var c in b.options)delete b.options[c].location,a(b.options[c].value);else w(b)&&C(b.style)||(x(b)||y(b))&&D(b.style)?delete b.style.location:B(b)&&a(b.children)})}(c.val),c.val}!function(a){a.MISSING_VALUE="MISSING_VALUE",a.INVALID_VALUE="INVALID_VALUE",a.MISSING_INTL_API="MISSING_INTL_API"}(i||(i={}));var ak=function(a){function b(b,c,d){var e=a.call(this,b)||this;return e.code=c,e.originalMessage=d,e}return l(b,a),b.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},b}(Error),al=function(a){function b(b,c,d,e){return a.call(this,'Invalid values for "'.concat(b,'": "').concat(c,'". Options are "').concat(Object.keys(d).join('", "'),'"'),i.INVALID_VALUE,e)||this}return l(b,a),b}(ak),am=function(a){function b(b,c,d){return a.call(this,'Value for "'.concat(b,'" must be of type ').concat(c),i.INVALID_VALUE,d)||this}return l(b,a),b}(ak),an=function(a){function b(b,c){return a.call(this,'The intl string context variable "'.concat(b,'" was not provided to the string "').concat(c,'"'),i.MISSING_VALUE,c)||this}return l(b,a),b}(ak);function ao(a){return{create:function(){return{get:function(b){return a[b]},set:function(b,c){a[b]=c}}}}}!function(a){a[a.literal=0]="literal",a[a.object=1]="object"}(j||(j={}));var ap=function(){function a(b,c,d,f){void 0===c&&(c=a.defaultLocale);var g,h,k=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(a){var b=k.formatToParts(a);if(1===b.length)return b[0].value;var c=b.reduce(function(a,b){return a.length&&b.type===j.literal&&"string"==typeof a[a.length-1]?a[a.length-1]+=b.value:a.push(b.value),a},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(a){return function a(b,c,d,f,g,h,k){if(1===b.length&&v(b[0]))return[{type:j.literal,value:b[0].value}];for(var l=[],m=0;m<b.length;m++){var n=b[m];if(v(n)){l.push({type:j.literal,value:n.value});continue}if(n.type===e.pound){"number"==typeof h&&l.push({type:j.literal,value:d.getNumberFormat(c).format(h)});continue}var o=n.value;if(!(g&&o in g))throw new an(o,k);var p=g[o];if(n.type===e.argument){p&&"string"!=typeof p&&"number"!=typeof p||(p="string"==typeof p||"number"==typeof p?String(p):""),l.push({type:"string"==typeof p?j.literal:j.object,value:p});continue}if(x(n)){var q="string"==typeof n.style?f.date[n.style]:D(n.style)?n.style.parsedOptions:void 0;l.push({type:j.literal,value:d.getDateTimeFormat(c,q).format(p)});continue}if(y(n)){var q="string"==typeof n.style?f.time[n.style]:D(n.style)?n.style.parsedOptions:f.time.medium;l.push({type:j.literal,value:d.getDateTimeFormat(c,q).format(p)});continue}if(w(n)){var q="string"==typeof n.style?f.number[n.style]:C(n.style)?n.style.parsedOptions:void 0;q&&q.scale&&(p*=q.scale||1),l.push({type:j.literal,value:d.getNumberFormat(c,q).format(p)});continue}if(B(n)){var r=n.children,s=n.value,t=g[s];if("function"!=typeof t)throw new am(s,"function",k);var u=t(a(r,c,d,f,g,h).map(function(a){return a.value}));Array.isArray(u)||(u=[u]),l.push.apply(l,u.map(function(a){return{type:"string"==typeof a?j.literal:j.object,value:a}}))}if(z(n)){var E=n.options[p]||n.options.other;if(!E)throw new al(n.value,p,Object.keys(n.options),k);l.push.apply(l,a(E.value,c,d,f,g));continue}if(A(n)){var E=n.options["=".concat(p)];if(!E){if(!Intl.PluralRules)throw new ak('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',i.MISSING_INTL_API,k);var F=d.getPluralRules(c,{type:n.pluralType}).select(p-(n.offset||0));E=n.options[F]||n.options.other}if(!E)throw new al(n.value,p,Object.keys(n.options),k);l.push.apply(l,a(E.value,c,d,f,g,p-(n.offset||0)));continue}}return l.length<2?l:l.reduce(function(a,b){var c=a[a.length-1];return c&&c.type===j.literal&&b.type===j.literal?c.value+=b.value:a.push(b),a},[])}(k.ast,k.locales,k.formatters,k.formats,a,void 0,k.message)},this.resolvedOptions=function(){var a;return{locale:(null==(a=k.resolvedLocale)?void 0:a.toString())||Intl.NumberFormat.supportedLocalesOf(k.locales)[0]}},this.getAst=function(){return k.ast},this.locales=c,this.resolvedLocale=a.resolveLocale(c),"string"==typeof b){if(this.message=b,!a.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var l=f||{},p=(l.formatters,function(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}(l,["formatters"]));this.ast=a.__parse(b,m(m({},p),{locale:this.resolvedLocale}))}else this.ast=b;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(g=a.formats,d?Object.keys(g).reduce(function(a,b){var c,e;return a[b]=(c=g[b],(e=d[b])?m(m(m({},c||{}),e||{}),Object.keys(c).reduce(function(a,b){return a[b]=m(m({},c[b]),e[b]||{}),a},{})):c),a},m({},g)):g),this.formatters=f&&f.formatters||(void 0===(h=this.formatterCache)&&(h={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:o(function(){for(var a,b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return new((a=Intl.NumberFormat).bind.apply(a,n([void 0],b,!1)))},{cache:ao(h.number),strategy:u.variadic}),getDateTimeFormat:o(function(){for(var a,b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return new((a=Intl.DateTimeFormat).bind.apply(a,n([void 0],b,!1)))},{cache:ao(h.dateTime),strategy:u.variadic}),getPluralRules:o(function(){for(var a,b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return new((a=Intl.PluralRules).bind.apply(a,n([void 0],b,!1)))},{cache:ao(h.pluralRules),strategy:u.variadic})})}return Object.defineProperty(a,"defaultLocale",{get:function(){return a.memoizedDefaultLocale||(a.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),a.memoizedDefaultLocale},enumerable:!1,configurable:!0}),a.memoizedDefaultLocale=null,a.resolveLocale=function(a){if(void 0!==Intl.Locale){var b=Intl.NumberFormat.supportedLocalesOf(a);return new Intl.Locale(b.length>0?b[0]:"string"==typeof a?a:a[0])}},a.__parse=aj,a.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},a}(),aq=c(61120);class ar extends Error{constructor(a,b){let c=a;b&&(c+=": "+b),super(c),this.code=a,b&&(this.originalMessage=b)}}var as=function(a){return a.MISSING_MESSAGE="MISSING_MESSAGE",a.MISSING_FORMAT="MISSING_FORMAT",a.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",a.INSUFFICIENT_PATH="INSUFFICIENT_PATH",a.INVALID_MESSAGE="INVALID_MESSAGE",a.INVALID_KEY="INVALID_KEY",a.FORMATTING_ERROR="FORMATTING_ERROR",a}(as||{});function at(...a){return a.filter(Boolean).join(".")}function au(a){return at(a.namespace,a.key)}function av(a){console.error(a)}function aw(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function ax(a,b){return o(a,{cache:{create:()=>({get:a=>b[a],set(a,c){b[a]=c}})},strategy:u.variadic})}function ay(a,b){return ax((...b)=>new a(...b),b)}function az(a){return{getDateTimeFormat:ay(Intl.DateTimeFormat,a.dateTime),getNumberFormat:ay(Intl.NumberFormat,a.number),getPluralRules:ay(Intl.PluralRules,a.pluralRules),getRelativeTimeFormat:ay(Intl.RelativeTimeFormat,a.relativeTime),getListFormat:ay(Intl.ListFormat,a.list),getDisplayNames:ay(Intl.DisplayNames,a.displayNames)}}function aA(a,b,c,d){let e=at(d,c);if(!b)throw Error(e);let f=b;return c.split(".").forEach(b=>{let c=f[b];if(null==b||null==c)throw Error(e+` (${a})`);f=c}),f}function aB(a){let b=function(a,b,c,d=av){try{if(!b)throw Error(void 0);let d=c?aA(a,b,c):b;if(!d)throw Error(c);return d}catch(b){let a=new ar(as.MISSING_MESSAGE,b.message);return d(a),a}}(a.locale,a.messages,a.namespace,a.onError);return function({cache:a,formats:b,formatters:c,getMessageFallback:d=au,locale:e,messagesOrError:f,namespace:g,onError:h,timeZone:i}){let j=f instanceof ar;function k(a,b,c){let e=new ar(b,c);return h(e),d({error:e,key:a,namespace:g})}function l(h,l,m){var n;let o,p;if(j)return d({error:f,key:h,namespace:g});try{o=aA(e,f,h,g)}catch(a){return k(h,as.MISSING_MESSAGE,a.message)}if("object"==typeof o){let a;return k(h,Array.isArray(o)?as.INVALID_MESSAGE:as.INSUFFICIENT_PATH,a)}let q=(n=o,l||/'[{}]/.test(n)?void 0:n);if(q)return q;c.getMessageFormat||(c.getMessageFormat=ax((...a)=>new ap(a[0],a[1],a[2],{formatters:c,...a[3]}),a.message));try{p=c.getMessageFormat(o,e,function(a,b,c){let d=ap.formats.date,e=ap.formats.time,f={...a?.dateTime,...b?.dateTime},g={date:{...d,...f},time:{...e,...f},number:{...a?.number,...b?.number}};return c&&["date","time"].forEach(a=>{let b=g[a];for(let[a,d]of Object.entries(b))b[a]={timeZone:c,...d}}),g}(b,m,i),{formatters:{...c,getDateTimeFormat:(a,b)=>c.getDateTimeFormat(a,{timeZone:i,...b})}})}catch(a){return k(h,as.INVALID_MESSAGE,a.message)}try{let a=p.format(l?function(a){let b={};return Object.keys(a).forEach(c=>{let d,e=0,f=a[c];d="function"==typeof f?a=>{let b=f(a);return(0,aq.isValidElement)(b)?(0,aq.cloneElement)(b,{key:c+e++}):b}:f,b[c]=d}),b}(l):l);if(null==a)throw Error(void 0);return(0,aq.isValidElement)(a)||Array.isArray(a)||"string"==typeof a?a:String(a)}catch(a){return k(h,as.FORMATTING_ERROR,a.message)}}function m(a,b,c){let d=l(a,b,c);return"string"!=typeof d?k(a,as.INVALID_MESSAGE,void 0):d}return m.rich=l,m.markup=(a,b,c)=>l(a,b,c),m.raw=a=>{if(j)return d({error:f,key:a,namespace:g});try{return aA(e,f,a,g)}catch(b){return k(a,as.MISSING_MESSAGE,b.message)}},m.has=a=>{if(j)return!1;try{return aA(e,f,a,g),!0}catch{return!1}},m}({...a,messagesOrError:b})}function aC(a,b){return a===b?void 0:a.slice((b+".").length)}function aD({formats:a,getMessageFallback:b,messages:c,onError:d,...e}){return{...e,formats:a||void 0,messages:c||void 0,onError:d||av,getMessageFallback:b||au}}},14967:(a,b,c)=>{"use strict";c.d(b,{EL:()=>f,HM:()=>e});var d=c(2144);function e({_cache:a=(0,d.d)(),_formatters:b=(0,d.b)(a),getMessageFallback:c=d.f,messages:e,namespace:f,onError:g=d.g,...h}){return function({messages:a,namespace:b,...c},e){return a=a["!"],b=(0,d.r)(b,"!"),(0,d.e)({...c,messages:a,namespace:b})}({...h,onError:g,cache:a,formatters:b,getMessageFallback:c,messages:{"!":e},namespace:f?`!.${f}`:"!"},"!")}function f(a,b){return a.includes(b)}},29443:a=>{a.exports={style:{fontFamily:"'Noto Sans SC', 'Noto Sans SC Fallback'",fontStyle:"normal"},className:"__className_a19ac1",variable:"__variable_a19ac1"}},31858:()=>{},33856:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(85814),e=c(16189),f=c(43210),g=c(71330),h=c(60687),i=(0,f.forwardRef)(function({href:a,locale:b,localeCookie:c,onClick:f,prefetch:i,...j},k){let l=(0,g.Ym)(),m=null!=b&&b!==l,n=(0,e.usePathname)();return m&&(i=!1),(0,h.jsx)(d,{ref:k,href:a,hrefLang:m?b:void 0,onClick:function(a){(function(a,b,c,d){if(!a||d===c||null==d||!b)return;let e=function(a,b=window.location.pathname){return"/"===a?b:b.replace(a,"")}(b),{name:f,...g}=a;g.path||(g.path=""!==e?e:"/");let h=`${f}=${d};`;for(let[a,b]of Object.entries(g))h+=`${"maxAge"===a?"max-age":a}`,"boolean"!=typeof b&&(h+="="+b),h+=";";document.cookie=h})(c,n,l,b),f&&f(a)},prefetch:i,...j})})},35471:(a,b,c)=>{"use strict";function d(a){return a}c.d(b,{A:()=>d})},36478:()=>{},39916:(a,b,c)=>{"use strict";var d=c(97576);c.o(d,"notFound")&&c.d(b,{notFound:function(){return d.notFound}}),c.o(d,"permanentRedirect")&&c.d(b,{permanentRedirect:function(){return d.permanentRedirect}}),c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},42037:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(61120),e=c(67424),f=c(14967),g=(0,d.cache)(function(a,b){return(0,f.HM)({...a,namespace:b})}),h=(0,d.cache)(async function(a){let b,c;return"string"==typeof a?b=a:a&&(c=a.locale,b=a.namespace),g(await (0,e.A)(c),b)})},45196:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(71330),e=c(60687);function f({locale:a,...b}){if(!a)throw Error(void 0);return(0,e.jsx)(d.Dk,{locale:a,...b})}},48488:()=>{},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},55946:(a,b,c)=>{"use strict";function d(a){return a}c.d(b,{A:()=>d})},56025:(a,b,c)=>{"use strict";function d(a){return("object"==typeof a?null==a.host&&null==a.hostname:!/^[a-z]+:/i.test(a))&&!function(a){let b="object"==typeof a?a.pathname:a;return null!=b&&!b.startsWith("/")}(a)}function e(a,b){let c=a;return/^\/(\?.*)?$/.test(b)&&(b=b.slice(1)),c+=b}function f(a,b,c){return"string"==typeof a?a:a[b]||c}function g(a){let b=function(){try{return"true"===process.env._next_intl_trailing_slash}catch{return!1}}(),[c,...d]=a.split("#"),e=d.join("#"),f=c;if("/"!==f){let a=f.endsWith("/");b&&!a?f+="/":!b&&a&&(f=f.slice(0,-1))}return e&&(f+="#"+e),f}function h(a,b){return"never"!==b.mode&&b.prefixes?.[a]||"/"+a}function i(a){return"function"==typeof a.then}c.d(b,{PJ:()=>e,Wl:()=>f,XP:()=>h,_x:()=>d,po:()=>g,yL:()=>i})},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},64207:a=>{a.exports={style:{fontFamily:"'Noto Sans', 'Noto Sans Fallback'",fontStyle:"normal"},className:"__className_9ba018",variable:"__variable_9ba018"}},67424:(a,b,c)=>{"use strict";c.d(b,{A:()=>q});var d=c(61120),e=c(2144),f=c(56025);c(99933);var g=c(86280);c(73913);let h=(0,d.cache)(function(){return{locale:void 0}}),i=(0,d.cache)(async function(){let a=(0,g.b)();return(0,f.yL)(a)?await a:a}),j=(0,d.cache)(async function(){let a;try{a=(await i()).get("X-NEXT-INTL-LOCALE")||void 0}catch(a){if(a instanceof Error&&"DYNAMIC_SERVER_USAGE"===a.digest){let b=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:a});throw b.digest=a.digest,b}throw a}return a});async function k(){return h().locale||await j()}var l=c(72996);let m=(0,d.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),n=(0,d.cache)(async function(a,b){let c=a({locale:b,get requestLocale(){return b?Promise.resolve(b):k()}});if((0,f.yL)(c)&&(c=await c),!c.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return c}),o=(0,d.cache)(e.b),p=(0,d.cache)(e.d),q=(0,d.cache)(async function(a){let b=await n(l.A,a);return{...(0,e.i)(b),_formatters:o(p()),timeZone:b.timeZone||m()}})},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},73913:(a,b,c)=>{"use strict";let d=c(63033),e=c(29294),f=c(84971),g=c(76926),h=c(80023),i=c(98479),j=c(71617);c(43763);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},75788:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/production/navigation/shared/BaseLink.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/production/navigation/shared/BaseLink.js","default")},80994:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")},83448:()=>{},86280:(a,b,c)=>{"use strict";Object.defineProperty(b,"b",{enumerable:!0,get:function(){return m}});let d=c(92584),e=c(29294),f=c(63033),g=c(84971),h=c(80023),i=c(68388),j=c(76926);c(44523);let k=c(8719),l=c(71617);function m(){let a=e.workAsyncStorage.getStore(),b=f.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let e=n.get(c);if(e)return e;let f=(0,i.makeHangingPromise)(c.renderSignal,"`headers()`");return n.set(c,f),f;case"prerender-client":let j="`headers`";throw Object.defineProperty(new l.InvariantError(`${j} must not be used within a client component. Next.js should be preventing ${j} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,g.postponeWithTracking)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,g.throwToInterruptStaticGeneration)("headers",a,b)}(0,g.trackDynamicDataInDynamicRender)(a,b)}return o((0,f.getExpectedRequestStore)("headers").headers)}c(43763);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},92118:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(39916),e=c(61120),f=c.t(e,2)["use".trim()],g=c(56025),h=c(75788);function i(a){let b=new URLSearchParams;for(let[c,d]of Object.entries(a))Array.isArray(d)?d.forEach(a=>{b.append(c,String(a))}):b.set(c,String(d));return"?"+b.toString()}var j=c(37413),k=c(67424);async function l(){return(await (0,k.A)()).locale}function m(a){let{config:b,...c}=function(a,b){var c,k,l;let m={...c=b||{},localePrefix:"object"==typeof(l=c.localePrefix)?l:{mode:l||"always"},localeCookie:!!((k=c.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof k&&k},localeDetection:c.localeDetection??!0,alternateLinks:c.alternateLinks??!0},n=m.pathnames,o=(0,e.forwardRef)(function({href:b,locale:c,...d},e){let i,k;"object"==typeof b?(i=b.pathname,k=b.params):i=b;let l=(0,g._x)(b),o=a(),q=(0,g.yL)(o)?f(o):o,r=l?p({locale:c||q,href:null==n?i:{pathname:i,params:k},forcePrefix:null!=c||void 0}):i;return(0,j.jsx)(h.default,{ref:e,href:"object"==typeof b?{...b,pathname:r}:r,locale:c,localeCookie:m.localeCookie,...d})});function p(a){let b,{forcePrefix:c,href:d,locale:e}=a;return null==n?"object"==typeof d?(b=d.pathname,d.query&&(b+=i(d.query))):b=d:b=function({pathname:a,locale:b,params:c,pathnames:d,query:e}){function f(a){let f,h=d[a];return h?(f=(0,g.Wl)(h,b,a),c&&Object.entries(c).forEach(([a,b])=>{let c,d;Array.isArray(b)?(c=`(\\[)?\\[...${a}\\](\\])?`,d=b.map(a=>String(a)).join("/")):(c=`\\[${a}\\]`,d=String(b)),f=f.replace(RegExp(c,"g"),d)}),f=(f=f.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(a=>encodeURIComponent(a)).join("/")):f=a,f=(0,g.po)(f),e&&(f+=i(e)),f}if("string"==typeof a)return f(a);{let{pathname:b,...c}=a;return{...c,pathname:f(b)}}}({locale:e,..."string"==typeof d?{pathname:d}:d,pathnames:m.pathnames}),function(a,b,c,d){let e,{mode:f}=c.localePrefix;return void 0!==d?e=d:(0,g._x)(a)&&("always"===f?e=!0:"as-needed"===f&&(e=c.domains?!c.domains.some(a=>a.defaultLocale===b):b!==c.defaultLocale)),e?(0,g.PJ)((0,g.XP)(b,c.localePrefix),a):a}(b,e,m,c)}function q(a){return function(b,...c){return a(p(b),...c)}}return{config:m,Link:o,redirect:q(d.redirect),permanentRedirect:q(d.permanentRedirect),getPathname:p}}(l,a);function k(a){return()=>{throw Error(`\`${a}\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`)}}return{...c,usePathname:k("usePathname"),useRouter:k("useRouter")}}},94069:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(23158),e=c(43763),f=c(29294),g=c(63033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},96794:a=>{a.exports={style:{fontFamily:"'Noto Sans Thai', 'Noto Sans Thai Fallback'",fontStyle:"normal"},className:"__className_a060d5",variable:"__variable_a060d5"}},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},97663:(a,b,c)=>{"use strict";c.d(b,{A:()=>o});var d=c(61120),e=c(67424);let f=(0,d.cache)(async function(a){return(await (0,e.A)(a)).now}),g=(0,d.cache)(async function(){return(await (0,e.A)()).formats});var h=c(80994),i=c(37413);let j=(0,d.cache)(async function(a){return(await (0,e.A)(a)).timeZone});async function k(a){return j(a?.locale)}let l=(0,d.cache)(async function(a){var b=await (0,e.A)(a);if(!b.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return b.messages});async function m(a){return l(a?.locale)}let n=(0,d.cache)(async function(){return(await (0,e.A)()).locale});async function o({formats:a,locale:b,messages:c,now:d,timeZone:e,...j}){return(0,i.jsx)(h.default,{formats:void 0===a?await g():a,locale:b??await n(),messages:void 0===c?await m():c,now:d??await f(),timeZone:e??await k(),...j})}},99933:(a,b,c)=>{"use strict";let d=c(94069),e=c(23158),f=c(29294),g=c(63033),h=c(84971),i=c(80023),j=c(68388),k=c(76926);c(44523);c(8719),c(71617);c(43763);new WeakMap;(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})}};
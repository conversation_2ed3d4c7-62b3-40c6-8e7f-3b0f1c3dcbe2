exports.id=443,exports.ids=[443],exports.modules={24793:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413);function e(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"loader-wrap",children:[(0,d.jsx)("div",{className:"layer layer-one",children:(0,d.jsx)("span",{className:"overlay"})}),(0,d.jsx)("div",{className:"layer layer-two",children:(0,d.jsx)("span",{className:"overlay"})}),(0,d.jsx)("div",{className:"layer layer-three",children:(0,d.jsx)("span",{className:"overlay"})})]})})}function f(){return(0,d.jsx)(d.<PERSON>ag<PERSON>,{children:(0,d.jsx)(e,{})})}},31528:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(37413),e=c(42024),f=c(4536),g=c.n(f);function h(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(e.default,{headerStyle:3,footerStyle:4,breadcrumbTitle:"Page Not Found",children:(0,d.jsx)("section",{className:"error-page",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"error-page__inner text-center",children:[(0,d.jsx)("div",{className:"error-page__img float-bob-y",children:(0,d.jsx)("img",{src:"assets/images/resources/error-page-img1.png",alt:""})}),(0,d.jsxs)("div",{className:"error-page__content",children:[(0,d.jsx)("h2",{children:"Oops! Page Not Found!"}),(0,d.jsx)("p",{children:"The page you are looking for does not exist. It might have been moved or deleted."}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(g(),{className:"thm-btn",href:"/",children:["Back To Home",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})})})})})}},32648:(a,b,c)=>{"use strict";function d({children:a}){return a}c.r(b),c.d(b,{default:()=>d})},39900:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},42024:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/SAK WoodWorks/Website/Landing page/developing/intl2-companyprofile-v02/components/layout/Layout.js","default")},46055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},46057:(a,b,c)=>{Promise.resolve().then(c.bind(c,42024)),Promise.resolve().then(c.t.bind(c,4536,23))},70189:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(77618);function i(){let a=(0,h.c3)("HeaderBtn"),[b,c]=(0,e.useState)(!1);return(0,d.jsx)(d.Fragment,{children:b?(0,d.jsx)(g(),{href:`tel:${a("phone_no")}`,children:a("phone_no")}):(0,d.jsxs)("a",{style:{cursor:"pointer"},onClick:()=>{c(!b)},children:[a("phone_no").slice(0,7)," ****"]})})}},78335:()=>{},81756:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},87913:(a,b,c)=>{Promise.resolve().then(c.bind(c,96268)),Promise.resolve().then(c.t.bind(c,85814,23))},96268:(a,b,c)=>{"use strict";c.d(b,{default:()=>C});var d=c(60687),e=c(43210);function f({scroll:a}){return(0,d.jsx)(d.Fragment,{children:a&&(0,d.jsx)("a",{className:"scroll-to-top scroll-to-target d-block",href:"#top",children:(0,d.jsx)("i",{className:"fas fa-angle-up"})})})}function g(){return(0,d.jsx)(d.Fragment,{})}var h=c(85814),i=c.n(h);function j({breadcrumbTitle:a}){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("section",{className:"page-header",children:[(0,d.jsx)("div",{className:"page-header__bg",style:{backgroundImage:"url(assets/images/pattern/store-pattern.png)"}}),(0,d.jsx)("div",{className:"page-header__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/page-header-pattern.png",alt:""})}),(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"page-header__inner",children:[(0,d.jsx)("h2",{children:a}),(0,d.jsxs)("ul",{className:"thm-breadcrumb",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/",children:"Home"})}),(0,d.jsx)("li",{children:(0,d.jsx)("span",{className:"icon-right-arrow21"})}),(0,d.jsx)("li",{children:a})]})]})})]})})}function k({isPopup:a,handlePopup:b}){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:`search-popup ${a?"active":""}`,children:[(0,d.jsx)("div",{className:"search-popup__overlay search-toggler",onClick:b}),(0,d.jsx)("div",{className:"search-popup__content",children:(0,d.jsxs)("form",{action:"#",children:[(0,d.jsx)("label",{className:"sr-only",children:"search here"}),(0,d.jsx)("input",{type:"text",id:"search",placeholder:"Search Here..."}),(0,d.jsx)("button",{type:"submit","aria-label":"search submit",className:"thm-btn",children:(0,d.jsx)("i",{className:"icon-magnifying-glass"})})]})})]})})}function l({isSidebar:a,handleSidebar:b}){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:`xs-sidebar-group info-group info-sidebar ${a?"isActive":""}`,children:[(0,d.jsx)("div",{className:"xs-overlay xs-bg-black",onClick:b}),(0,d.jsx)("div",{className:"xs-sidebar-widget",children:(0,d.jsxs)("div",{className:"sidebar-widget-container",children:[(0,d.jsx)("div",{className:"widget-heading",children:(0,d.jsx)(i(),{href:"#",className:"close-side-widget",children:"X"})}),(0,d.jsx)("div",{className:"sidebar-textwidget",children:(0,d.jsx)("div",{className:"sidebar-info-contents",children:(0,d.jsxs)("div",{className:"content-inner",children:[(0,d.jsx)("div",{className:"logo",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"/assets/images/resources/sidebar-logo.png",alt:""})})}),(0,d.jsxs)("div",{className:"content-box",children:[(0,d.jsx)("h4",{children:"About Us"}),(0,d.jsx)("div",{className:"inner-text",children:(0,d.jsx)("p",{children:"Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classNameical Latin literature from 45 BC, making it over 2000 years old."})})]}),(0,d.jsxs)("div",{className:"form-inner",children:[(0,d.jsx)("h4",{children:"Get a free quote"}),(0,d.jsxs)("form",{action:"/",method:"post",children:[(0,d.jsx)("div",{className:"form-group",children:(0,d.jsx)("input",{type:"text",name:"name",placeholder:"Name",required:""})}),(0,d.jsx)("div",{className:"form-group",children:(0,d.jsx)("input",{type:"email",name:"email",placeholder:"Email",required:""})}),(0,d.jsx)("div",{className:"form-group",children:(0,d.jsx)("textarea",{name:"message",placeholder:"Message..."})}),(0,d.jsx)("div",{className:"form-group message-btn",children:(0,d.jsxs)("button",{className:"thm-btn",type:"submit","data-loading-text":"Please wait...",children:["Submit Now",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,d.jsxs)("div",{className:"sidebar-contact-info",children:[(0,d.jsx)("h4",{children:"Contact Info"}),(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("span",{className:"icon-location1"})," 88 broklyn street, New York"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("span",{className:"icon-phone"}),(0,d.jsx)(i(),{href:"tel:123456789",children:"******-9990-153"})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("span",{className:"fa fa-envelope"}),(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})]})]})]}),(0,d.jsx)("div",{className:"thm-social-link1",children:(0,d.jsxs)("ul",{className:"social-box",children:[(0,d.jsx)("li",{className:"facebook",children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("i",{className:"icon-facebook-f","aria-hidden":"true"})})}),(0,d.jsx)("li",{className:"twitter",children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("i",{className:"icon-twitter","aria-hidden":"true"})})}),(0,d.jsx)("li",{className:"linkedin",children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("i",{className:"icon-instagram","aria-hidden":"true"})})}),(0,d.jsx)("li",{className:"gplus",children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("i",{className:"icon-linkedin","aria-hidden":"true"})})})]})})]})})})]})})]})})}var m=c(77618);function n(){let a=(0,m.c3)("MenuData");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("ul",{className:"main-menu__list",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#about",children:a("nav1")})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#product",children:a("nav3")})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#service",children:a("nav2")})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#branch",children:a("nav4")})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#store",children:a("nav5")})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#contact",children:a("nav6")})})]})})}var o=c(71330),p=c(16189);let q=(0,c(85484).A)({locales:["th","en","ru","ch"],defaultLocale:"th"});function r(){let a=(0,o.Ym)(),b=(0,p.useRouter)(),c=(0,p.usePathname)(),[f,g]=(0,e.useTransition)(),h=q.locales.map(a=>({value:a,label:a.toUpperCase()})),i={en:"Eng",ru:"Рус",th:"ไทย",ch:"中"};return(0,d.jsxs)("div",{className:"main-header__language-switcher",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"fa fa-globe"})}),(0,d.jsx)("div",{className:"language-switcher clearfix",children:(0,d.jsx)("div",{className:"select-box clearfix",children:(0,d.jsx)("select",{className:"selectmenu wide",value:a,onChange:d=>{var e;(e=d.target.value)!==a&&g(()=>{let d=c.replace(`/${a}`,"")||"/",f=`/${e}${d}`;b.push(f)})},disabled:f,style:{cursor:"pointer"},children:h.map(a=>(0,d.jsx)("option",{value:a.value,children:i[a.value]},a.value))})})})]})}var s=c(70189);let t=({isSidebar:a,handleMobileMenu:b,handleSidebar:c})=>{let f=(0,m.c3)("MenuData");(0,m.c3)("HeaderBtn");let[g,h]=(0,e.useState)({status:!1,key:"",subMenuKey:""});return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"mobile-nav__wrapper",children:[(0,d.jsx)("div",{className:"mobile-nav__overlay mobile-nav__toggler",onClick:b}),(0,d.jsxs)("div",{className:"mobile-nav__content",children:[(0,d.jsx)("span",{className:"mobile-nav__close mobile-nav__toggler",onClick:b,children:(0,d.jsx)("i",{className:"fa fa-times"})}),(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/","aria-label":"logo image",children:(0,d.jsx)("img",{src:"/assets/images/resources/logo-wh.svg",width:"150",alt:""})})}),(0,d.jsx)("div",{className:"mobile-nav__container",children:(0,d.jsx)("div",{className:"collapse navbar-collapse show clearfix",id:"navbarSupportedContent",children:(0,d.jsx)("ul",{className:"main-menu__list",children:(0,d.jsxs)("li",{children:[(0,d.jsx)(i(),{href:"#about",children:f("nav1")}),(0,d.jsx)(i(),{href:"#product",children:f("nav3")}),(0,d.jsx)(i(),{href:"#service",children:f("nav2")}),(0,d.jsx)(i(),{href:"#branch",children:f("nav4")}),(0,d.jsx)(i(),{href:"#store",children:f("nav5")}),(0,d.jsx)(i(),{href:"#contact",children:f("nav6")})]})})})}),(0,d.jsx)("div",{className:"mobile-nav__container",children:(0,d.jsxs)("ul",{className:"mobile-nav__contact list-unstyled",children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("i",{className:"fa fa-phone-alt"}),(0,d.jsx)("div",{style:{color:"white"},children:(0,d.jsx)(s.A,{})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("i",{className:"fa fa-envelope"}),(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})]})]})}),(0,d.jsxs)("div",{className:"mobile-nav__social",children:[(0,d.jsx)(i(),{href:"https://www.facebook.com/Sakwwth",className:"fab fa-facebook-f"}),(0,d.jsx)(i(),{href:"https://www.facebook.com/Sakwwth",className:"fab fa-youtube"}),(0,d.jsx)(i(),{href:"https://www.tiktok.com/@sakwoodworks",className:"fab fa-tiktok"}),(0,d.jsx)(i(),{href:"https://www.instagram.com/sakwoodworks",className:"fab fa-instagram"}),(0,d.jsx)(i(),{href:"https://lin.ee/smwoT3j",className:"fab fa-line"})]})]})]})})};function u({scroll:a,handlePopup:b,handleMobileMenu:c}){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"main-header main-header-one",children:(0,d.jsx)("nav",{className:"main-menu",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-one__inner",children:[(0,d.jsx)("div",{className:"main-header-one__top",children:(0,d.jsxs)("div",{className:"main-header-one__top-inner",children:[(0,d.jsx)("div",{className:"main-header-one__top-left",children:(0,d.jsx)("div",{className:"header-contact-style1",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Talk to Us"})," ",(0,d.jsx)(i(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Mail Us"})," ",(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})}),(0,d.jsxs)("div",{className:"main-header-one__top-right",children:[(0,d.jsxs)("div",{className:"header-social-links",children:[(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})}),(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})}),(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})}),(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})]}),(0,d.jsx)("div",{className:"header-search-box",children:(0,d.jsxs)(i(),{href:"#",className:"main-menu__search search-toggler",onClick:b,children:["Search",(0,d.jsx)("i",{className:"icon-search"})]})})]})]})}),(0,d.jsx)("div",{className:"main-header-one__bottom",children:(0,d.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,d.jsxs)("div",{className:"main-header-one__bottom-inner",children:[(0,d.jsxs)("div",{className:"main-header-one__bottom-left",children:[(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/logo-1.png",alt:""})})}),(0,d.jsx)("div",{className:"main-header-one__bottom-menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:c,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})]}),(0,d.jsxs)("div",{className:"main-header-one__bottom-right",children:[(0,d.jsx)("div",{className:"main-header-one__bottom-right-btn",children:(0,d.jsxs)(i(),{href:"contact",children:["Track Order",(0,d.jsx)("i",{className:"icon-right-arrow21"})]})}),(0,d.jsx)("div",{className:"login-box",children:(0,d.jsxs)(i(),{href:"#",children:[(0,d.jsx)("i",{className:"fa fa-sign-in"})," ",(0,d.jsxs)("span",{children:["Member ",(0,d.jsx)("br",{}),"Login"]})," "]})})]})]})})})]})})})})}),(0,d.jsx)("div",{className:`stricky-header stricky-header--style1 stricked-menu main-menu ${a?"stricky-fixed":""}`,children:(0,d.jsx)("div",{className:"sticky-header__content",children:(0,d.jsx)("div",{className:"main-header-one__bottom",children:(0,d.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,d.jsxs)("div",{className:"main-header-one__bottom-inner",children:[(0,d.jsxs)("div",{className:"main-header-one__bottom-left",children:[(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/logo-1.png",alt:""})})}),(0,d.jsx)("div",{className:"main-header-one__bottom-menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:c,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})]}),(0,d.jsxs)("div",{className:"main-header-one__bottom-right",children:[(0,d.jsx)("div",{className:"main-header-one__bottom-right-btn",children:(0,d.jsxs)(i(),{href:"contact",children:["Track Order",(0,d.jsx)("i",{className:"icon-right-arrow21"})]})}),(0,d.jsx)("div",{className:"login-box",children:(0,d.jsxs)(i(),{href:"#",children:[(0,d.jsx)("i",{className:"fa fa-sign-in"})," ",(0,d.jsxs)("span",{children:["Member ",(0,d.jsx)("br",{}),"Login"]})," "]})})]})]})})})})}),(0,d.jsx)(t,{handleMobileMenu:c})]})}function v({scroll:a,handlePopup:b,handleSidebar:c,handleMobileMenu:e}){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"main-header main-header-two",children:(0,d.jsx)("nav",{className:"main-menu",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,d.jsxs)("div",{className:"main-header-two__inner",children:[(0,d.jsx)("div",{className:"logo-box-two",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/logo-2.png",alt:""})})}),(0,d.jsx)("div",{className:"main-header-two__top",children:(0,d.jsxs)("div",{className:"main-header-two__top-inner",children:[(0,d.jsx)("div",{className:"main-header-two__top-left",children:(0,d.jsx)("div",{className:"header-contact-style2",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-clock"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Opening Hours"}),(0,d.jsx)("p",{className:"text2",children:"Mon - Sat: 8am - 5pm"})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Send Us Mail"}),(0,d.jsx)("p",{className:"text2",children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone2"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Make A Call"}),(0,d.jsx)("p",{className:"text2",children:(0,d.jsx)(i(),{href:"tel:**********",children:"680 123 456 789"})})]})]})]})})}),(0,d.jsx)("div",{className:"main-header-two__top-right",children:(0,d.jsxs)("div",{className:"header-social-link-style2",children:[(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("p",{children:"Follow Us On:"})}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})})]})]})})]})}),(0,d.jsxs)("div",{className:"main-header-two__bottom",children:[(0,d.jsx)("div",{className:"shape1"}),(0,d.jsxs)("div",{className:"main-header-two__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-two__bottom-left",children:(0,d.jsx)("div",{className:"main-header-two__menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:e,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})}),(0,d.jsxs)("div",{className:"main-header-two__bottom-right",children:[(0,d.jsx)("div",{className:"header-search-box-two",children:(0,d.jsx)(i(),{href:"#",className:"main-menu__search search-toggler icon-search",onClick:b})}),(0,d.jsx)("div",{className:"sidebar-icon",children:(0,d.jsxs)(i(),{className:"navSidebar-button icon2",href:"#",onClick:c,children:[(0,d.jsx)("span",{className:"nav-sidebar-menu-1"}),(0,d.jsx)("span",{className:"nav-sidebar-menu-2"}),(0,d.jsx)("span",{className:"nav-sidebar-menu-3"})]})}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(i(),{className:"thm-btn",href:"contact",children:["Track Order",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})]})]})})})})})}),(0,d.jsx)("div",{className:`stricky-header stricky-header--style2 stricked-menu main-menu ${a?"stricky-fixed":""}`,children:(0,d.jsx)("div",{className:"sticky-header__content",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,d.jsxs)("div",{className:"main-header-two__inner",children:[(0,d.jsx)("div",{className:"logo-box-two",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/logo-2.png",alt:""})})}),(0,d.jsx)("div",{className:"main-header-two__top",children:(0,d.jsxs)("div",{className:"main-header-two__top-inner",children:[(0,d.jsx)("div",{className:"main-header-two__top-left",children:(0,d.jsx)("div",{className:"header-contact-style2",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-clock"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Opening Hours"}),(0,d.jsx)("p",{className:"text2",children:"Mon - Sat: 8am - 5pm"})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Send Us Mail"}),(0,d.jsx)("p",{className:"text2",children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone2"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{className:"text1",children:"Make A Call"}),(0,d.jsx)("p",{className:"text2",children:(0,d.jsx)(i(),{href:"tel:**********",children:"680 123 456 789"})})]})]})]})})}),(0,d.jsx)("div",{className:"main-header-two__top-right",children:(0,d.jsxs)("div",{className:"header-social-link-style2",children:[(0,d.jsx)("div",{className:"title-box",children:(0,d.jsx)("p",{children:"Follow Us On:"})}),(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})})]})]})})]})}),(0,d.jsxs)("div",{className:"main-header-two__bottom",children:[(0,d.jsx)("div",{className:"shape1"}),(0,d.jsxs)("div",{className:"main-header-two__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-two__bottom-left",children:(0,d.jsx)("div",{className:"main-header-two__menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:e,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})}),(0,d.jsxs)("div",{className:"main-header-two__bottom-right",children:[(0,d.jsx)("div",{className:"header-search-box-two",children:(0,d.jsx)(i(),{href:"#",className:"main-menu__search search-toggler icon-search",onClick:b})}),(0,d.jsx)("div",{className:"sidebar-icon",children:(0,d.jsxs)(i(),{className:"navSidebar-button icon2",href:"#",onClick:c,children:[(0,d.jsx)("span",{className:"nav-sidebar-menu-1"}),(0,d.jsx)("span",{className:"nav-sidebar-menu-2"}),(0,d.jsx)("span",{className:"nav-sidebar-menu-3"})]})}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(i(),{className:"thm-btn",href:"contact",children:["Track Order",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})]})]})})})})})}),(0,d.jsx)(t,{handleMobileMenu:e})]})}function w({scroll:a,handleMobileMenu:b}){let c=(0,m.c3)("HeaderBtn");return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"main-header main-header-three",children:(0,d.jsx)("nav",{className:"main-menu",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,d.jsx)("div",{className:"main-header-three__top",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-three__top-inner",children:[(0,d.jsx)("div",{className:"header-contact-style1",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)(s.A,{})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})})]})]})}),(0,d.jsx)("div",{className:"main-header-three__right",children:(0,d.jsxs)(i(),{className:"thm-btn2",href:"#contact",children:[c("btn_text"),(0,d.jsx)("span",{className:"hover-btn hover-cx"}),(0,d.jsx)("span",{className:"hover-btn hover-cx2"}),(0,d.jsx)("span",{className:"hover-btn hover-cx3"}),(0,d.jsx)("span",{className:"hover-btn hover-cx4"})]})})]})})}),(0,d.jsx)("div",{className:"main-header-three__bottom",style:{backgroundColor:"white"},children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-three__bottom-left",children:(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,d.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,d.jsx)(r,{}),(0,d.jsx)("div",{className:"main-header-three__menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:b,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})]})]})})})]})})})}),(0,d.jsx)("div",{className:`stricky-header stricky-header--style3 stricked-menu main-menu ${a?"stricky-fixed":""}`,children:(0,d.jsx)("div",{className:"sticky-header__content",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,d.jsx)("div",{className:"main-header-three__top",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"main-header-three__top-inner",children:(0,d.jsx)("div",{className:"header-contact-style1",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Talk to Us"})," ",(0,d.jsx)(i(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Mail Us"})," ",(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})})})}),(0,d.jsx)("div",{className:"main-header-three__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-three__bottom-left",children:(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,d.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,d.jsx)(r,{}),(0,d.jsx)("div",{className:"main-header-three__menu",children:(0,d.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:b,children:(0,d.jsx)("i",{className:"fa fa-bars"})}),(0,d.jsx)(n,{})]})})]})]})})})]})})})}),(0,d.jsx)(t,{handleMobileMenu:b})]})}function x({scroll:a,handleMobileMenu:b}){return(0,m.c3)("HeaderBtn"),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"main-header main-header-three",children:(0,d.jsx)("nav",{className:"main-menu",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,d.jsx)("div",{className:"main-header-three__bottom",style:{backgroundColor:"white",marginTop:"0px"},children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-three__bottom-left",children:(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,d.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,d.jsx)(r,{}),(0,d.jsx)("div",{className:"main-header-three__menu",children:(0,d.jsx)("div",{className:"main-menu__main-menu-box",children:(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:b,children:(0,d.jsx)("i",{className:"fa fa-bars"})})})})]})]})})})})})})}),(0,d.jsx)("div",{className:`stricky-header stricky-header--style3 stricked-menu main-menu ${a?"stricky-fixed":""}`,children:(0,d.jsx)("div",{className:"sticky-header__content",children:(0,d.jsx)("div",{className:"main-menu__wrapper",children:(0,d.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,d.jsx)("div",{className:"main-header-three__top",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"main-header-three__top-inner",children:(0,d.jsx)("div",{className:"header-contact-style1",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Talk to Us"})," ",(0,d.jsx)(i(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:[(0,d.jsx)("span",{children:"Mail Us"})," ",(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})})})}),(0,d.jsx)("div",{className:"main-header-three__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,d.jsx)("div",{className:"main-header-three__bottom-left",children:(0,d.jsx)("div",{className:"logo-box",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,d.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,d.jsx)(r,{}),(0,d.jsx)("div",{className:"main-header-three__menu",children:(0,d.jsx)("div",{className:"main-menu__main-menu-box",children:(0,d.jsx)(i(),{href:"#",className:"mobile-nav__toggler",onClick:b,children:(0,d.jsx)("i",{className:"fa fa-bars"})})})})]})]})})})]})})})}),(0,d.jsx)(t,{handleMobileMenu:b})]})}function y(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("footer",{className:"footer-one",children:[(0,d.jsx)("div",{className:"footer-one__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,d.jsx)("div",{className:"footer-one__top",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsx)("div",{className:"footer-one__top-inner",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,d.jsx)("div",{className:"footer-one__about-logo",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,d.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,d.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-support"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:"Make a Call"}),(0,d.jsx)("h4",{children:(0,d.jsx)(i(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Quick Links ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"/",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"about",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"service",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"project",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"contact",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Get In Touch ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsx)("div",{className:"footer-one__contact-box",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-address"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:["3060 Commercial Street Road ",(0,d.jsx)("br",{})," Fratton, Australia"]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Subscribe Us ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,d.jsx)("br",{}),"thoughts, and insights"]}),(0,d.jsx)("div",{className:"footer-one__subscribe-form",children:(0,d.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,d.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,d.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})})}),(0,d.jsx)("div",{className:"footer-one__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,d.jsx)("div",{className:"footer-one__bottom-text",children:(0,d.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,d.jsx)(i(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,d.jsx)("div",{className:"footer-one__social-links",children:(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function z(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,d.jsx)("div",{className:"footer-one__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,d.jsx)("div",{className:"shape3 float-bob-y",children:(0,d.jsx)("img",{src:"assets/images/shapes/footer-v2-shape3.png",alt:""})}),(0,d.jsx)("div",{className:"footer-one__top",children:(0,d.jsxs)("div",{className:"container",children:[(0,d.jsxs)("div",{className:"footer-one--two__cta",children:[(0,d.jsx)("div",{className:"shape1",children:(0,d.jsx)("img",{className:"float-bob-x3",src:"assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,d.jsx)("div",{className:"shape2",children:(0,d.jsx)("img",{className:"float-bob-y",src:"assets/images/shapes/footer-v2-shape1.png",alt:""})}),(0,d.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("h2",{children:"Efficient, Safe, & Swift Logistics Solution!"})}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(i(),{className:"thm-btn",href:"contact",children:["Contact with Us",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,d.jsx)("div",{className:"footer-one__top-inner",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,d.jsx)("div",{className:"footer-one__about-logo",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,d.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,d.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-support"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:"Make a Call"}),(0,d.jsx)("h4",{children:(0,d.jsx)(i(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Quick Links ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"/",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"about",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"service",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"project",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"contact",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Get In Touch ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsx)("div",{className:"footer-one__contact-box",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-address"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:["3060 Commercial Street Road ",(0,d.jsx)("br",{})," Fratton, Australia"]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Subscribe Us ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,d.jsx)("br",{}),"thoughts, and insights"]}),(0,d.jsx)("div",{className:"footer-one__subscribe-form",children:(0,d.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,d.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,d.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})]})}),(0,d.jsx)("div",{className:"footer-one__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,d.jsx)("div",{className:"footer-one__bottom-text",children:(0,d.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,d.jsx)(i(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,d.jsx)("div",{className:"footer-one__social-links",children:(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function A(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,d.jsx)("div",{className:"footer-one__pattern",children:(0,d.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,d.jsx)("div",{className:"shape3 float-bob-y",children:(0,d.jsx)("img",{src:"assets/images/shapes/footer-v2-shape3.png",alt:""})}),(0,d.jsx)("div",{className:"footer-one__top",children:(0,d.jsxs)("div",{className:"container",children:[(0,d.jsxs)("div",{className:"footer-one--two__cta",children:[(0,d.jsx)("div",{className:"shape1",children:(0,d.jsx)("img",{className:"float-bob-x3",src:"assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,d.jsx)("div",{className:"shape2",children:(0,d.jsx)("img",{className:"float-bob-y",src:"assets/images/shapes/footer-v2-shape1.png",alt:""})}),(0,d.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("h2",{children:"Efficient, Safe, & Swift Logistics Solution!"})}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(i(),{className:"thm-btn",href:"contact",children:["Contact with Us",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,d.jsx)("div",{className:"footer-one__top-inner",children:(0,d.jsxs)("div",{className:"row",children:[(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,d.jsx)("div",{className:"footer-one__about-logo",children:(0,d.jsx)(i(),{href:"/",children:(0,d.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,d.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,d.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-support"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:"Make a Call"}),(0,d.jsx)("h4",{children:(0,d.jsx)(i(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Quick Links ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"/",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"about",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"service",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"project",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,d.jsx)("li",{children:(0,d.jsxs)(i(),{href:"contact",children:[(0,d.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Get In Touch ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsx)("div",{className:"footer-one__contact-box",children:(0,d.jsxs)("ul",{children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-address"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("p",{children:["3060 Commercial Street Road ",(0,d.jsx)("br",{})," Fratton, Australia"]})})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-email"})}),(0,d.jsxs)("div",{className:"text-box",children:[(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("div",{className:"icon",children:(0,d.jsx)("span",{className:"icon-phone"})}),(0,d.jsx)("div",{className:"text-box",children:(0,d.jsx)("p",{children:(0,d.jsx)(i(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,d.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,d.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,d.jsx)("div",{className:"title",children:(0,d.jsxs)("h2",{children:["Subscribe Us ",(0,d.jsx)("span",{className:"icon-plane3"})]})}),(0,d.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,d.jsx)("br",{}),"thoughts, and insights"]}),(0,d.jsx)("div",{className:"footer-one__subscribe-form",children:(0,d.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,d.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,d.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,d.jsx)("i",{className:"icon-right-arrow21"}),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})]})}),(0,d.jsx)("div",{className:"footer-one__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,d.jsx)("div",{className:"footer-one__bottom-text",children:(0,d.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,d.jsx)(i(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,d.jsx)("div",{className:"footer-one__social-links",children:(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-twitter1"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"#",children:(0,d.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function B(){let a=(0,m.c3)("Footer"),b=(0,m.c3)("HeaderBtn");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,d.jsx)("div",{className:"footer-one__pattern",children:(0,d.jsx)("img",{src:"/assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,d.jsx)("div",{className:"footer-four__top",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"footer-one--two__cta",children:[(0,d.jsx)("div",{className:"shape1",children:(0,d.jsx)("img",{className:"float-bob-x3",src:"/assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,d.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,d.jsx)("div",{className:"text-box",children:(0,d.jsxs)("h2",{children:[" ",a("title")," "]})}),(0,d.jsx)("div",{className:"btn-box",children:(0,d.jsxs)(i(),{className:"thm-btn",href:`tel:${b("phone_no")}`,children:[(0,d.jsx)("i",{className:"icon-phone",style:{paddingRight:"8px"}}),a("btn_text"),(0,d.jsx)("span",{className:"hover-btn hover-bx"}),(0,d.jsx)("span",{className:"hover-btn hover-bx2"}),(0,d.jsx)("span",{className:"hover-btn hover-bx3"}),(0,d.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})})}),(0,d.jsx)("div",{className:"footer-one__bottom",children:(0,d.jsx)("div",{className:"container",children:(0,d.jsxs)("div",{className:"footer-one__bottom-inner",style:{border:"none"},children:[(0,d.jsx)("div",{className:"footer-one__bottom-text",children:(0,d.jsxs)("p",{children:["\xa9 ",a("copy_right")," ",(0,d.jsxs)(i(),{href:"/",target:"_blank",children:[" ",a("company")," "]})," ",a("all_right")]})}),(0,d.jsx)("div",{className:"footer-one__social-links",children:(0,d.jsxs)("ul",{children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"https://www.facebook.com/Sakwwth",target:"_blank",children:(0,d.jsx)("span",{className:"fab fa-facebook-f"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"https://www.youtube.com/@sakwoodworks",target:"_blank",children:(0,d.jsx)("span",{className:"fab fa-youtube"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"https://www.tiktok.com/@sakwoodworks",target:"_blank",children:(0,d.jsx)("span",{className:"fab fa-tiktok"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"https://www.instagram.com/sakwoodworks",target:"_blank",children:(0,d.jsx)("span",{className:"fab fa-instagram"})})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"https://lin.ee/3sa8cDh",target:"_blank",children:(0,d.jsx)("span",{className:"fab fa-line"})})})]})})]})})})]})})}function C({headerStyle:a,footerStyle:b,headTitle:c,breadcrumbTitle:h,children:i,wrapperCls:m}){let[n,o]=(0,e.useState)(0),[p,q]=(0,e.useState)(!1),r=()=>{q(!p),p?document.body.classList.remove("mobile-menu-visible"):document.body.classList.add("mobile-menu-visible")},[s,t]=(0,e.useState)(!1),C=()=>t(!s),[D,E]=(0,e.useState)(!1),F=()=>E(!D);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g,{}),(0,d.jsxs)("div",{className:`page-wrapper ${m||""}`,id:"#top",children:[!a&&(0,d.jsx)(u,{scroll:n,isMobileMenu:p,handleMobileMenu:r,handlePopup:C,isSidebar:D,handleSidebar:F}),1==a?(0,d.jsx)(u,{scroll:n,isMobileMenu:p,handleMobileMenu:r,handlePopup:C,isSidebar:D,handleSidebar:F}):null,2==a?(0,d.jsx)(v,{scroll:n,isMobileMenu:p,handleMobileMenu:r,handlePopup:C,isSidebar:D,handleSidebar:F}):null,3==a?(0,d.jsx)(w,{scroll:n,isMobileMenu:p,handleMobileMenu:r,handlePopup:C,isSidebar:D,handleSidebar:F}):null,4==a?(0,d.jsx)(x,{scroll:n,isMobileMenu:p,handleMobileMenu:r,handlePopup:C,isSidebar:D,handleSidebar:F}):null,(0,d.jsx)(l,{isSidebar:D,handleSidebar:F}),(0,d.jsx)(k,{isPopup:s,handlePopup:C}),h&&(0,d.jsx)(j,{breadcrumbTitle:h}),i,!b&&(0,d.jsx)(y,{}),1==b?(0,d.jsx)(y,{}):null,2==b?(0,d.jsx)(z,{}):null,3==b?(0,d.jsx)(A,{}):null,4==b?(0,d.jsx)(B,{}):null]}),(0,d.jsx)(f,{scroll:n})]})}},96487:()=>{}};
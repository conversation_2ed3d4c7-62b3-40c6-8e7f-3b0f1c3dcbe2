(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[155],{772:(e,t,i)=>{"use strict";i.d(t,{A:()=>ec});var r=i(2115);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e}).apply(this,arguments)}let s={origin:[0,0,0],round:4},a={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function l(e){let t=e.pathValue[e.segmentStart],i=t.toLowerCase(),{data:r}=e;for(;r.length>=a[i]&&("m"===i&&r.length>2?(e.segments.push([t,...r.splice(0,2)]),i="l",t="m"===t?"l":"L"):e.segments.push([t,...r.splice(0,a[i])]),a[i]););}let o="SVGPathCommander error";function u(e){return e>=48&&e<=57}let c="Invalid path value";function d(e){var t;let{pathValue:i,max:r}=e;for(;e.index<r&&(10===(t=i.charCodeAt(e.index))||13===t||8232===t||8233===t||32===t||9===t||11===t||12===t||160===t||t>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(t));)e.index+=1}function p(e){let{max:t,pathValue:i,index:r}=e,n=i.charCodeAt(r),s=a[i[r].toLowerCase()];if(e.segmentStart=r,function(e){switch(32|e){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}(n))if(e.index+=1,d(e),e.data=[],s){for(;;){var p;for(let r=s;r>0;r-=1){if(97!=(32|n)||3!==r&&4!==r?function(e){let{max:t,pathValue:i,index:r}=e,n,s=r,a=!1,l=!1,d=!1,p=!1;if(s>=t)e.err=`${o}: ${c} at index ${s}, "pathValue" is missing param`;else if(43!==(n=i.charCodeAt(s))&&45!==n||(s+=1,n=i.charCodeAt(s)),u(n)||46===n){if(46!==n){if(a=48===n,s+=1,n=i.charCodeAt(s),a&&s<t&&n&&u(n))return e.err=`${o}: ${c} at index ${r}, "${i[r]}" illegal number`;for(;s<t&&u(i.charCodeAt(s));)s+=1,l=!0;n=i.charCodeAt(s)}if(46===n){for(p=!0,s+=1;u(i.charCodeAt(s));)s+=1,d=!0;n=i.charCodeAt(s)}if(101===n||69===n){if(p&&!l&&!d)return e.err=`${o}: ${c} at index ${s}, "${i[s]}" invalid float exponent`;if(s+=1,43!==(n=i.charCodeAt(s))&&45!==n||(s+=1),!(s<t&&u(i.charCodeAt(s))))return e.err=`${o}: ${c} at index ${s}, "${i[s]}" invalid integer exponent`;for(;s<t&&u(i.charCodeAt(s));)s+=1}e.index=s,e.param=+e.pathValue.slice(r,s)}else e.err=`${o}: ${c} at index ${s}, "${i[s]}" is not a number`}(e):function(e){let{index:t,pathValue:i}=e,r=i.charCodeAt(t);48===r?(e.param=0,e.index+=1):49===r?(e.param=1,e.index+=1):e.err=`${o}: invalid Arc flag "${i[t]}", expecting 0 or 1 at index ${t}`}(e),e.err.length)return;e.data.push(e.param),d(e),e.index<t&&44===i.charCodeAt(e.index)&&(e.index+=1,d(e))}if(e.index>=e.max||!((p=i.charCodeAt(e.index))>=48&&p<=57||43===p||45===p||46===p))break}l(e)}else l(e);else e.err=`${o}: ${c} "${i[r]}" is not a path command`}function h(e){return e.map(e=>Array.isArray(e)?[...e]:e)}function m(e){this.segments=[],this.pathValue=e,this.max=e.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""}function f(e){return Array.isArray(e)&&e.every(e=>{let t=e[0].toLowerCase();return a[t]===e.length-1&&"achlmqstvz".includes(t)})}function g(e){if(f(e))return h(e);let t=new m(e);for(d(t);t.index<t.max&&!t.err.length;)p(t);return t.err?t.err:t.segments}function v(e){return f(e)&&e.every(([e])=>e===e.toUpperCase())}function y(e){if(v(e))return h(e);let t=g(e),i=0,r=0,n=0,s=0;return t.map(e=>{let t=e.slice(1).map(Number),[a]=e,l=a.toUpperCase();if("M"===a)return[i,r]=t,n=i,s=r,["M",i,r];let o=[];if(a!==l)switch(l){case"A":o=[l,t[0],t[1],t[2],t[3],t[4],t[5]+i,t[6]+r];break;case"V":o=[l,t[0]+r];break;case"H":o=[l,t[0]+i];break;default:o=[l,...t.map((e,t)=>e+(t%2?r:i))]}else o=[l,...t];let u=o.length;switch(l){case"Z":i=n,r=s;break;case"H":[,i]=o;break;case"V":[,r]=o;break;default:i=o[u-2],r=o[u-1],"M"===l&&(n=i,s=r)}return o})}function b(e){return f(e)&&e.slice(1).every(([e])=>e===e.toLowerCase())}function w(e){if(b(e))return h(e);let t=g(e),i=0,r=0,n=0,s=0;return t.map(e=>{let t=e.slice(1).map(Number),[a]=e,l=a.toLowerCase();if("M"===a)return[i,r]=t,n=i,s=r,["M",i,r];let o=[];if(a!==l)switch(l){case"a":o=[l,t[0],t[1],t[2],t[3],t[4],t[5]-i,t[6]-r];break;case"v":o=[l,t[0]-r];break;case"h":o=[l,t[0]-i];break;default:o=[l,...t.map((e,t)=>e-(t%2?r:i))]}else"m"===a&&(n=t[0]+i,s=t[1]+r),o=[l,...t];let u=o.length;switch(l){case"z":i=n,r=s;break;case"h":i+=o[1];break;case"v":r+=o[1];break;default:i+=o[u-2],r+=o[u-1]}return o})}function x(e,t,i){if(e[i].length>7){e[i].shift();let r=e[i],n=i;for(;r.length;)t[i]="A",e.splice(n+=1,0,["C",...r.splice(0,6)]);e.splice(i,1)}}function E(e){return v(e)&&e.every(([e])=>"ACLMQZ".includes(e))}function S(e){return E(e)&&e.every(([e])=>"MC".includes(e))}let C={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function M(e){if(E(e))return h(e);let t=y(e),i={...C},r=t.length;for(let e=0;e<r;e+=1){t[e],t[e]=function(e,t){let[i]=e,{x1:r,y1:n,x2:s,y2:a}=t,l=e.slice(1).map(Number),o=e;if("TQ".includes(i)||(t.qx=null,t.qy=null),"H"===i)o=["L",e[1],n];else if("V"===i)o=["L",r,e[1]];else if("S"===i){let e=2*r-s,i=2*n-a;t.x1=e,t.y1=i,o=["C",e,i,...l]}else if("T"===i){let e=2*r-t.qx,i=2*n-t.qy;t.qx=e,t.qy=i,o=["Q",e,i,...l]}else if("Q"===i){let[e,i]=l;t.qx=e,t.qy=i}return o}(t[e],i);let r=t[e],n=r.length;i.x1=+r[n-2],i.y1=+r[n-1],i.x2=+r[n-4]||i.x1,i.y2=+r[n-3]||i.y1}return t}function T(e,t,i){return{x:e*Math.cos(i)-t*Math.sin(i),y:e*Math.sin(i)+t*Math.cos(i)}}function P(e,t,i){let[r,n]=e,[s,a]=t;return[r+(s-r)*i,n+(a-n)*i]}function k(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}function A(e,t,i,r,n){let s=k([e,t],[i,r]),a={x:0,y:0};if("number"==typeof n)if(n<=0)a={x:e,y:t};else if(n>=s)a={x:i,y:r};else{let[l,o]=P([e,t],[i,r],n/s);a={x:l,y:o}}return{length:s,point:a,min:{x:Math.min(e,i),y:Math.min(t,r)},max:{x:Math.max(e,i),y:Math.max(t,r)}}}function L(e,t,i,r){let n=[e,t],s=[i,r],a=P(n,s,.5),l=P(s,a,.5),o=P(a,l,.5),u=P(l,o,.5),c=P(o,u,.5),d=A(...n,...a,...o,...c,.5).point,p=A(...c,...u,...l,...s,0).point;return[d.x,d.y,p.x,p.y,i,r]}function N(e,t){let[i]=e,r=e.slice(1).map(Number),[n,s]=r,{x1:a,y1:l,x:o,y:u}=t;switch("TQ".includes(i)||(t.qx=null,t.qy=null),i){case"M":return t.x=n,t.y=s,e;case"A":return["C",...function e(t,i,r,n,s,a,l,o,u,c){let d=t,p=i,h=r,m=n,f=o,g=u,v=120*Math.PI/180,y=Math.PI/180*(+s||0),b,w,x,E,S,C=[];if(c)[w,x,E,S]=c;else{d=(b=T(d,p,-y)).x,p=b.y,f=(b=T(f,g,-y)).x,g=b.y;let e=(d-f)/2,t=(p-g)/2,i=e*e/(h*h)+t*t/(m*m);i>1&&(h*=i=Math.sqrt(i),m*=i);let r=h*h,n=m*m,s=(a===l?-1:1)*Math.sqrt(Math.abs((r*n-r*t*t-n*e*e)/(r*t*t+n*e*e)));E=s*h*t/m+(d+f)/2,S=-(s*m)*e/h+(p+g)/2,w=Math.asin(((p-S)/m*1e9|0)/1e9),x=Math.asin(((g-S)/m*1e9|0)/1e9),w=d<E?Math.PI-w:w,x=f<E?Math.PI-x:x,w<0&&(w=2*Math.PI+w),x<0&&(x=2*Math.PI+x),l&&w>x&&(w-=2*Math.PI),!l&&x>w&&(x-=2*Math.PI)}let M=x-w;if(Math.abs(M)>v){let t=x,i=f,r=g;C=e(f=E+h*Math.cos(x=w+v*(l&&x>w?1:-1)),g=S+m*Math.sin(x),h,m,s,0,l,i,r,[x,t,E,S])}M=x-w;let P=Math.cos(w),k=Math.cos(x),A=Math.tan(M/4),L=4/3*h*A,N=4/3*m*A,O=[d,p],I=[d+L*Math.sin(w),p-N*P],z=[f+L*Math.sin(x),g-N*k],_=[f,g];if(I[0]=2*O[0]-I[0],I[1]=2*O[1]-I[1],c)return[...I,...z,..._,...C];C=[...I,...z,..._,...C];let $=[];for(let e=0,t=C.length;e<t;e+=1)$[e]=e%2?T(C[e-1],C[e],y).y:T(C[e],C[e+1],y).x;return $}(a,l,...r)];case"Q":return t.qx=n,t.qy=s,["C",...function(e,t,i,r,n,s){return[1/3*e+2/3*i,1/3*t+2/3*r,1/3*n+2/3*i,1/3*s+2/3*r,n,s]}(a,l,...r)];case"L":return["C",...L(a,l,n,s)];case"Z":return["C",...L(a,l,o,u)]}return e}function O(e){if(S(e))return h(e);let t=M(e),i={...C},r=[],n="",s=t.length;for(let e=0;e<s;e+=1){[n]=t[e],r[e]=n,t[e]=N(t[e],i),x(t,r,e),s=t.length;let a=t[e],l=a.length;i.x1=+a[l-2],i.y1=+a[l-1],i.x2=+a[l-4]||i.x1,i.y2=+a[l-3]||i.y1}return t}function I(e,t){let{round:i}=s;if("off"===t||"off"===i)return h(e);let r="number"==typeof(i=t>=0?t:i)&&i>=1?10**i:1;return e.map(e=>{let t=e.slice(1).map(Number).map(e=>i?Math.round(e*r)/r:Math.round(e));return[e[0],...t]})}function z(e,t){return I(e,t).map(e=>e[0]+e.slice(1).join(" ")).join("")}function _(e){let t=y(e),i="Z"===t.slice(-1)[0][0],r=M(t).map((e,i)=>{let[r,n]=e.slice(-2).map(Number);return{seg:t[i],n:e,c:t[i][0],x:r,y:n}}).map((e,t,r)=>{let n=e.seg,s=e.n,a=t&&r[t-1],l=r[t+1],o=e.c,u=r.length,c=t?r[t-1].x:r[u-1].x,d=t?r[t-1].y:r[u-1].y,p=[];switch(o){case"M":p=i?["Z"]:[o,c,d];break;case"A":p=[o,...n.slice(1,-3),+(1!==n[5]),c,d];break;case"C":p=l&&"S"===l.c?["S",n[1],n[2],c,d]:[o,n[3],n[4],n[1],n[2],c,d];break;case"S":p=a&&"CS".includes(a.c)&&(!l||"S"!==l.c)?["C",s[3],s[4],s[1],s[2],c,d]:[o,s[1],s[2],c,d];break;case"Q":p=l&&"T"===l.c?["T",c,d]:[o,...n.slice(1,-2),c,d];break;case"T":p=a&&"QT".includes(a.c)&&(!l||"T"!==l.c)?["Q",s[1],s[2],c,d]:[o,c,d];break;case"Z":p=["M",c,d];break;case"H":p=[o,c];break;case"V":p=[o,d];break;default:p=[o,...n.slice(1,-2),c,d]}return p});return i?r.reverse():[r[0],...r.slice(1).reverse()]}function $(e){let t=[],i,r=-1;return e.forEach(e=>{"M"===e[0]?(i=[e],r+=1):i=[...i,e],t[r]=i}),t}function D(e,t){let i=y(e),r=M(i),n={...C},s=[],a=i.length,l="",o="",u=0,c=0,d=0,p=0;for(let e=0;e<a;e+=1){[l]=i[e],s[e]=l,e&&(o=s[e-1]),i[e]=function(e,t,i,r){let[n]=e,s=e=>Math.round(1e4*e)/1e4,a=e.slice(1).map(e=>+e),l=t.slice(1).map(e=>+e),{x1:o,y1:u,x2:c,y2:d,x:p,y:h}=i,m=e,[f,g]=l.slice(-2);if("TQ".includes(n)||(i.qx=null,i.qy=null),["V","H","S","T","Z"].includes(n))m=[n,...a];else if("L"===n)s(p)===s(f)?m=["V",g]:s(h)===s(g)&&(m=["H",f]);else if("C"===n){let[e,t]=l;"CS".includes(r)&&(s(e)===s(2*o-c)&&s(t)===s(2*u-d)||s(o)===s(2*c-p)&&s(u)===s(2*d-h))&&(m=["S",...l.slice(-4)]),i.x1=e,i.y1=t}else if("Q"===n){let[e,t]=l;i.qx=e,i.qy=t,"QT".includes(r)&&(s(e)===s(2*o-c)&&s(t)===s(2*u-d)||s(o)===s(2*c-p)&&s(u)===s(2*d-h))&&(m=["T",...l.slice(-2)])}return m}(i[e],r[e],n,o);let t=i[e],a=t.length;switch(n.x1=+t[a-2],n.y1=+t[a-1],n.x2=+t[a-4]||n.x1,n.y2=+t[a-3]||n.y1,l){case"Z":u=d,c=p;break;case"H":[,u]=t;break;case"V":[,c]=t;break;default:[u,c]=t.slice(-2).map(Number),"M"===l&&(d=u,p=c)}n.x=u,n.y=c}let h=I(i,t),m=I(w(i),t);return h.map((e,t)=>t?e.join("").length<m[t].join("").length?e:m[t]:e)}function R(e){let t=new U,i=Array.from(e);if(!i.every(e=>!Number.isNaN(e)))throw TypeError(`CSSMatrix: "${e}" must only have numbers.`);if(16===i.length){let[e,r,n,s,a,l,o,u,c,d,p,h,m,f,g,v]=i;t.m11=e,t.a=e,t.m21=a,t.c=a,t.m31=c,t.m41=m,t.e=m,t.m12=r,t.b=r,t.m22=l,t.d=l,t.m32=d,t.m42=f,t.f=f,t.m13=n,t.m23=o,t.m33=p,t.m43=g,t.m14=s,t.m24=u,t.m34=h,t.m44=v}else{if(6!==i.length)throw TypeError("CSSMatrix: expecting an Array of 6/16 values.");{let[e,r,n,s,a,l]=i;t.m11=e,t.a=e,t.m12=r,t.b=r,t.m21=n,t.c=n,t.m22=s,t.d=s,t.m41=a,t.e=a,t.m42=l,t.f=l}}return t}function F(e){let t=Object.keys(new U);if("object"==typeof e&&t.every(t=>t in e))return R([e.m11,e.m12,e.m13,e.m14,e.m21,e.m22,e.m23,e.m24,e.m31,e.m32,e.m33,e.m34,e.m41,e.m42,e.m43,e.m44]);throw TypeError(`CSSMatrix: "${JSON.stringify(e)}" is not a DOMMatrix / CSSMatrix / JSON compatible object.`)}function j(e){if("string"!=typeof e)throw TypeError(`CSSMatrix: "${e}" is not a string.`);let t=String(e).replace(/\s/g,""),i=new U,r=`CSSMatrix: invalid transform string "${e}"`;return t.split(")").filter(e=>e).forEach(e=>{let[t,n]=e.split("(");if(!n)throw TypeError(r);let s=n.split(",").map(e=>e.includes("rad")?parseFloat(e)*(180/Math.PI):parseFloat(e)),[a,l,o,u]=s,c=[a,l,o],d=[a,l,o,u];if("perspective"===t&&a&&[l,o].every(e=>void 0===e))i.m34=-1/a;else if(t.includes("matrix")&&[6,16].includes(s.length)&&s.every(e=>!Number.isNaN(+e))){let e=s.map(e=>1e-6>Math.abs(e)?0:e);i=i.multiply(R(e))}else if("translate3d"===t&&c.every(e=>!Number.isNaN(+e)))i=i.translate(a,l,o);else if("translate"===t&&a&&void 0===o)i=i.translate(a,l||0,0);else if("rotate3d"===t&&d.every(e=>!Number.isNaN(+e))&&u)i=i.rotateAxisAngle(a,l,o,u);else if("rotate"===t&&a&&[l,o].every(e=>void 0===e))i=i.rotate(0,0,a);else if("scale3d"===t&&c.every(e=>!Number.isNaN(+e))&&c.some(e=>1!==e))i=i.scale(a,l,o);else if("scale"!==t||Number.isNaN(a)||1===a||void 0!==o)if("skew"===t&&(a||!Number.isNaN(a)&&l)&&void 0===o)i=i.skew(a,l||0);else{if(!(/[XYZ]/.test(t)&&a&&[l,o].every(e=>void 0===e)&&["translate","rotate","scale","skew"].some(e=>t.includes(e))))throw TypeError(r);if(["skewX","skewY"].includes(t))i=i[t](a);else{let e=t.replace(/[XYZ]/,""),r=["X","Y","Z"].indexOf(t.replace(e,"")),n=+("scale"===e),s=[0===r?a:n,1===r?a:n,2===r?a:n];i=i[e](...s)}}else{let e=Number.isNaN(+l)?a:l;i=i.scale(a,e,1)}}),i}function V(e,t){return t?[e.a,e.b,e.c,e.d,e.e,e.f]:[e.m11,e.m12,e.m13,e.m14,e.m21,e.m22,e.m23,e.m24,e.m31,e.m32,e.m33,e.m34,e.m41,e.m42,e.m43,e.m44]}function H(e,t,i){let r=new U;return r.m41=e,r.e=e,r.m42=t,r.f=t,r.m43=i,r}function q(e,t,i){let r=new U,n=Math.PI/180,s=e*n,a=t*n,l=i*n,o=Math.cos(s),u=-Math.sin(s),c=Math.cos(a),d=-Math.sin(a),p=Math.cos(l),h=-Math.sin(l),m=c*p,f=-c*h;r.m11=m,r.a=m,r.m12=f,r.b=f,r.m13=d;let g=u*d*p+o*h;r.m21=g,r.c=g;let v=o*p-u*d*h;return r.m22=v,r.d=v,r.m23=-u*c,r.m31=u*h-o*d*p,r.m32=u*p+o*d*h,r.m33=o*c,r}function B(e,t,i,r){let n=new U,s=Math.sqrt(e*e+t*t+i*i);if(0===s)return n;let a=e/s,l=t/s,o=i/s,u=Math.PI/360*r,c=Math.sin(u),d=Math.cos(u),p=c*c,h=a*a,m=l*l,f=o*o,g=1-2*(m+f)*p;n.m11=g,n.a=g;let v=2*(a*l*p+o*c*d);n.m12=v,n.b=v,n.m13=2*(a*o*p-l*c*d);let y=2*(l*a*p-o*c*d);n.m21=y,n.c=y;let b=1-2*(f+h)*p;return n.m22=b,n.d=b,n.m23=2*(l*o*p+a*c*d),n.m31=2*(o*a*p+l*c*d),n.m32=2*(o*l*p-a*c*d),n.m33=1-2*(h+m)*p,n}function G(e,t,i){let r=new U;return r.m11=e,r.a=e,r.m22=t,r.d=t,r.m33=i,r}function W(e,t){let i=new U;if(e){let t=Math.tan(e*Math.PI/180);i.m21=t,i.c=t}if(t){let e=Math.tan(t*Math.PI/180);i.m12=e,i.b=e}return i}function X(e){return W(e,0)}function Y(e){return W(0,e)}function Z(e,t){return R([t.m11*e.m11+t.m12*e.m21+t.m13*e.m31+t.m14*e.m41,t.m11*e.m12+t.m12*e.m22+t.m13*e.m32+t.m14*e.m42,t.m11*e.m13+t.m12*e.m23+t.m13*e.m33+t.m14*e.m43,t.m11*e.m14+t.m12*e.m24+t.m13*e.m34+t.m14*e.m44,t.m21*e.m11+t.m22*e.m21+t.m23*e.m31+t.m24*e.m41,t.m21*e.m12+t.m22*e.m22+t.m23*e.m32+t.m24*e.m42,t.m21*e.m13+t.m22*e.m23+t.m23*e.m33+t.m24*e.m43,t.m21*e.m14+t.m22*e.m24+t.m23*e.m34+t.m24*e.m44,t.m31*e.m11+t.m32*e.m21+t.m33*e.m31+t.m34*e.m41,t.m31*e.m12+t.m32*e.m22+t.m33*e.m32+t.m34*e.m42,t.m31*e.m13+t.m32*e.m23+t.m33*e.m33+t.m34*e.m43,t.m31*e.m14+t.m32*e.m24+t.m33*e.m34+t.m34*e.m44,t.m41*e.m11+t.m42*e.m21+t.m43*e.m31+t.m44*e.m41,t.m41*e.m12+t.m42*e.m22+t.m43*e.m32+t.m44*e.m42,t.m41*e.m13+t.m42*e.m23+t.m43*e.m33+t.m44*e.m43,t.m41*e.m14+t.m42*e.m24+t.m43*e.m34+t.m44*e.m44])}class U{constructor(...e){if(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0,this.m11=1,this.m12=0,this.m13=0,this.m14=0,this.m21=0,this.m22=1,this.m23=0,this.m24=0,this.m31=0,this.m32=0,this.m33=1,this.m34=0,this.m41=0,this.m42=0,this.m43=0,this.m44=1,e.length){let t=[16,6].some(t=>t===e.length)?e:e[0];return this.setMatrixValue(t)}return this}get isIdentity(){return 1===this.m11&&0===this.m12&&0===this.m13&&0===this.m14&&0===this.m21&&1===this.m22&&0===this.m23&&0===this.m24&&0===this.m31&&0===this.m32&&1===this.m33&&0===this.m34&&0===this.m41&&0===this.m42&&0===this.m43&&1===this.m44}get is2D(){return 0===this.m31&&0===this.m32&&1===this.m33&&0===this.m34&&0===this.m43&&1===this.m44}setMatrixValue(e){return"string"==typeof e&&e.length&&"none"!==e?j(e):[Array,Float64Array,Float32Array].some(t=>e instanceof t)?R(e):[U,DOMMatrix,Object].some(t=>e instanceof t)?F(e):this}toFloat32Array(e){return Float32Array.from(V(this,e))}toFloat64Array(e){return Float64Array.from(V(this,e))}toString(){let{is2D:e}=this;return`${e?"matrix":"matrix3d"}(${this.toFloat64Array(e).join(", ")})`}toJSON(){let{is2D:e,isIdentity:t}=this;return{...this,is2D:e,isIdentity:t}}multiply(e){return Z(this,e)}translate(e,t,i){let r=t,n=i;return void 0===r&&(r=0),void 0===n&&(n=0),Z(this,H(e,r,n))}scale(e,t,i){let r=t,n=i;return void 0===r&&(r=e),void 0===n&&(n=1),Z(this,G(e,r,n))}rotate(e,t,i){let r=e,n=t||0,s=i||0;return"number"==typeof e&&void 0===t&&void 0===i&&(s=r,r=0,n=0),Z(this,q(r,n,s))}rotateAxisAngle(e,t,i,r){if([e,t,i,r].some(e=>Number.isNaN(+e)))throw TypeError("CSSMatrix: expecting 4 values");return Z(this,B(e,t,i,r))}skewX(e){return Z(this,X(e))}skewY(e){return Z(this,Y(e))}skew(e,t){return Z(this,W(e,t))}transformPoint(e){let t=this.m11*e.x+this.m21*e.y+this.m31*e.z+this.m41*e.w,i=this.m12*e.x+this.m22*e.y+this.m32*e.z+this.m42*e.w,r=this.m13*e.x+this.m23*e.y+this.m33*e.z+this.m43*e.w,n=this.m14*e.x+this.m24*e.y+this.m34*e.z+this.m44*e.w;return e instanceof DOMPoint?new DOMPoint(t,i,r,n):{x:t,y:i,z:r,w:n}}}function K(e,t,i){var r;let n,[s,a,l]=i,[o,u,c]=(n=H(...r=[...t,0,1]),[,,,n.m44]=r,[(n=e.multiply(n)).m41,n.m42,n.m43,n.m44]),d=c-l;return[(o-s)*(Math.abs(l)/Math.abs(d)||1)+s,(u-a)*(Math.abs(l)/Math.abs(d)||1)+a]}function Q(e,t){let i,r,n,a,l,o,u=0,c=0,d=y(e),p=t&&Object.keys(t);if(!t||!p.length)return h(d);let m=M(d);if(!t.origin){let{origin:e}=s;Object.assign(t,{origin:e})}let f=function(e){let t=new U,{origin:i}=e,[r,n]=i,{translate:s}=e,{rotate:a}=e,{skew:l}=e,{scale:o}=e;return Array.isArray(s)&&s.every(e=>!Number.isNaN(+e))&&s.some(e=>0!==e)?t=t.translate(...s):"number"!=typeof s||Number.isNaN(s)||(t=t.translate(s)),(a||l||o)&&(t=t.translate(r,n),Array.isArray(a)&&a.every(e=>!Number.isNaN(+e))&&a.some(e=>0!==e)?t=t.rotate(...a):"number"!=typeof a||Number.isNaN(a)||(t=t.rotate(a)),Array.isArray(l)&&l.every(e=>!Number.isNaN(+e))&&l.some(e=>0!==e)?(t=l[0]?t.skewX(l[0]):t,t=l[1]?t.skewY(l[1]):t):"number"!=typeof l||Number.isNaN(l)||(t=t.skewX(l)),Array.isArray(o)&&o.every(e=>!Number.isNaN(+e))&&o.some(e=>1!==e)?t=t.scale(...o):"number"!=typeof o||Number.isNaN(o)||(t=t.scale(o)),t=t.translate(-r,-n)),t}(t),{origin:g}=t,v={...C},b=[],w=0,E="",S=[],T=[];if(!f.isIdentity){for(i=0,n=d.length;i<n;i+=1)b=d[i],d[i]&&([E]=b),T[i]=E,"A"===E&&(b=N(m[i],v),d[i]=N(m[i],v),x(d,T,i),m[i]=N(m[i],v),x(m,T,i),n=Math.max(d.length,m.length)),w=(b=m[i]).length,v.x1=+b[w-2],v.y1=+b[w-1],v.x2=+b[w-4]||v.x1,v.y2=+b[w-3]||v.y1,S=[...S,{s:d[i],c:d[i][0],x:v.x1,y:v.y1}];return S.map(e=>{switch(E=e.c,b=e.s,E){case"L":case"H":case"V":return[l,o]=K(f,[e.x,e.y],g),u!==l&&c!==o?b=["L",l,o]:c===o?b=["H",l]:u===l&&(b=["V",o]),u=l,c=o,b;default:for(r=1,a=b.length;r<a;r+=2)[u,c]=K(f,[+b[r],+b[r+1]],g),b[r]=u,b[r+1]=c;return b}})}return h(d)}function J(e,t){let{x:i,y:r}=e,{x:n,y:s}=t,a=Math.sqrt((i**2+r**2)*(n**2+s**2));return(i*s-r*n<0?-1:1)*Math.acos((i*n+r*s)/a)}function ee(e,t){let i=M(e),r="number"==typeof t,n,s,a,l=[],o=0,u=0,c=0,d=0,p=[],h=[],m=0,f={x:0,y:0},g=f,v=f,y=f,b=0;for(let e=0,w=i.length;e<w;e+=1)a=i[e],[s]=a,l=(n="M"===s)?l:[o,u,...a.slice(1)],n?([,c,d]=a,g=f={x:c,y:d},m=0,r&&t<.001&&(y=f)):"L"===s?{length:m,min:f,max:g,point:v}=A(...l,(t||0)-b):"A"===s?{length:m,min:f,max:g,point:v}=function(e,t,i,r,n,s,a,l,o,u){let c="number"==typeof u,d=e,p=t,h=0,m=[d,p,0],f=[d,p],g=0,v={x:0,y:0},y=[{x:d,y:p}];c&&u<=0&&(v={x:d,y:p});for(let b=0;b<=300;b+=1){if(g=b/300,{x:d,y:p}=function(e,t,i,r,n,s,a,l,o,u){let{abs:c,sin:d,cos:p,sqrt:h,PI:m}=Math,f=c(i),g=c(r),v=(n%360+360)%360*(m/180);if(e===l&&t===o)return{x:e,y:t};if(0===f||0===g)return A(e,t,l,o,u).point;let y=(e-l)/2,b=(t-o)/2,w=p(v)*y+d(v)*b,x=-d(v)*y+p(v)*b,E=w**2/f**2+x**2/g**2;E>1&&(f*=h(E),g*=h(E));let S=(f**2*g**2-f**2*x**2-g**2*w**2)/(f**2*x**2+g**2*w**2),C=(s!==a?1:-1)*h(S=S<0?0:S),M=f*x/g*C,T=-g*w/f*C,P=p(v)*M-d(v)*T+(e+l)/2,k=d(v)*M+p(v)*T+(t+o)/2,L={x:(w-M)/f,y:(x-T)/g},N=J({x:1,y:0},L),O=J(L,{x:(-w-M)/f,y:(-x-T)/g});!a&&O>0?O-=2*m:a&&O<0&&(O+=2*m);let I=N+(O%=2*m)*u,z=f*p(I),_=g*d(I);return{x:p(v)*z-d(v)*_+P,y:d(v)*z+p(v)*_+k}}(e,t,i,r,n,s,a,l,o,g),y=[...y,{x:d,y:p}],h+=k(f,[d,p]),f=[d,p],c&&h>u&&u>m[2]){let e=(h-u)/(h-m[2]);v={x:f[0]*(1-e)+m[0]*e,y:f[1]*(1-e)+m[1]*e}}m=[d,p,h]}return c&&u>=h&&(v={x:l,y:o}),{length:h,point:v,min:{x:Math.min(...y.map(e=>e.x)),y:Math.min(...y.map(e=>e.y))},max:{x:Math.max(...y.map(e=>e.x)),y:Math.max(...y.map(e=>e.y))}}}(...l,(t||0)-b):"C"===s?{length:m,min:f,max:g,point:v}=function(e,t,i,r,n,s,a,l,o){let u="number"==typeof o,c=e,d=t,p=0,h=[c,d,0],m=[c,d],f=0,g={x:0,y:0},v=[{x:c,y:d}];u&&o<=0&&(g={x:c,y:d});for(let y=0;y<=300;y+=1){if(f=y/300,{x:c,y:d}=function(e,t,i,r,n,s,a,l,o){let u=1-o;return{x:u**3*e+3*u**2*o*i+3*u*o**2*n+o**3*a,y:u**3*t+3*u**2*o*r+3*u*o**2*s+o**3*l}}(e,t,i,r,n,s,a,l,f),v=[...v,{x:c,y:d}],p+=k(m,[c,d]),m=[c,d],u&&p>o&&o>h[2]){let e=(p-o)/(p-h[2]);g={x:m[0]*(1-e)+h[0]*e,y:m[1]*(1-e)+h[1]*e}}h=[c,d,p]}return u&&o>=p&&(g={x:a,y:l}),{length:p,point:g,min:{x:Math.min(...v.map(e=>e.x)),y:Math.min(...v.map(e=>e.y))},max:{x:Math.max(...v.map(e=>e.x)),y:Math.max(...v.map(e=>e.y))}}}(...l,(t||0)-b):"Q"===s?{length:m,min:f,max:g,point:v}=function(e,t,i,r,n,s,a){let l="number"==typeof a,o=e,u=t,c=0,d=[o,u,0],p=[o,u],h=0,m={x:0,y:0},f=[{x:o,y:u}];l&&a<=0&&(m={x:o,y:u});for(let g=0;g<=300;g+=1){if(h=g/300,{x:o,y:u}=function(e,t,i,r,n,s,a){let l=1-a;return{x:l**2*e+2*l*a*i+a**2*n,y:l**2*t+2*l*a*r+a**2*s}}(e,t,i,r,n,s,h),f=[...f,{x:o,y:u}],c+=k(p,[o,u]),p=[o,u],l&&c>a&&a>d[2]){let e=(c-a)/(c-d[2]);m={x:p[0]*(1-e)+d[0]*e,y:p[1]*(1-e)+d[1]*e}}d=[o,u,c]}return l&&a>=c&&(m={x:n,y:s}),{length:c,point:m,min:{x:Math.min(...f.map(e=>e.x)),y:Math.min(...f.map(e=>e.y))},max:{x:Math.max(...f.map(e=>e.x)),y:Math.max(...f.map(e=>e.y))}}}(...l,(t||0)-b):"Z"===s&&(l=[o,u,c,d],{length:m,min:f,max:g,point:v}=A(...l,(t||0)-b)),r&&b<t&&b+m>=t&&(y=v),h=[...h,g],p=[...p,f],b+=m,[o,u]="Z"!==s?a.slice(-2):[c,d];return r&&t>=b&&(y={x:o,y:u}),{length:b,point:y,min:{x:Math.min(...p.map(e=>e.x)),y:Math.min(...p.map(e=>e.y))},max:{x:Math.max(...h.map(e=>e.x)),y:Math.max(...h.map(e=>e.y))}}}function et(e){if(!e)return{x:0,y:0,width:0,height:0,x2:0,y2:0,cx:0,cy:0,cz:0};let{min:{x:t,y:i},max:{x:r,y:n}}=ee(e),s=r-t,a=n-i;return{width:s,height:a,x:t,y:i,x2:r,y2:n,cx:t+s/2,cy:i+a/2,cz:Math.max(s,a)+Math.min(s,a)/2}}function ei(e){return ee(e).length}function er(e,t){return ee(e,t).point}Object.assign(U,{Translate:H,Rotate:q,RotateAxisAngle:B,Scale:G,SkewX:X,SkewY:Y,Skew:W,Multiply:Z,fromArray:R,fromMatrix:F,fromString:j,toArray:V}),Object.assign(U,{Version:"1.0.3"});class en{constructor(e,t){let i,r,n=void 0===e;if(n||!e.length)throw TypeError(`${o}: "pathValue" is ${n?"undefined":"empty"}`);let a=g(e);if("string"==typeof a)throw TypeError(a);this.segments=a;let{width:l,height:u,cx:c,cy:d,cz:p}=this.getBBox(),{round:h,origin:m}=t||{};if("auto"===h){let e=`${Math.floor(Math.max(l,u))}`.length;i=e>=4?0:4-e}else Number.isInteger(h)||"off"===h?i=h:{round:i}=s;if(Array.isArray(m)&&m.length>=2){let[e,t,i]=m.map(Number);r=[Number.isNaN(e)?c:e,Number.isNaN(t)?d:t,Number.isNaN(i)?p:i]}else r=[c,d,p];return this.round=i,this.origin=r,this}getBBox(){return et(this.segments)}getTotalLength(){return ei(this.segments)}getPointAtLength(e){return er(this.segments,e)}toAbsolute(){let{segments:e}=this;return this.segments=y(e),this}toRelative(){let{segments:e}=this;return this.segments=w(e),this}toCurve(){let{segments:e}=this;return this.segments=O(e),this}reverse(e){this.toAbsolute();let{segments:t}=this,i=$(t),r=i.length>1?i:0,n=r&&h(r).map((t,i)=>e?i?_(t):g(t):_(t)),s=[];return s=r?n.flat(1):e?t:_(t),this.segments=h(s),this}normalize(){let{segments:e}=this;return this.segments=M(e),this}optimize(){let{segments:e}=this;return this.segments=D(e,this.round),this}transform(e){if(!e||"object"!=typeof e||"object"==typeof e&&!["translate","rotate","skew","scale"].some(t=>t in e))return this;let t={};Object.keys(e).forEach(i=>{t[i]=Array.isArray(e[i])?[...e[i]]:Number(e[i])});let{segments:i}=this,[r,n,s]=this.origin,{origin:a}=t;if(Array.isArray(a)&&a.length>=2){let[e,i,l]=a.map(Number);t.origin=[Number.isNaN(e)?r:e,Number.isNaN(i)?n:i,l||s]}else t.origin=[r,n,s];return this.segments=Q(i,t),this}flipX(){return this.transform({rotate:[0,180,0]}),this}flipY(){return this.transform({rotate:[180,0,0]}),this}toString(){return z(this.segments,this.round)}}function es(e){let t=0,i=0,r=0;return O(e).map(e=>"M"===e[0]?([,t,i]=e,0):(r=function(e,t,i,r,n,s,a,l){return 3*((l-t)*(i+n)-(a-e)*(r+s)+r*(e-n)-i*(t-s)+l*(n+e/3)-a*(s+t/3))/20}(t,i,...e.slice(1)),[t,i]=e.slice(-2),r)).reduce((e,t)=>e+t,0)}function ea(e,t){let i=g(e);if("string"==typeof i)throw TypeError(i);let r=[...i],n=ei(r),s=r.length-1,a=0,l=0,o=i[0],[u,c]=o.slice(-2);if(s<=0||!t||!Number.isFinite(t))return{segment:o,index:0,length:l,point:{x:u,y:c},lengthAtSegment:a};if(t>=n)return l=n-(a=ei(r=i.slice(0,-1))),{segment:i[s],index:s,length:l,lengthAtSegment:a};let d=[];for(;s>0;)o=r[s],l=n-(a=ei(r=r.slice(0,-1))),n=a,d.push({segment:o,index:s,length:l,lengthAtSegment:a}),s-=1;return d.find(({lengthAtSegment:e})=>e<=t)}function el(e,t){let i=g(e),r=M(i),n=ei(i),s=e=>{let i=e.x-t.x,r=e.y-t.y;return i*i+r*r},a,l,o,u,c=8,d=0,p=0,h=1/0;for(let e=0;e<=n;e+=c)(d=s(a=er(r,e)))<h&&(l=a,p=e,h=d);c/=2;let m=0,f=0,v=0,y=0;for(;c>.5;)v=s(o=er(r,m=p-c)),y=s(u=er(r,f=p+c)),m>=0&&v<h?(l=o,p=m,h=v):f<=n&&y<h?(l=u,p=f,h=y):c/=2;return{closest:l,distance:Math.sqrt(h),segment:ea(i,p)}}function eo(e){if("string"!=typeof e)return!1;let t=new m(e);for(d(t);t.index<t.max&&!t.err.length;)p(t);return!t.err.length&&"mM".includes(t.segments[0][0])}let eu={line:["x1","y1","x2","y2"],circle:["cx","cy","r"],ellipse:["cx","cy","rx","ry"],rect:["width","height","x","y","rx","ry"],polygon:["points"],polyline:["points"],glyph:["d"]};Object.assign(en,{CSSMatrix:U,parsePathString:g,isPathArray:f,isCurveArray:S,isAbsoluteArray:v,isRelativeArray:b,isNormalizedArray:E,isValidPath:eo,pathToAbsolute:y,pathToRelative:w,pathToCurve:O,pathToString:z,getDrawDirection:function(e){return es(O(e))>=0},getPathArea:es,getPathBBox:et,pathLengthFactory:ee,getTotalLength:ei,getPointAtLength:er,getClosestPoint:function(e,t){return el(e,t).closest},getSegmentOfPoint:function(e,t){return el(e,t).segment},getPropertiesAtPoint:el,getPropertiesAtLength:ea,getSegmentAtLength:function(e,t){return ea(e,t).segment},isPointInStroke:function(e,t){let{distance:i}=el(e,t);return .001>Math.abs(i)},clonePath:h,splitPath:$,fixPath:function(e){let t=g(e),i=M(t),{length:r}=t,n="Z"===i.slice(-1)[0][0],[s,a]=i[0].slice(1),[l,o]=i[n?r-2:r-1].slice(-2);return n&&s===l&&a===o?t.slice(0,-1):t},roundPath:I,optimizePath:D,reverseCurve:function(e){let t=e.slice(1).map((t,i,r)=>i?[...r[i-1].slice(-2),...t.slice(1)]:[...e[0].slice(1),...t.slice(1)]).map(e=>e.map((t,i)=>e[e.length-i-2*(1-i%2)])).reverse();return[["M",...t[0].slice(0,2)],...t.map(e=>["C",...e.slice(2)])]},reversePath:_,normalizePath:M,transformPath:Q,shapeToPath:function(e,t){let i,r=Object.keys(eu),{tagName:n}=e;if(n&&!r.some(e=>n===e))throw TypeError(`${o}: "${n}" is not SVGElement`);let a=document.createElementNS("http://www.w3.org/2000/svg","path"),l=n||e.type,u={};u.type=l;let c=eu[l];n?(c.forEach(t=>{u[t]=e.getAttribute(t)}),Object.values(e.attributes).forEach(({name:e,value:t})=>{c.includes(e)||a.setAttribute(e,t)})):(Object.assign(u,e),Object.keys(u).forEach(e=>{c.includes(e)||"type"===e||a.setAttribute(e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`),u[e])}));let{round:d}=s;return"circle"===l?i=z(function(e){let{cx:t,cy:i,r:r}=e;return[["M",t-r,i],["a",r,r,0,1,0,2*r,0],["a",r,r,0,1,0,-2*r,0]]}(u),d):"ellipse"===l?i=z(function(e){let{cx:t,cy:i,rx:r,ry:n}=e;return[["M",t-r,i],["a",r,n,0,1,0,2*r,0],["a",r,n,0,1,0,-2*r,0]]}(u),d):["polyline","polygon"].includes(l)?i=z(function(e){let t=[],i=(e.points||"").trim().split(/[\s|,]/).map(Number),r=0;for(;r<i.length;)t.push([r?"L":"M",i[r],i[r+1]]),r+=2;return"polygon"===e.type?[...t,["z"]]:t}(u),d):"rect"===l?i=z(function(e){let t=+e.x||0,i=+e.y||0,r=+e.width,n=+e.height,s=+e.rx,a=+e.ry;return s||a?(s=s||a,a=a||s,2*s>r&&(s-=(2*s-r)/2),2*a>n&&(a-=(2*a-n)/2),[["M",t+s,i],["h",r-2*s],["s",s,0,s,a],["v",n-2*a],["s",0,a,-s,a],["h",2*s-r],["s",-s,0,-s,-a],["v",2*a-n],["s",0,-a,s,-a]]):[["M",t,i],["h",r],["v",n],["H",t],["Z"]]}(u),d):"line"===l?i=z(function(e){let{x1:t,y1:i,x2:r,y2:n}=e;return[["M",t,i],["L",r,n]]}(u),d):"glyph"===l&&(i=n?e.getAttribute("d"):e.d),!!eo(i)&&(a.setAttribute("d",i),t&&n&&(e.before(a,e),e.remove()),a)},options:s},{Version:"1.0.5"});let ec=e=>{let{width:t,height:i,cx:s,cy:a,rx:l,ry:o,startOffset:u,reversed:c,text:d,svgProps:p,ellipseProps:h,textPathProps:m,textProps:f,tspanProps:g}=e,[v,y]=(0,r.useState)(!1),[b]=(0,r.useState)(`ellipse-id${(0,r.useId)().replaceAll(":","-")}`),w=(0,r.useRef)();if((0,r.useEffect)(()=>{if(w.current){let e={id:b,type:"ellipse",rx:l,ry:o,cx:s,cy:a,style:"fill: none;",...h},t=w.current,i=en.shapeToPath(e,!0),r=document.getElementById(b);if(r&&r.remove(),t.prepend(i),c){let e=i.getAttribute("d"),t=en.reversePath(e),r=en.pathToString(t);i.setAttribute("d",r)}y(!0)}},[w.current,c,t,i,p,s,a,l,o,h]),null==t)throw Error("ReactCurvedText Error: width is required");if(null==i)throw Error("ReactCurvedText Error: height is required");if(null==s)throw Error("ReactCurvedText Error: cx is required");if(null==a)throw Error("ReactCurvedText Error: cy is required");if(null==l)throw Error("ReactCurvedText Error: rx is required");if(null==o)throw Error("ReactCurvedText Error: ry is required");if(null==d)throw Error("ReactCurvedText Error: text is required");let x=JSON.stringify({width:t,height:i,cx:s,cy:a,rx:l,ry:o,startOffset:u,reversed:c,text:d,svgProps:p,ellipseProps:h,textPathProps:m,textProps:f,tspanProps:g,rendered:v});return r.createElement("svg",n({ref:w,height:i,width:t},p),r.createElement("text",n({key:x},f),r.createElement("textPath",n({xlinkHref:`#${b}`,startOffset:u},m),r.createElement("tspan",g,d))))}},1606:function(){(function(){var e,t,i,r,n,s=function(e,t){return function(){return e.apply(t,arguments)}},a=[].indexOf||function(e){for(var t=0,i=this.length;t<i;t++)if(t in this&&this[t]===e)return t;return -1};t=function(){function e(){}return e.prototype.extend=function(e,t){var i,r;for(i in t)r=t[i],null==e[i]&&(e[i]=r);return e},e.prototype.isMobile=function(e){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)},e.prototype.createEvent=function(e,t,i,r){var n;return null==t&&(t=!1),null==i&&(i=!1),null==r&&(r=null),null!=document.createEvent?(n=document.createEvent("CustomEvent")).initCustomEvent(e,t,i,r):null!=document.createEventObject?(n=document.createEventObject()).eventType=e:n.eventName=e,n},e.prototype.emitEvent=function(e,t){return null!=e.dispatchEvent?e.dispatchEvent(t):t in(null!=e)?e[t]():"on"+t in(null!=e)?e["on"+t]():void 0},e.prototype.addEvent=function(e,t,i){return null!=e.addEventListener?e.addEventListener(t,i,!1):null!=e.attachEvent?e.attachEvent("on"+t,i):e[t]=i},e.prototype.removeEvent=function(e,t,i){return null!=e.removeEventListener?e.removeEventListener(t,i,!1):null!=e.detachEvent?e.detachEvent("on"+t,i):delete e[t]},e.prototype.innerHeight=function(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight},e}(),i=this.WeakMap||this.MozWeakMap||(i=function(){function e(){this.keys=[],this.values=[]}return e.prototype.get=function(e){var t,i,r,n;for(n=this.keys,t=i=0,r=n.length;i<r;t=++i)if(n[t]===e)return this.values[t]},e.prototype.set=function(e,t){var i,r,n,s;for(s=this.keys,i=r=0,n=s.length;r<n;i=++r)if(s[i]===e){this.values[i]=t;return}return this.keys.push(e),this.values.push(t)},e}()),e=this.MutationObserver||this.WebkitMutationObserver||this.MozMutationObserver||(e=function(){function e(){"undefined"!=typeof console&&null!==console&&console.warn("MutationObserver is not supported by your browser."),"undefined"!=typeof console&&null!==console&&console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content.")}return e.notSupported=!0,e.prototype.observe=function(){},e}()),r=this.getComputedStyle||function(e,t){return this.getPropertyValue=function(t){var i;return"float"===t&&(t="styleFloat"),n.test(t)&&t.replace(n,function(e,t){return t.toUpperCase()}),(null!=(i=e.currentStyle)?i[t]:void 0)||null},this},n=/(\-([a-z]){1})/g,this.WOW=function(){function n(e){null==e&&(e={}),this.scrollCallback=s(this.scrollCallback,this),this.scrollHandler=s(this.scrollHandler,this),this.resetAnimation=s(this.resetAnimation,this),this.start=s(this.start,this),this.scrolled=!0,this.config=this.util().extend(e,this.defaults),null!=e.scrollContainer&&(this.config.scrollContainer=document.querySelector(e.scrollContainer)),this.animationNameCache=new i,this.wowEvent=this.util().createEvent(this.config.boxClass)}return n.prototype.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null},n.prototype.init=function(){var e;return this.element=window.document.documentElement,"interactive"===(e=document.readyState)||"complete"===e?this.start():this.util().addEvent(document,"DOMContentLoaded",this.start),this.finished=[]},n.prototype.start=function(){var t,i,r,n,s;if(this.stopped=!1,this.boxes=(function(){var e,i,r,n;for(e=0,r=this.element.querySelectorAll("."+this.config.boxClass),n=[],i=r.length;e<i;e++)t=r[e],n.push(t);return n}).call(this),this.all=(function(){var e,i,r,n;for(e=0,r=this.boxes,n=[],i=r.length;e<i;e++)t=r[e],n.push(t);return n}).call(this),this.boxes.length)if(this.disabled())this.resetStyle();else for(i=0,r=(n=this.boxes).length;i<r;i++)t=n[i],this.applyStyle(t,!0);if(this.disabled()||(this.util().addEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().addEvent(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live)return new e((s=this,function(e){var t,i,r,n,a;for(t=0,a=[],i=e.length;t<i;t++)n=e[t],a.push((function(){var e,t,i,s;for(e=0,i=n.addedNodes||[],s=[],t=i.length;e<t;e++)r=i[e],s.push(this.doSync(r));return s}).call(s));return a})).observe(document.body,{childList:!0,subtree:!0})},n.prototype.stop=function(){if(this.stopped=!0,this.util().removeEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().removeEvent(window,"resize",this.scrollHandler),null!=this.interval)return clearInterval(this.interval)},n.prototype.sync=function(t){if(e.notSupported)return this.doSync(this.element)},n.prototype.doSync=function(e){var t,i,r,n,s;if(null==e&&(e=this.element),1===e.nodeType){for(i=0,n=(e=e.parentNode||e).querySelectorAll("."+this.config.boxClass),s=[],r=n.length;i<r;i++)t=n[i],0>a.call(this.all,t)?(this.boxes.push(t),this.all.push(t),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(t,!0),s.push(this.scrolled=!0)):s.push(void 0);return s}},n.prototype.show=function(e){return this.applyStyle(e),e.className=e.className+" "+this.config.animateClass,null!=this.config.callback&&this.config.callback(e),this.util().emitEvent(e,this.wowEvent),this.util().addEvent(e,"animationend",this.resetAnimation),this.util().addEvent(e,"oanimationend",this.resetAnimation),this.util().addEvent(e,"webkitAnimationEnd",this.resetAnimation),this.util().addEvent(e,"MSAnimationEnd",this.resetAnimation),e},n.prototype.applyStyle=function(e,t){var i,r,n,s;return r=e.getAttribute("data-wow-duration"),i=e.getAttribute("data-wow-delay"),n=e.getAttribute("data-wow-iteration"),this.animate((s=this,function(){return s.customStyle(e,t,r,i,n)}))},n.prototype.animate="requestAnimationFrame"in window?function(e){return window.requestAnimationFrame(e)}:function(e){return e()},n.prototype.resetStyle=function(){var e,t,i,r,n;for(t=0,r=this.boxes,n=[],i=r.length;t<i;t++)e=r[t],n.push(e.style.visibility="visible");return n},n.prototype.resetAnimation=function(e){var t;if(e.type.toLowerCase().indexOf("animationend")>=0)return(t=e.target||e.srcElement).className=t.className.replace(this.config.animateClass,"").trim()},n.prototype.customStyle=function(e,t,i,r,n){return t&&this.cacheAnimationName(e),e.style.visibility=t?"hidden":"visible",i&&this.vendorSet(e.style,{animationDuration:i}),r&&this.vendorSet(e.style,{animationDelay:r}),n&&this.vendorSet(e.style,{animationIterationCount:n}),this.vendorSet(e.style,{animationName:t?"none":this.cachedAnimationName(e)}),e},n.prototype.vendors=["moz","webkit"],n.prototype.vendorSet=function(e,t){var i,r,n,s;for(i in r=[],t)n=t[i],e[""+i]=n,r.push((function(){var t,r,a,l;for(t=0,a=this.vendors,l=[],r=a.length;t<r;t++)s=a[t],l.push(e[""+s+i.charAt(0).toUpperCase()+i.substr(1)]=n);return l}).call(this));return r},n.prototype.vendorCSS=function(e,t){var i,n,s,a,l,o;for(i=0,a=(l=r(e)).getPropertyCSSValue(t),n=(s=this.vendors).length;i<n;i++)o=s[i],a=a||l.getPropertyCSSValue("-"+o+"-"+t);return a},n.prototype.animationName=function(e){var t;try{t=this.vendorCSS(e,"animation-name").cssText}catch(i){t=r(e).getPropertyValue("animation-name")}return"none"===t?"":t},n.prototype.cacheAnimationName=function(e){return this.animationNameCache.set(e,this.animationName(e))},n.prototype.cachedAnimationName=function(e){return this.animationNameCache.get(e)},n.prototype.scrollHandler=function(){return this.scrolled=!0},n.prototype.scrollCallback=function(){var e;if(this.scrolled&&(this.scrolled=!1,this.boxes=(function(){var t,i,r,n;for(t=0,r=this.boxes,n=[],i=r.length;t<i;t++)if(e=r[t]){if(this.isVisible(e)){this.show(e);continue}n.push(e)}return n}).call(this),!(this.boxes.length||this.config.live)))return this.stop()},n.prototype.offsetTop=function(e){for(var t;void 0===e.offsetTop;)e=e.parentNode;for(t=e.offsetTop;e=e.offsetParent;)t+=e.offsetTop;return t},n.prototype.isVisible=function(e){var t,i,r,n,s;return i=e.getAttribute("data-wow-offset")||this.config.offset,n=(s=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset)+Math.min(this.element.clientHeight,this.util().innerHeight())-i,t=(r=this.offsetTop(e))+e.clientHeight,r<=n&&t>=s},n.prototype.util=function(){return null!=this._util?this._util:this._util=new t},n.prototype.disabled=function(){return!this.config.mobile&&this.util().isMobile(navigator.userAgent)},n}()}).call(this)},1804:(e,t,i)=>{"use strict";i.d(t,{A9:()=>_,AH:()=>c,Bv:()=>m,C:()=>M,CC:()=>g,EC:()=>f,HR:()=>y,I1:()=>p,Kv:()=>n,Ky:()=>s,Nc:()=>S,OT:()=>o,P9:()=>h,Qo:()=>d,RK:()=>P,Td:()=>F,Ue:()=>x,Uj:()=>D,V:()=>V,b6:()=>a,e7:()=>j,j7:()=>I,jR:()=>R,jn:()=>$,kJ:()=>l,mY:()=>r,or:()=>z,pc:()=>w,pq:()=>u,qq:()=>T,s:()=>L,sI:()=>C,t$:()=>O,tC:()=>v,tD:()=>A,tu:()=>k,uF:()=>N,vg:()=>b,zn:()=>E});let r="carousel",n="controller",s="navigation",a="no-scroll",l="portal",o="root",u="toolbar",c="zoom",d="loading",p="error",h="complete",m="placeholder",f=e=>`active-slide-${e}`;f(d),f("playing"),f(p),f(h);let g="fullsize",v="flex_center",y="no_scroll",b="no_scroll_padding",w="slide_wrapper",x="slide_wrapper_interactive",E="prev",S="next",C="swipe",M="close",T="onPointerDown",P="onPointerMove",k="onPointerUp",A="onPointerLeave",L="onPointerCancel",N="onKeyDown",O="onKeyUp",I="onWheel",z="Escape",_="ArrowLeft",$="ArrowRight",D="button",R="icon",F="contain",j="cover",V="Unknown action type"},2252:()=>{},2379:(e,t,i)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function n(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach(i=>{void 0===e[i]?e[i]=t[i]:r(t[i])&&r(e[i])&&Object.keys(t[i]).length>0&&n(e[i],t[i])})}i.d(t,{a:()=>o,g:()=>a});let s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return n(e,s),e}let l={document:s,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){let e="undefined"!=typeof window?window:{};return n(e,l),e}},2482:(e,t,i)=>{"use strict";i.d(t,{a:()=>v,c:()=>p,d:()=>a,e:()=>d,f:()=>y,g:()=>g,h:()=>l,l:()=>f,m:()=>m,n:()=>s,o:()=>h,p:()=>c,q:()=>function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let r=1;r<arguments.length;r+=1){let n=r<0||arguments.length<=r?void 0:arguments[r];if(null!=n&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(n instanceof HTMLElement):!n||1!==n.nodeType&&11!==n.nodeType)){let r=Object.keys(Object(n)).filter(e=>0>i.indexOf(e));for(let i=0,s=r.length;i<s;i+=1){let s=r[i],a=Object.getOwnPropertyDescriptor(n,s);void 0!==a&&a.enumerable&&(o(t[s])&&o(n[s])?n[s].__swiper__?t[s]=n[s]:e(t[s],n[s]):!o(t[s])&&o(n[s])?(t[s]={},n[s].__swiper__?t[s]=n[s]:e(t[s],n[s])):t[s]=n[s])}}}return t},r:()=>n,s:()=>u});var r=i(2379);function n(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function s(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function a(){return Date.now()}function l(e,t){let i,n,s;void 0===t&&(t="x");let a=(0,r.a)(),l=function(e){let t,i=(0,r.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((n=l.transform||l.webkitTransform).split(",").length>6&&(n=n.split(", ").map(e=>e.replace(",",".")).join(", ")),s=new a.WebKitCSSMatrix("none"===n?"":n)):i=(s=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(n=a.WebKitCSSMatrix?s.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(n=a.WebKitCSSMatrix?s.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),n||0}function o(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function u(e,t,i){e.style.setProperty(t,i)}function c(e){let t,{swiper:i,targetPosition:n,side:s}=e,a=(0,r.a)(),l=-i.translate,o=null,u=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let c=n>l?"next":"prev",d=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,p=()=>{t=new Date().getTime(),null===o&&(o=t);let e=l+(.5-Math.cos(Math.max(Math.min((t-o)/u,1),0)*Math.PI)/2)*(n-l);if(d(e,n)&&(e=n),i.wrapperEl.scrollTo({[s]:e}),d(e,n)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[s]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(p)};p()}function d(e,t){return void 0===t&&(t=""),[...e.children].filter(e=>e.matches(t))}function p(e,t){void 0===t&&(t=[]);let i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:[t]),i}function h(e,t){let i=[];for(;e.previousElementSibling;){let r=e.previousElementSibling;t?r.matches(t)&&i.push(r):i.push(r),e=r}return i}function m(e,t){let i=[];for(;e.nextElementSibling;){let r=e.nextElementSibling;t?r.matches(t)&&i.push(r):i.push(r),e=r}return i}function f(e,t){return(0,r.a)().getComputedStyle(e,null).getPropertyValue(t)}function g(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function v(e,t){let i=[],r=e.parentElement;for(;r;)t?r.matches(t)&&i.push(r):i.push(r),r=r.parentElement;return i}function y(e,t,i){let n=(0,r.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}},4178:(e,t,i)=>{"use strict";i.d(t,{A:()=>E});var r=i(2115),n=i(6209),s=i(1804);let a={maxZoomPixelRatio:1,zoomInMultiplier:2,doubleTapDelay:300,doubleClickDelay:500,doubleClickMaxStops:2,keyboardMoveDistance:50,wheelZoomDistanceFactor:100,pinchZoomDistanceFactor:100,scrollToZoom:!1},l=e=>({...a,...e});function o(){let{zoom:e}=(0,n.D$)();return l(e)}function u(e,t){return((e.clientX-t.clientX)**2+(e.clientY-t.clientY)**2)**.5}function c(e,t,i=100,r=2){return e*Math.min(1+Math.abs(t/i),r)**Math.sign(t)}let d=r.createContext(null),p=(0,n.Tz)("useZoom","ZoomControllerContext",d);function h({children:e}){let[t,i]=r.useState(),{slideRect:a}=(0,n.as)(),{imageRect:l,maxZoom:p}=function(e,t){var i,r;let s={width:0,height:0},a={width:0,height:0},{currentSlide:l}=(0,n.mp)(),{imageFit:u}=(0,n.D$)().carousel,{maxZoomPixelRatio:c}=o();if(e&&l){let o={...l,...t};if((0,n.tx)(o)){let t=(0,n.QJ)(o,u),l=Math.max(...((null==(i=o.srcSet)?void 0:i.map(e=>e.width))||[]).concat(o.width?[o.width]:[])),d=Math.max(...((null==(r=o.srcSet)?void 0:r.map(e=>e.height))||[]).concat(o.height?[o.height]:[]));l>0&&d>0&&e.width>0&&e.height>0&&(a={width:(a=t?{width:Math.round(Math.min(l,e.width/e.height*d)),height:Math.round(Math.min(d,e.height/e.width*l))}:{width:l,height:d}).width*c,height:a.height*c},s=t?{width:Math.min(e.width,a.width,l),height:Math.min(e.height,a.height,d)}:{width:Math.round(Math.min(e.width,e.height/d*l,l)),height:Math.round(Math.min(e.height,e.width/l*d,d))})}}let d=s.width?Math.max((0,n.LI)(a.width/s.width,5),1):1;return{imageRect:s,maxZoom:d}}(a,null==t?void 0:t.imageDimensions),{zoom:h,offsetX:m,offsetY:f,disabled:g,changeZoom:v,changeOffsets:y,zoomIn:b,zoomOut:w}=function(e,t,i){let[s,a]=r.useState(1),[l,u]=r.useState(0),[c,d]=r.useState(0),p=function(e,t,i,s){let a=r.useRef(),l=r.useRef(),{zoom:o}=(0,n.D$)().animation,u=(0,n.iJ)(),c=(0,n.DK)(()=>{var r,n,c;if(null==(r=a.current)||r.cancel(),a.current=void 0,l.current&&(null==s?void 0:s.current)){try{a.current=null==(c=(n=s.current).animate)?void 0:c.call(n,[{transform:l.current},{transform:`scale(${e}) translateX(${t}px) translateY(${i}px)`}],{duration:u?0:null!=o?o:500,easing:a.current?"ease-out":"ease-in-out"})}catch(e){console.error(e)}l.current=void 0,a.current&&(a.current.onfinish=()=>{a.current=void 0})}});return(0,n.Nf)(c,[e,t,i,c]),r.useCallback(()=>{l.current=(null==s?void 0:s.current)?window.getComputedStyle(s.current).transform:void 0},[s])}(s,l,c,i),{currentSlide:h,globalIndex:m}=(0,n.mp)(),{containerRect:f,slideRect:g}=(0,n.as)(),{zoomInMultiplier:v}=o(),y=h&&(0,n.tx)(h)?h.src:void 0,b=!y||!(null==i?void 0:i.current);(0,n.Nf)(()=>{a(1),u(0),d(0)},[m,y]);let w=r.useCallback((t,i,r)=>{let n=r||s,a=l-(t||0),o=c-(i||0),p=(e.width*n-g.width)/2/n,h=(e.height*n-g.height)/2/n;u(Math.min(Math.abs(a),Math.max(p,0))*Math.sign(a)),d(Math.min(Math.abs(o),Math.max(h,0))*Math.sign(o))},[s,l,c,g,e.width,e.height]),x=r.useCallback((e,i,r,l)=>{let o=(0,n.LI)(Math.min(Math.max(e+.001<t?e:t,1),t),5);o!==s&&(i||p(),w(r?r*(1/s-1/o):0,l?l*(1/s-1/o):0,o),a(o))},[s,t,w,p]),E=(0,n.DK)(()=>{s>1&&(s>t&&x(t,!0),w())});(0,n.Nf)(E,[f.width,f.height,E]);let S=r.useCallback(()=>x(s*v),[s,v,x]),C=r.useCallback(()=>x(s/v),[s,v,x]);return{zoom:s,offsetX:l,offsetY:c,disabled:b,changeOffsets:w,changeZoom:x,zoomIn:S,zoomOut:C}}(l,p,null==t?void 0:t.zoomWrapperRef),{on:x}=(0,n.D$)(),E=(0,n.DK)(()=>{var e;g||null==(e=x.zoom)||e.call(x,{zoom:h})});r.useEffect(E,[h,E]),function(e,t,i,a,l,d){let p=r.useRef([]),h=r.useRef(0),m=r.useRef(),{globalIndex:f}=(0,n.mp)(),{getOwnerWindow:g}=(0,n.uG)(),{containerRef:v,subscribeSensors:y}=(0,n.as)(),{keyboardMoveDistance:b,zoomInMultiplier:w,wheelZoomDistanceFactor:x,scrollToZoom:E,doubleTapDelay:S,doubleClickDelay:C,doubleClickMaxStops:M,pinchZoomDistanceFactor:T}=o(),P=r.useCallback(e=>{if(v.current){let{pageX:t,pageY:i}=e,{scrollX:r,scrollY:n}=g(),{left:s,top:a,width:l,height:o}=v.current.getBoundingClientRect();return[t-s-r-l/2,i-a-n-o/2]}return[]},[v,g]),k=(0,n.DK)(t=>{let i=()=>{t.preventDefault(),t.stopPropagation()};if(e>1){let e=(e,t)=>{i(),l(e,t)};"ArrowDown"===t.key?e(0,b):"ArrowUp"===t.key?e(0,-b):"ArrowLeft"===t.key?e(-b,0):"ArrowRight"===t.key&&e(b,0)}let r=e=>{i(),a(e)},n=()=>t.getModifierState("Meta");"+"===t.key||"="===t.key&&n()?r(e*w):"-"===t.key||"_"===t.key&&n()?r(e/w):"0"===t.key&&n()&&r(1)}),A=(0,n.DK)(t=>{if((t.ctrlKey||E)&&Math.abs(t.deltaY)>Math.abs(t.deltaX)){t.stopPropagation(),a(c(e,-t.deltaY,x),!0,...P(t));return}e>1&&(t.stopPropagation(),E||l(t.deltaX,t.deltaY))}),L=r.useCallback(e=>{let t=p.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),N=r.useCallback(e=>{L(e),e.persist(),p.current.push(e)},[L]),O=(0,n.DK)(i=>{var r;let n=p.current;if("mouse"===i.pointerType&&i.buttons>1||!(null==(r=null==d?void 0:d.current)?void 0:r.contains(i.target)))return;e>1&&i.stopPropagation();let{timeStamp:s}=i;0===n.length&&s-h.current<("touch"===i.pointerType?S:C)?(h.current=0,a(e!==t?e*Math.max(t**(1/M),w):1,!1,...P(i))):h.current=s,N(i),2===n.length&&(m.current=u(n[0],n[1]))}),I=(0,n.DK)(t=>{let i=p.current,r=i.find(e=>e.pointerId===t.pointerId);if(2===i.length&&m.current){t.stopPropagation(),N(t);let r=u(i[0],i[1]),n=r-m.current;Math.abs(n)>0&&(a(c(e,n,T),!0,...i.map(e=>P(e)).reduce((e,t)=>t.map((t,i)=>e[i]+t/2))),m.current=r);return}e>1&&(t.stopPropagation(),r&&(1===i.length&&l((r.clientX-t.clientX)/e,(r.clientY-t.clientY)/e),N(t)))}),z=r.useCallback(e=>{let t=p.current;2===t.length&&t.find(t=>t.pointerId===e.pointerId)&&(m.current=void 0),L(e)},[L]),_=r.useCallback(()=>{let e=p.current;e.splice(0,e.length),h.current=0,m.current=void 0},[]);(0,n.yQ)(y,O,I,z,i),r.useEffect(_,[f,_]),r.useEffect(()=>i?()=>{}:(0,n.tP)(_,y(s.uF,k),y(s.j7,A)),[i,y,_,k,A])}(h,p,g,v,y,null==t?void 0:t.zoomWrapperRef);let S=r.useMemo(()=>({zoom:h,maxZoom:p,offsetX:m,offsetY:f,disabled:g,zoomIn:b,zoomOut:w,changeZoom:v}),[h,p,m,f,g,b,w,v]);r.useImperativeHandle(o().ref,()=>S,[S]);let C=r.useMemo(()=>({...S,setZoomWrapper:i}),[S,i]);return r.createElement(d.Provider,{value:C},e)}let m=(0,n.wt)("ZoomIn",r.createElement(r.Fragment,null,r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),r.createElement("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"}))),f=(0,n.wt)("ZoomOut",r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})),g=r.forwardRef(function({zoomIn:e,onLoseFocus:t},i){let s=r.useRef(!1),a=r.useRef(!1),{zoom:l,maxZoom:o,zoomIn:u,zoomOut:c,disabled:d}=p(),{render:h}=(0,n.D$)(),g=d||(e?l>=o:l<=1);return r.useEffect(()=>{g&&s.current&&a.current&&t(),g||(s.current=!0)},[g,t]),r.createElement(n.K0,{ref:i,disabled:g,label:e?"Zoom in":"Zoom out",icon:e?m:f,renderIcon:e?h.iconZoomIn:h.iconZoomOut,onClick:e?u:c,onFocus:()=>{a.current=!0},onBlur:()=>{a.current=!1}})});function v(){let e=r.useRef(null),t=r.useRef(null),{focus:i}=(0,n.as)(),s=r.useCallback(e=>{var t,r;(null==(t=e.current)?void 0:t.disabled)?i():null==(r=e.current)||r.focus()},[i]),a=r.useCallback(()=>s(e),[s]),l=r.useCallback(()=>s(t),[s]);return r.createElement(r.Fragment,null,r.createElement(g,{zoomIn:!0,ref:e,onLoseFocus:l}),r.createElement(g,{ref:t,onLoseFocus:a}))}function y(){let{render:e}=(0,n.D$)(),t=p();return e.buttonZoom?r.createElement(r.Fragment,null,e.buttonZoom(t)):r.createElement(v,null)}function b({current:e,preload:t},{type:i,source:r}){switch(i){case"fetch":if(!e)return{current:r};return{current:e,preload:r};case"done":if(r===t)return{current:r};return{current:e,preload:t};default:throw Error(s.V)}}function w(e){var t,i;let[{current:s,preload:a},l]=r.useReducer(b,{}),{slide:o,rect:u,imageFit:c,render:d,interactive:p}=e,h=o.srcSet.sort((e,t)=>e.width-t.width),m=null!=(t=o.width)?t:h[h.length-1].width,f=null!=(i=o.height)?i:h[h.length-1].height,g=(0,n.QJ)(o,c),v=Math.max(...h.map(e=>e.width)),y=Math.min((g?Math.max:Math.min)(u.width,m*(u.height/f)),v),w=(0,n.Y5)(),x=(0,n.DK)(()=>{var e;let t=null!=(e=h.find(e=>e.width>=y*w))?e:h[h.length-1];(!s||h.findIndex(e=>e.src===s)<h.findIndex(e=>e===t))&&l({type:"fetch",source:t.src})});(0,n.Nf)(x,[u.width,u.height,w,x]);let E=(0,n.DK)(e=>l({type:"done",source:e})),S={WebkitTransform:p?"initial":"translateZ(0)"};return g||Object.assign(S,u.width/u.height<m/f?{width:"100%",height:"auto"}:{width:"auto",height:"100%"}),r.createElement(r.Fragment,null,a&&a!==s&&r.createElement(n.Z$,{key:"preload",...e,slide:{...o,src:a,srcSet:void 0},style:{position:"absolute",visibility:"hidden",...S},onLoad:()=>E(a),render:{...d,iconLoading:()=>null,iconError:()=>null}}),s&&r.createElement(n.Z$,{key:"current",...e,slide:{...o,src:s,srcSet:void 0},style:S}))}function x({render:e,slide:t,offset:i,rect:a}){var l,o;let[u,c]=r.useState(),d=r.useRef(null),{zoom:h,maxZoom:m,offsetX:f,offsetY:g,setZoomWrapper:v}=p(),y=h>1,{carousel:b,on:x}=(0,n.D$)(),{currentIndex:E}=(0,n.mp)();(0,n.Nf)(()=>0===i?(v({zoomWrapperRef:d,imageDimensions:u}),()=>v(void 0)):()=>{},[i,u,v]);let S=null==(l=e.slide)?void 0:l.call(e,{slide:t,offset:i,rect:a,zoom:h,maxZoom:m});if(!S&&(0,n.tx)(t)){let s={slide:t,offset:i,rect:a,render:e,imageFit:b.imageFit,imageProps:b.imageProps,onClick:0===i?()=>{var e;return null==(e=x.click)?void 0:e.call(x,{index:E})}:void 0};S=((null==(o=t.srcSet)?void 0:o.length)||0)>0?r.createElement(w,{...s,slide:t,interactive:y,rect:0===i?{width:a.width*h,height:a.height*h}:a}):r.createElement(n.Z$,{onLoad:e=>c({width:e.naturalWidth,height:e.naturalHeight}),...s})}return S?r.createElement("div",{ref:d,className:(0,n.$z)((0,n.oF)(s.CC),(0,n.oF)(s.tC),(0,n.oF)(s.pc),y&&(0,n.oF)(s.Ue)),style:0===i?{transform:`scale(${h}) translateX(${f}px) translateY(${g}px)`}:void 0},S):null}let E=({augment:e,addModule:t})=>{e(({zoom:e,toolbar:t,render:i,controller:a,...o})=>{let u=l(e);return{zoom:u,toolbar:(0,n.bo)(t,s.AH,r.createElement(y,null)),render:{...i,slide:e=>{var t;return(0,n.tx)(e.slide)?r.createElement(x,{render:i,...e}):null==(t=i.slide)?void 0:t.call(i,e)}},controller:{...a,preventDefaultWheelY:u.scrollToZoom},...o}}),t((0,n.Fi)(s.AH,h))}},5695:(e,t,i)=>{"use strict";var r=i(8999);i.o(r,"usePathname")&&i.d(t,{usePathname:function(){return r.usePathname}}),i.o(r,"useRouter")&&i.d(t,{useRouter:function(){return r.useRouter}})},6209:(e,t,i)=>{"use strict";i.d(t,{$z:()=>u,Ay:()=>eD,D$:()=>R,DK:()=>el,Fi:()=>k,K0:()=>Z,LI:()=>v,Nf:()=>ei,QJ:()=>b,Tz:()=>f,Y5:()=>E,Z$:()=>em,as:()=>eb,bo:()=>M,iJ:()=>er,mp:()=>V,oF:()=>c,tP:()=>m,tx:()=>y,uG:()=>O,wt:()=>U,yQ:()=>eg});var r,n,s=i(2115),a=i(1804),l=i(7650);let o="yarl__";function u(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return[...t].filter(Boolean).join(" ")}function c(e){return"".concat(o).concat(e)}function d(e){return"--".concat(o).concat(e)}function p(e,t){return"".concat(e).concat(t?"_".concat(t):"")}function h(e){return t=>p(e,t)}function m(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return()=>{t.forEach(e=>{e()})}}function f(e,t,i){return()=>{let r=s.useContext(i);if(!r)throw Error("".concat(e," must be used within a ").concat(t,".Provider"));return r}}function g(){return"undefined"!=typeof window}function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=10**t;return Math.round((e+Number.EPSILON)*i)/i}function y(e){return void 0===e.type||"image"===e.type}function b(e,t){return e.imageFit===a.e7||e.imageFit!==a.Td&&t===a.e7}function w(e){return"string"==typeof e?Number.parseInt(e,10):e}function x(e){if("number"==typeof e)return{pixel:e};if("string"==typeof e){let t=w(e);return e.endsWith("%")?{percent:t}:{pixel:t}}return{pixel:0}}function E(){return(g()?null==window?void 0:window.devicePixelRatio:void 0)||1}function S(e,t){var i;return e[(i=e.length)>0?(t%i+i)%i:0]}function C(e,t){return e.length>0?S(e,t):void 0}function M(e,t,i){if(!i)return e;let{buttons:r,...n}=e,a=r.findIndex(e=>e===t),l=s.isValidElement(i)?s.cloneElement(i,{key:t},null):i;if(a>=0){let e=[...r];return e.splice(a,1,l),{buttons:e,...n}}return{buttons:[l,...r],...n}}let T=Number(s.version.split(".")[0])>=19,P={open:!1,close:()=>{},index:0,slides:[],render:{},plugins:[],toolbar:{buttons:[a.C]},labels:{},animation:{fade:250,swipe:500,easing:{fade:"ease",swipe:"ease-out",navigation:"ease-in-out"}},carousel:{finite:!1,preload:2,padding:"16px",spacing:"30%",imageFit:a.Td,imageProps:{}},controller:{ref:null,focus:!0,aria:!1,touchAction:"none",closeOnPullUp:!1,closeOnPullDown:!1,closeOnBackdropClick:!1,preventDefaultWheelX:!0,preventDefaultWheelY:!1},portal:{},noScroll:{disabled:!1},on:{},styles:{},className:""};function k(e,t){return{name:e,component:t}}function A(e,t){return{module:e,children:t}}function L(e,t,i){return e.flatMap(e=>{var r;return null!=(r=function e(t,i,r){return t.module.name===i?r(t):t.children?[A(t.module,t.children.flatMap(t=>{var n;return null!=(n=e(t,i,r))?n:[]}))]:[t]}(e,t,i))?r:[]})}let N=s.createContext(null),O=f("useDocument","DocumentContext",N);function I(e){let{nodeRef:t,children:i}=e,r=s.useMemo(()=>{let e=e=>{var i;return(null==(i=e||t.current)?void 0:i.ownerDocument)||document};return{getOwnerDocument:e,getOwnerWindow:t=>{var i;return(null==(i=e(t))?void 0:i.defaultView)||window}}},[t]);return s.createElement(N.Provider,{value:r},i)}let z=s.createContext(null),_=f("useEvents","EventsContext",z);function $(e){let{children:t}=e,[i]=s.useState({});s.useEffect(()=>()=>{Object.keys(i).forEach(e=>delete i[e])},[i]);let r=s.useMemo(()=>{let e=(e,t)=>{var r;null==(r=i[e])||r.splice(0,i[e].length,...i[e].filter(e=>e!==t))};return{publish:function(){for(var e,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let[s,a]=r;null==(e=i[s])||e.forEach(e=>e(a))},subscribe:(t,r)=>(i[t]||(i[t]=[]),i[t].push(r),()=>e(t,r)),unsubscribe:e}},[i]);return s.createElement(z.Provider,{value:r},t)}let D=s.createContext(null),R=f("useLightboxProps","LightboxPropsContext",D);function F(e){let{children:t,...i}=e;return s.createElement(D.Provider,{value:i},t)}let j=s.createContext(null),V=f("useLightboxState","LightboxStateContext",j),H=s.createContext(null),q=f("useLightboxDispatch","LightboxDispatchContext",H);function B(e,t){switch(t.type){case"swipe":{var i;let{slides:r}=e,n=(null==t?void 0:t.increment)||0,s=e.globalIndex+n,a=(i=r.length)>0?(s%i+i)%i:0,l=C(r,a);return{slides:r,currentIndex:a,globalIndex:s,currentSlide:l,animation:n||t.duration?{increment:n,duration:t.duration,easing:t.easing}:void 0}}case"update":if(t.slides!==e.slides||t.index!==e.currentIndex)return{slides:t.slides,currentIndex:t.index,globalIndex:t.index,currentSlide:C(t.slides,t.index)};return e;default:throw Error(a.V)}}function G(e){let{slides:t,index:i,children:r}=e,[n,a]=s.useReducer(B,{slides:t,currentIndex:i,globalIndex:i,currentSlide:C(t,i)});s.useEffect(()=>{a({type:"update",slides:t,index:i})},[t,i]);let l=s.useMemo(()=>({...n,state:n,dispatch:a}),[n,a]);return s.createElement(H.Provider,{value:a},s.createElement(j.Provider,{value:l},r))}let W=s.createContext(null),X=f("useTimeouts","TimeoutsContext",W);function Y(e){let{children:t}=e,[i]=s.useState([]);s.useEffect(()=>()=>{i.forEach(e=>window.clearTimeout(e)),i.splice(0,i.length)},[i]);let r=s.useMemo(()=>{let e=e=>{i.splice(0,i.length,...i.filter(t=>t!==e))};return{setTimeout:(t,r)=>{let n=window.setTimeout(()=>{e(n),t()},r);return i.push(n),n},clearTimeout:t=>{void 0!==t&&(e(t),window.clearTimeout(t))}}},[i]);return s.createElement(W.Provider,{value:r},t)}let Z=s.forwardRef(function(e,t){var i;let{label:r,className:n,icon:l,renderIcon:o,onClick:d,style:p,...h}=e,{styles:m,labels:f}=R(),g=null!=(i=null==f?void 0:f[r])?i:r;return s.createElement("button",{ref:t,type:"button",title:g,"aria-label":g,className:u(c(a.Uj),n),onClick:d,style:{...p,...m.button},...h},o?o():s.createElement(l,{className:c(a.jR),style:m.icon}))});function U(e,t){var i=s.createElement("g",{fill:"currentColor"},s.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t);let r=e=>s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",...e},i);return r.displayName=e,r}let K=U("Close",s.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Q=U("Previous",s.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})),J=U("Next",s.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),ee=U("Loading",s.createElement(s.Fragment,null,Array.from({length:8}).map((e,t,i)=>s.createElement("line",{key:t,x1:"12",y1:"6.5",x2:"12",y2:"1.8",strokeLinecap:"round",strokeWidth:"2.6",stroke:"currentColor",strokeOpacity:1/i.length*(t+1),transform:"rotate(".concat(360/i.length*t,", 12, 12)")})))),et=U("Error",s.createElement("path",{d:"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"})),ei=g()?s.useLayoutEffect:s.useEffect;function er(){let[e,t]=s.useState(!1);return s.useEffect(()=>{var e,i;let r=null==(e=window.matchMedia)?void 0:e.call(window,"(prefers-reduced-motion: reduce)");t(null==r?void 0:r.matches);let n=e=>t(e.matches);return null==(i=null==r?void 0:r.addEventListener)||i.call(r,"change",n),()=>{var e;return null==(e=null==r?void 0:r.removeEventListener)?void 0:e.call(r,"change",n)}},[]),e}function en(e,t){let i=s.useRef(),r=s.useRef(),n=er();return ei(()=>{var s,a,l;if(e.current&&void 0!==i.current&&!n){let{keyframes:n,duration:o,easing:u,onfinish:c}=t(i.current,e.current.getBoundingClientRect(),function(e){let t=0,i=0,r=0,n=window.getComputedStyle(e).transform.match(/matrix.*\((.+)\)/);if(n){let e=n[1].split(",").map(w);6===e.length?(t=e[4],i=e[5]):16===e.length&&(t=e[12],i=e[13],r=e[14])}return{x:t,y:i,z:r}}(e.current))||{};if(n&&o){null==(s=r.current)||s.cancel(),r.current=void 0;try{r.current=null==(l=(a=e.current).animate)?void 0:l.call(a,n,{duration:o,easing:u})}catch(e){console.error(e)}r.current&&(r.current.onfinish=()=>{r.current=void 0,null==c||c()})}}i.current=void 0}),{prepareAnimation:e=>{i.current=e},isAnimationPlaying:()=>{var e;return(null==(e=r.current)?void 0:e.playState)==="running"}}}function es(){let e=s.useRef(null),t=s.useRef(),[i,r]=s.useState();return{setContainerRef:s.useCallback(i=>{e.current=i,t.current&&(t.current.disconnect(),t.current=void 0);let n=()=>{if(i){let e=window.getComputedStyle(i),t=e=>parseFloat(e)||0;r({width:Math.round(i.clientWidth-t(e.paddingLeft)-t(e.paddingRight)),height:Math.round(i.clientHeight-t(e.paddingTop)-t(e.paddingBottom))})}else r(void 0)};n(),i&&"undefined"!=typeof ResizeObserver&&(t.current=new ResizeObserver(n),t.current.observe(i))},[]),containerRef:e,containerRect:i}}function ea(){let e=s.useRef(),{setTimeout:t,clearTimeout:i}=X();return s.useCallback((r,n)=>{i(e.current),e.current=t(r,n>0?n:0)},[t,i])}function el(e){let t=s.useRef(e);return ei(()=>{t.current=e}),s.useCallback(function(){for(var e,i=arguments.length,r=Array(i),n=0;n<i;n++)r[n]=arguments[n];return null==(e=t.current)?void 0:e.call(t,...r)},[])}function eo(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function eu(e,t){return s.useMemo(()=>null==e&&null==t?null:i=>{eo(e,i),eo(t,i)},[e,t])}function ec(){let[e,t]=s.useState(!1);return ei(()=>{t("rtl"===window.getComputedStyle(window.document.documentElement).direction)},[]),e}function ed(e,t){let i=s.useRef(0),r=ea(),n=el(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];i.current=Date.now(),e(r)});return s.useCallback(function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];r(()=>{n(s)},t-(Date.now()-i.current))},[t,n,r])}let ep=h("slide"),eh=h("slide_image");function em(e){var t,i,r,n,l,o,d;let{slide:p,offset:h,render:m,rect:f,imageFit:v,imageProps:y,onClick:w,onLoad:x,onError:E,style:S}=e,[C,M]=s.useState(a.Qo),{publish:T}=_(),{setTimeout:P}=X(),k=s.useRef(null);s.useEffect(()=>{0===h&&T((0,a.EC)(C))},[h,C,T]);let A=el(e=>{("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{e.parentNode&&(M(a.P9),P(()=>{null==x||x(e)},0))})}),L=s.useCallback(e=>{k.current=e,(null==e?void 0:e.complete)&&A(e)},[A]),N=s.useCallback(e=>{A(e.currentTarget)},[A]),O=el(()=>{M(a.I1),null==E||E()}),I=b(p,v),z=(e,t)=>Number.isFinite(e)?e:t,$=z(Math.max(...(null!=(i=null==(t=p.srcSet)?void 0:t.map(e=>e.width))?i:[]).concat(p.width?[p.width]:[]).filter(Boolean)),(null==(r=k.current)?void 0:r.naturalWidth)||0),D=z(Math.max(...(null!=(l=null==(n=p.srcSet)?void 0:n.map(e=>e.height))?l:[]).concat(p.height?[p.height]:[]).filter(Boolean)),(null==(o=k.current)?void 0:o.naturalHeight)||0),R=null==(d=p.srcSet)?void 0:d.sort((e,t)=>e.width-t.width).map(e=>"".concat(e.src," ").concat(e.width,"w")).join(", "),F=R&&f&&g()?"".concat(Math.round(Math.min(f&&!I&&p.width&&p.height?f.height/p.height*p.width:Number.MAX_VALUE,f.width)),"px"):void 0,{style:j,className:V,...H}=y||{};return s.createElement(s.Fragment,null,s.createElement("img",{ref:L,onLoad:N,onError:O,onClick:w,draggable:!1,className:u(c(eh()),I&&c(eh("cover")),C!==a.P9&&c(eh("loading")),V),style:{...$&&D?{maxWidth:"min(".concat($,"px, 100%)"),maxHeight:"min(".concat(D,"px, 100%)")}:{maxWidth:"100%",maxHeight:"100%"},...S,...j},...H,alt:p.alt,sizes:F,srcSet:R,src:p.src}),C!==a.P9&&s.createElement("div",{className:c(ep(a.Bv))},C===a.Qo&&((null==m?void 0:m.iconLoading)?m.iconLoading():s.createElement(ee,{className:u(c(a.jR),c(ep(a.Qo)))})),C===a.I1&&((null==m?void 0:m.iconError)?m.iconError():s.createElement(et,{className:u(c(a.jR),c(ep(a.I1)))}))))}let ef=s.forwardRef(function(e,t){let{className:i,children:r,...n}=e,a=s.useRef(null);return s.createElement(I,{nodeRef:a},s.createElement("div",{ref:eu(t,a),className:u(c("root"),i),...n},r))});function eg(e,t,i,r,n){s.useEffect(()=>n?()=>{}:m(e(a.qq,t),e(a.RK,i),e(a.tu,r),e(a.tD,r),e(a.s,r)),[e,t,i,r,n])}!function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL",e[e.ANIMATION=3]="ANIMATION"}(r||(r={})),function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL"}(n||(n={}));let ev=h("container"),ey=s.createContext(null),eb=f("useController","ControllerContext",ey),ew=k(a.Kv,function(e){var t;let{children:i,...l}=e,{carousel:o,animation:p,controller:h,on:f,styles:g,render:y}=l,{closeOnPullUp:b,closeOnPullDown:w,preventDefaultWheelX:E,preventDefaultWheelY:S}=h,[C,M]=s.useState(),T=V(),P=q(),[k,A]=s.useState(r.NONE),L=s.useRef(0),N=s.useRef(0),I=s.useRef(1),{registerSensors:z,subscribeSensors:$}=function(){let[e]=s.useState({}),t=s.useCallback((t,i)=>{var r;null==(r=e[t])||r.forEach(e=>{i.isPropagationStopped()||e(i)})},[e]);return{registerSensors:s.useMemo(()=>({onPointerDown:e=>t(a.qq,e),onPointerMove:e=>t(a.RK,e),onPointerUp:e=>t(a.tu,e),onPointerLeave:e=>t(a.tD,e),onPointerCancel:e=>t(a.s,e),onKeyDown:e=>t(a.uF,e),onKeyUp:e=>t(a.t$,e),onWheel:e=>t(a.j7,e)}),[t]),subscribeSensors:s.useCallback((t,i)=>(e[t]||(e[t]=[]),e[t].unshift(i),()=>{let r=e[t];r&&r.splice(0,r.length,...r.filter(e=>e!==i))}),[e])}}(),{subscribe:D,publish:R}=_(),F=ea(),j=ea(),H=ea(),{containerRef:B,setContainerRef:G,containerRect:W}=es(),Y=eu(function(e){let{preventDefaultWheelX:t,preventDefaultWheelY:i}=e,r=s.useRef(null),n=el(e=>{let r=Math.abs(e.deltaX)>Math.abs(e.deltaY);(r&&t||!r&&i||e.ctrlKey)&&e.preventDefault()});return s.useCallback(e=>{var t;e?e.addEventListener("wheel",n,{passive:!1}):null==(t=r.current)||t.removeEventListener("wheel",n),r.current=e},[n])}({preventDefaultWheelX:E,preventDefaultWheelY:S}),G),Z=s.useRef(null),U=eu(Z,void 0),{getOwnerDocument:K}=O(),Q=ec(),J=e=>(Q?-1:1)*("number"==typeof e?e:1),ee=el(()=>{var e;return null==(e=B.current)?void 0:e.focus()}),et=el(()=>l),ei=el(()=>T),er=s.useCallback(e=>R(a.zn,e),[R]),eo=s.useCallback(e=>R(a.Nc,e),[R]),ed=s.useCallback(()=>R(a.C),[R]),ep=e=>!(o.finite&&(J(e)>0&&0===T.currentIndex||0>J(e)&&T.currentIndex===T.slides.length-1)),eh=e=>{var t;L.current=e,null==(t=B.current)||t.style.setProperty(d("swipe_offset"),"".concat(Math.round(e),"px"))},em=e=>{var t,i;N.current=e,I.current=Math.min(Math.max(v(1-(w&&e>0?e:b&&e<0?-e:0)/60*.5,2),.5),1),null==(t=B.current)||t.style.setProperty(d("pull_offset"),"".concat(Math.round(e),"px")),null==(i=B.current)||i.style.setProperty(d("pull_opacity"),"".concat(I.current))},{prepareAnimation:ef}=en(Z,(e,t,i)=>{if(Z.current&&W)return{keyframes:[{transform:"translate(0, ".concat(e.rect.y-t.y+i.y,"px)"),opacity:e.opacity},{transform:"translate(0, 0)",opacity:1}],duration:e.duration,easing:p.easing.fade}}),eb=(e,t)=>{if(b||w){em(e);let i=0;Z.current&&(i=p.fade*(t?2:1),ef({rect:Z.current.getBoundingClientRect(),opacity:I.current,duration:i})),H(()=>{em(0),A(r.NONE)},i),A(r.ANIMATION),t||ed()}},{prepareAnimation:ew,isAnimationPlaying:ex}=en(Z,(e,t,i)=>{var r;if(Z.current&&W&&(null==(r=T.animation)?void 0:r.duration)){let r=x(o.spacing),n=(r.percent?r.percent*W.width/100:r.pixel)||0;return{keyframes:[{transform:"translate(".concat(J(T.globalIndex-e.index)*(W.width+n)+e.rect.x-t.x+i.x,"px, 0)")},{transform:"translate(0, 0)"}],duration:T.animation.duration,easing:T.animation.easing}}}),eE=el(e=>{var t,i;let n=e.offset||0,s=n?p.swipe:null!=(t=p.navigation)?t:p.swipe,l=n||ex()?p.easing.swipe:p.easing.navigation,{direction:o}=e,u=null!=(i=e.count)?i:1,c=r.ANIMATION,d=s*u;if(!o){let t=null==W?void 0:W.width,i=e.duration||0,r=t?s/t*Math.abs(n):s;0!==u?(i<r?d=d/r*Math.max(i,r/5):t&&(d=s/t*(t-Math.abs(n))),o=J(n)>0?a.zn:a.Nc):d=s/2}let h=0;o===a.zn?ep(J(1))?h=-u:(c=r.NONE,d=s):o===a.Nc&&(ep(J(-1))?h=u:(c=r.NONE,d=s)),j(()=>{eh(0),A(r.NONE)},d=Math.round(d)),Z.current&&ew({rect:Z.current.getBoundingClientRect(),index:T.globalIndex}),A(c),R(a.sI,{type:"swipe",increment:h,duration:d,easing:l})});s.useEffect(()=>{var e,t;(null==(e=T.animation)?void 0:e.increment)&&(null==(t=T.animation)?void 0:t.duration)&&F(()=>P({type:"swipe",increment:0}),T.animation.duration)},[T.animation,P,F]);let eS=[$,ep,(null==W?void 0:W.width)||0,p.swipe,()=>A(r.SWIPE),e=>eh(e),(e,t)=>eE({offset:e,duration:t,count:1}),e=>eE({offset:e,count:0})],eC=[()=>{w&&A(r.PULL)},e=>em(e),e=>eb(e),e=>eb(e,!0)];!function(e,t,i,r,a,l,o,u,c,d,p,h,m,f){let g=s.useRef(0),v=s.useRef([]),y=s.useRef(),b=s.useRef(0),w=s.useRef(n.NONE),x=s.useCallback(e=>{y.current===e.pointerId&&(y.current=void 0,w.current=n.NONE);let t=v.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),E=s.useCallback(e=>{x(e),e.persist(),v.current.push(e)},[x]),S=el(e=>{E(e)}),C=(e,t)=>d&&e>t||c&&e<-t,M=el(e=>{if(v.current.find(t=>t.pointerId===e.pointerId)&&y.current===e.pointerId){let e=Date.now()-b.current,t=g.current;w.current===n.SWIPE?Math.abs(t)>.3*i||Math.abs(t)>5&&e<r?o(t,e):u(t):w.current===n.PULL&&(C(t,60)?m(t,e):f(t)),g.current=0,w.current=n.NONE}x(e)});eg(e,S,el(e=>{let i=v.current.find(t=>t.pointerId===e.pointerId);if(i){let r=y.current===e.pointerId;if(0===e.buttons)return void(r&&0!==g.current?M(e):x(i));let s=e.clientX-i.clientX,o=e.clientY-i.clientY;if(void 0===y.current){let i=t=>{E(e),y.current=e.pointerId,b.current=Date.now(),w.current=t};Math.abs(s)>Math.abs(o)&&Math.abs(s)>30&&t(s)?(i(n.SWIPE),a()):Math.abs(o)>Math.abs(s)&&C(o,30)&&(i(n.PULL),p())}else r&&(w.current===n.SWIPE?(g.current=s,l(s)):w.current===n.PULL&&(g.current=o,h(o)))}}),M)}(...eS,b,w,...eC),function(e,t,i,n,l,o,u,c,d){let p=s.useRef(0),h=s.useRef(0),m=s.useRef(),f=s.useRef(),g=s.useRef(0),v=s.useRef(),y=s.useRef(0),{setTimeout:b,clearTimeout:w}=X(),x=s.useCallback(()=>{m.current&&(w(m.current),m.current=void 0)},[w]),E=s.useCallback(()=>{f.current&&(w(f.current),f.current=void 0)},[w]),S=el(()=>{e!==r.SWIPE&&(p.current=0,y.current=0,x(),E())});s.useEffect(S,[e,S]);let C=el(e=>{f.current=void 0,p.current===e&&d(p.current)}),M=el(t=>{if(t.ctrlKey||Math.abs(t.deltaY)>Math.abs(t.deltaX))return;let s=e=>{g.current=e,w(v.current),v.current=e>0?b(()=>{g.current=0,v.current=void 0},300):void 0};if(e===r.NONE){if(Math.abs(t.deltaX)<=1.2*Math.abs(g.current))return void s(t.deltaX);if(!i(-t.deltaX))return;if(h.current+=t.deltaX,x(),Math.abs(h.current)>30)h.current=0,s(0),y.current=Date.now(),o();else{let e=h.current;m.current=b(()=>{m.current=void 0,e===h.current&&(h.current=0)},l)}}else if(e===r.SWIPE){let e=p.current-t.deltaX;if(p.current=e=Math.min(Math.abs(e),n)*Math.sign(e),u(e),E(),Math.abs(e)>.2*n){s(t.deltaX),c(e,Date.now()-y.current);return}f.current=b(()=>C(e),2*l)}else s(t.deltaX)});s.useEffect(()=>t(a.j7,M),[t,M])}(k,...eS);let eM=el(()=>{h.focus&&K().querySelector(".".concat(c(a.kJ)," .").concat(c(ev())))&&ee()});s.useEffect(eM,[eM]);let eT=el(()=>{var e;null==(e=f.view)||e.call(f,{index:T.currentIndex})});s.useEffect(eT,[T.globalIndex,eT]),s.useEffect(()=>m(D(a.zn,e=>eE({direction:a.zn,...e})),D(a.Nc,e=>eE({direction:a.Nc,...e})),D(a.sI,e=>P(e))),[D,eE,P]);let eP=s.useMemo(()=>({prev:er,next:eo,close:ed,focus:ee,slideRect:W?function(e,t){let i=x(t),r=void 0!==i.percent?e.width/100*i.percent:i.pixel;return{width:Math.max(e.width-2*r,0),height:Math.max(e.height-2*r,0)}}(W,o.padding):{width:0,height:0},containerRect:W||{width:0,height:0},subscribeSensors:$,containerRef:B,setCarouselRef:U,toolbarWidth:C,setToolbarWidth:M}),[er,eo,ed,ee,$,W,B,U,C,M,o.padding]);return s.useImperativeHandle(h.ref,()=>({prev:er,next:eo,close:ed,focus:ee,getLightboxProps:et,getLightboxState:ei}),[er,eo,ed,ee,et,ei]),s.createElement("div",{ref:Y,className:u(c(ev()),c(a.tC)),style:{...k===r.SWIPE?{[d("swipe_offset")]:"".concat(Math.round(L.current),"px")}:null,...k===r.PULL?{[d("pull_offset")]:"".concat(Math.round(N.current),"px"),[d("pull_opacity")]:"".concat(I.current)}:null,..."none"!==h.touchAction?{[d("controller_touch_action")]:h.touchAction}:null,...g.container},...h.aria?{role:"presentation","aria-live":"polite"}:null,tabIndex:-1,...z},W&&s.createElement(ey.Provider,{value:eP},i,null==(t=y.controls)?void 0:t.call(y)))});function ex(e){return p(a.mY,e)}function eE(e){return p("slide",e)}function eS(e){var t,i,r,n;let l,{slide:o,offset:d}=e,p=s.useRef(null),{currentIndex:h}=V(),{slideRect:m,close:f,focus:g}=eb(),{render:v,carousel:{imageFit:b,imageProps:w},on:{click:x},controller:{closeOnBackdropClick:E},styles:{slide:S}}=R(),{getOwnerDocument:C}=O(),M=0!==d;return s.useEffect(()=>{var e;M&&(null==(e=p.current)?void 0:e.contains(C().activeElement))&&g()},[M,g,C]),s.createElement("div",{ref:p,className:u(c(eE()),!M&&c(eE("current")),c(a.tC)),...{inert:T?M:M?"":void 0},onClick:e=>{let t=p.current,i=e.target instanceof HTMLElement?e.target:void 0;E&&i&&t&&(i===t||Array.from(t.children).find(e=>e===i)&&i.classList.contains(c(a.pc)))&&f()},style:S},(!(l=null==(t=v.slide)?void 0:t.call(v,{slide:o,offset:d,rect:m}))&&y(o)&&(l=s.createElement(em,{slide:o,offset:d,render:v,rect:m,imageFit:b,imageProps:w,onClick:M?void 0:()=>null==x?void 0:x({index:h})})),l?s.createElement(s.Fragment,null,null==(i=v.slideHeader)?void 0:i.call(v,{slide:o}),(null!=(r=v.slideContainer)?r:e=>{let{children:t}=e;return t})({slide:o,children:l}),null==(n=v.slideFooter)?void 0:n.call(v,{slide:o})):null))}function eC(){let e=R().styles.slide;return s.createElement("div",{className:c("slide"),style:e})}let eM=k(a.mY,function(e){let{carousel:t}=e,{slides:i,currentIndex:r,globalIndex:n}=V(),{setCarouselRef:a}=eb(),l=x(t.spacing),o=x(t.padding),p=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Math.min(e.preload,Math.max(e.finite?t.length-1:Math.floor(t.length/2),i))}(t,i,1),h=[];if(i.length>0)for(let e=r-p;e<=r+p;e+=1){let s=S(i,e),a=n-r+e,l=t.finite&&(e<0||e>i.length-1);h.push(l?{key:a}:{key:["".concat(a),y(s)?s.src:void 0].filter(Boolean).join("|"),offset:e-r,slide:s})}return s.createElement("div",{ref:a,className:u(c(ex()),h.length>0&&c(ex("with_slides"))),style:{["".concat(d(ex("slides_count")))]:h.length,["".concat(d(ex("spacing_px")))]:l.pixel||0,["".concat(d(ex("spacing_percent")))]:l.percent||0,["".concat(d(ex("padding_px")))]:o.pixel||0,["".concat(d(ex("padding_percent")))]:o.percent||0}},h.map(e=>{let{key:t,slide:i,offset:r}=e;return i?s.createElement(eS,{key:t,slide:i,offset:r}):s.createElement(eC,{key:t})}))});function eT(){let{carousel:e}=R(),{slides:t,currentIndex:i}=V();return{prevDisabled:0===t.length||e.finite&&0===i,nextDisabled:0===t.length||e.finite&&i===t.length-1}}function eP(e){let{label:t,icon:i,renderIcon:r,action:n,onClick:a,disabled:l,style:o}=e;return s.createElement(Z,{label:t,icon:i,renderIcon:r,className:c("navigation_".concat(n)),disabled:l,onClick:a,style:o,...function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=s.useRef();return ei(()=>{t&&i.current&&(i.current=!1,e())},[t,e]),{onFocus:s.useCallback(()=>{i.current=!0},[]),onBlur:s.useCallback(()=>{i.current=!1},[])}}(eb().focus,l)})}let ek=k(a.Ky,function(e){let{render:{buttonPrev:t,buttonNext:i,iconPrev:r,iconNext:n},styles:l}=e,{prev:o,next:u,subscribeSensors:c}=eb(),{prevDisabled:d,nextDisabled:p}=eT();return!function(e){var t;let i=ec(),{publish:r}=_(),{animation:n}=R(),{prevDisabled:l,nextDisabled:o}=eT(),u=(null!=(t=n.navigation)?t:n.swipe)/2,c=ed(()=>r(a.zn),u),d=ed(()=>r(a.Nc),u),p=el(e=>{switch(e.key){case a.or:r(a.C);break;case a.A9:(i?o:l)||(i?d:c)();break;case a.jn:(i?l:o)||(i?c:d)()}});s.useEffect(()=>e(a.uF,p),[e,p])}(c),s.createElement(s.Fragment,null,t?t():s.createElement(eP,{label:"Previous",action:a.zn,icon:Q,renderIcon:r,style:l.navigationPrev,disabled:d,onClick:o}),i?i():s.createElement(eP,{label:"Next",action:a.Nc,icon:J,renderIcon:n,style:l.navigationNext,disabled:p,onClick:u}))}),eA=c(a.HR),eL=c(a.vg);function eN(e,t,i){let r=window.getComputedStyle(e),n=i?"padding-left":"padding-right",s=i?r.paddingLeft:r.paddingRight,a=e.style.getPropertyValue(n);return e.style.setProperty(n,"".concat((w(s)||0)+t,"px")),()=>{a?e.style.setProperty(n,a):e.style.removeProperty(n)}}let eO=k(a.b6,function(e){let{noScroll:{disabled:t},children:i}=e,r=ec(),{getOwnerDocument:n,getOwnerWindow:a}=O();return s.useEffect(()=>{if(t)return()=>{};let e=[],i=a(),{body:s,documentElement:l}=n(),o=Math.round(i.innerWidth-l.clientWidth);if(o>0){e.push(eN(s,o,r));let t=s.getElementsByTagName("*");for(let n=0;n<t.length;n+=1){let s=t[n];"style"in s&&"fixed"===i.getComputedStyle(s).getPropertyValue("position")&&!s.classList.contains(eL)&&e.push(eN(s,o,r))}}return s.classList.add(eA),()=>{s.classList.remove(eA),e.forEach(e=>e())}},[r,t,n,a]),s.createElement(s.Fragment,null,i)});function eI(e,t,i){let r=e.getAttribute(t);return e.setAttribute(t,i),()=>{r?e.setAttribute(t,r):e.removeAttribute(t)}}let ez=k(a.kJ,function(e){let{children:t,animation:i,styles:r,className:n,on:o,portal:h,close:m}=e,[f,g]=s.useState(!1),[v,y]=s.useState(!1),b=s.useRef([]),w=s.useRef(null),{setTimeout:x}=X(),{subscribe:E}=_(),S=er()?0:i.fade;s.useEffect(()=>(g(!0),()=>{g(!1),y(!1)}),[]);let C=el(()=>{b.current.forEach(e=>e()),b.current=[]}),M=el(()=>{var e;y(!1),C(),null==(e=o.exiting)||e.call(o),x(()=>{var e;null==(e=o.exited)||e.call(o),m()},S)});s.useEffect(()=>E(a.C,M),[E,M]);let T=el(e=>{var t,i,r;e.scrollTop,y(!0),null==(t=o.entering)||t.call(o);let n=null!=(r=null==(i=e.parentNode)?void 0:i.children)?r:[];for(let t=0;t<n.length;t+=1){let i=n[t];-1===["TEMPLATE","SCRIPT","STYLE"].indexOf(i.tagName)&&i!==e&&(b.current.push(eI(i,"inert","")),b.current.push(eI(i,"aria-hidden","true")))}b.current.push(()=>{var e,t;null==(t=null==(e=w.current)?void 0:e.focus)||t.call(e)}),x(()=>{var e;null==(e=o.entered)||e.call(o)},S)}),k=s.useCallback(e=>{e?T(e):C()},[T,C]);return f?(0,l.createPortal)(s.createElement(ef,{ref:k,className:u(n,c(p(a.kJ,void 0)),c(a.vg),v&&c(p(a.kJ,"open"))),role:"presentation","aria-live":"polite",style:{...i.fade!==P.animation.fade?{[d("fade_animation_duration")]:"".concat(S,"ms")}:null,...i.easing.fade!==P.animation.easing.fade?{[d("fade_animation_timing_function")]:i.easing.fade}:null,...r.root},onFocus:e=>{w.current||(w.current=e.relatedTarget)}},t),h.root||document.body):null}),e_=k(a.OT,function(e){let{children:t}=e;return s.createElement(s.Fragment,null,t)}),e$=k(a.pq,function(e){let{toolbar:{buttons:t},render:{buttonClose:i,iconClose:r},styles:n}=e,{close:l,setToolbarWidth:o}=eb(),{setContainerRef:u,containerRect:d}=es();return ei(()=>{o(null==d?void 0:d.width)},[o,null==d?void 0:d.width]),s.createElement("div",{ref:u,style:n.toolbar,className:c(p(a.pq,void 0))},null==t?void 0:t.map(e=>e===a.C?i?i():s.createElement(Z,{key:a.C,label:"Close",icon:K,renderIcon:r,onClick:l}):e))});function eD(e){let{carousel:t,animation:i,render:r,toolbar:n,controller:l,noScroll:o,on:u,plugins:c,slides:d,index:p,...h}=e,{animation:m,carousel:f,render:g,toolbar:v,controller:y,noScroll:b,on:x,slides:E,index:S,plugins:C,...M}=P,{config:T,augmentation:k}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=e,n=e=>{let t=[...r];for(;t.length>0;){let i=t.pop();if((null==i?void 0:i.module.name)===e)return!0;(null==i?void 0:i.children)&&t.push(...i.children)}return!1},s=(e,t)=>{if(""===e){r=[A(t,r)];return}r=L(r,e,e=>[A(t,[e])])},l=(e,t)=>{r=L(r,e,e=>[A(e.module,[A(t,e.children)])])},o=(e,t,i)=>{r=L(r,e,e=>{var r;return[A(e.module,[...i?[A(t)]:[],...null!=(r=e.children)?r:[],...i?[]:[A(t)]])]})},u=(e,t,i)=>{r=L(r,e,e=>[...i?[A(t)]:[],e,...i?[]:[A(t)]])},c=e=>{l(a.Kv,e)},d=(e,t)=>{r=L(r,e,e=>[A(t,e.children)])},p=e=>{r=L(r,e,e=>e.children)},h=e=>{i.push(e)};return t.forEach(e=>{e({contains:n,addParent:s,append:l,addChild:o,addSibling:u,addModule:c,replace:d,remove:p,augment:h})}),{config:r,augmentation:e=>i.reduce((e,t)=>t(e),e)}}([A(ez,[A(eO,[A(ew,[A(eM),A(e$),A(ek)])])])],c||C),N=k({animation:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{easing:i,...r}=e,{easing:n,...s}=t;return{easing:{...i,...n},...r,...s}}(m,i),carousel:{...f,...t},render:{...g,...r},toolbar:{...v,...n},controller:{...y,...l},noScroll:{...b,...o},on:{...x,...u},...M,...h});return N.open?s.createElement(F,{...N},s.createElement(G,{slides:d||E,index:w(p||S)},s.createElement(Y,null,s.createElement($,null,function e(t,i){var r;return s.createElement(t.module.component,{key:t.module.name,...i},null==(r=t.children)?void 0:r.map(t=>e(t,i)))}(A(e_,T),N))))):null}},7269:(e,t,i)=>{"use strict";i.d(t,{Ij:()=>u,Vx:()=>a,dK:()=>o});var r=i(2379),n=i(2482);function s(e,t,i,r){return e.params.createElements&&Object.keys(r).forEach(s=>{if(!i[s]&&!0===i.auto){let a=(0,n.e)(e.el,`.${r[s]}`)[0];a||((a=(0,n.c)("div",r[s])).className=r[s],e.el.append(a)),i[s]=a,t[s]=a}}),i}function a(e){let{swiper:t,extendParams:i,on:r,emit:n}=e;i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null};let a=e=>(Array.isArray(e)?e:[e]).filter(e=>!!e);function l(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i.length>1&&1===t.el.querySelectorAll(e).length&&(i=t.el.querySelector(e))),e&&!i)?e:i}function o(e,i){let r=t.params.navigation;(e=a(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...r.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](r.lockClass))})}function u(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){o(i,!1),o(e,!1);return}o(i,t.isBeginning&&!t.params.rewind),o(e,t.isEnd&&!t.params.rewind)}function c(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),n("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),n("navigationNext"))}function p(){let e=t.params.navigation;if(t.params.navigation=s(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=l(e.nextEl),r=l(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:r}),i=a(i),r=a(r);let n=(i,r)=>{i&&i.addEventListener("click","next"===r?d:c),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>n(e,"next")),r.forEach(e=>n(e,"prev"))}function h(){let{nextEl:e,prevEl:i}=t.navigation;e=a(e),i=a(i);let r=(e,i)=>{e.removeEventListener("click","next"===i?d:c),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>r(e,"next")),i.forEach(e=>r(e,"prev"))}r("init",()=>{!1===t.params.navigation.enabled?m():(p(),u())}),r("toEdge fromEdge lock unlock",()=>{u()}),r("destroy",()=>{h()}),r("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=a(e),i=a(i),t.enabled)return void u();[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),r("click",(e,i)=>{let{nextEl:r,prevEl:s}=t.navigation;r=a(r),s=a(s);let l=i.target;if(t.params.navigation.hideOnClick&&!s.includes(l)&&!r.includes(l)){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===l||t.pagination.el.contains(l)))return;r.length?e=r[0].classList.contains(t.params.navigation.hiddenClass):s.length&&(e=s[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?n("navigationShow"):n("navigationHide"),[...r,...s].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let m=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),h()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),p(),u()},disable:m,update:u,init:p,destroy:h})}function l(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function o(e){let t,{swiper:i,extendParams:r,on:a,emit:o}=e,u="swiper-pagination";r({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${u}-bullet`,bulletActiveClass:`${u}-bullet-active`,modifierClass:`${u}-`,currentClass:`${u}-current`,totalClass:`${u}-total`,hiddenClass:`${u}-hidden`,progressbarFillClass:`${u}-progressbar-fill`,progressbarOppositeClass:`${u}-progressbar-opposite`,clickableClass:`${u}-clickable`,lockClass:`${u}-lock`,horizontalClass:`${u}-horizontal`,verticalClass:`${u}-vertical`,paginationDisabledClass:`${u}-disabled`}}),i.pagination={el:null,bullets:[]};let c=0,d=e=>(Array.isArray(e)?e:[e]).filter(e=>!!e);function p(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function h(e,t){let{bulletActiveClass:r}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${r}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${r}-${t}-${t}`))}function m(e){let t=e.target.closest(l(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let r=(0,n.g)(t)*i.params.slidesPerGroup;if(i.params.loop){if(i.realIndex===r)return;let e=i.realIndex,t=i.getSlideIndexByData(r),n=i.getSlideIndexByData(i.realIndex),s=r=>{let n=i.activeIndex;i.loopFix({direction:r,activeSlideIndex:t,slideTo:!1}),n===i.activeIndex&&i.slideToLoop(e,0,!1,!0)};t>i.slides.length-i.loopedSlides?s(t>n?"next":"prev"):i.params.centeredSlides&&t<Math.floor(("auto"===i.params.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(i.params.slidesPerView,10)))/2)&&s("prev"),i.slideToLoop(r)}else i.slideTo(r)}function f(){let e,r,s=i.rtl,a=i.params.pagination;if(p())return;let u=i.pagination.el;u=d(u);let m=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,f=i.params.loop?Math.ceil(m/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(r=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,r=i.previousSnapIndex):(r=i.previousIndex||0,e=i.activeIndex||0),"bullets"===a.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let l,o,d,p=i.pagination.bullets;if(a.dynamicBullets&&(t=(0,n.f)(p[0],i.isHorizontal()?"width":"height",!0),u.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(a.dynamicMainBullets+4)}px`}),a.dynamicMainBullets>1&&void 0!==r&&((c+=e-(r||0))>a.dynamicMainBullets-1?c=a.dynamicMainBullets-1:c<0&&(c=0)),d=((o=(l=Math.max(e-c,0))+(Math.min(p.length,a.dynamicMainBullets)-1))+l)/2),p.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${a.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),u.length>1)p.forEach(t=>{let r=(0,n.g)(t);r===e?t.classList.add(...a.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),a.dynamicBullets&&(r>=l&&r<=o&&t.classList.add(...`${a.bulletActiveClass}-main`.split(" ")),r===l&&h(t,"prev"),r===o&&h(t,"next"))});else{let t=p[e];if(t&&t.classList.add(...a.bulletActiveClass.split(" ")),i.isElement&&p.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),a.dynamicBullets){let e=p[l],t=p[o];for(let e=l;e<=o;e+=1)p[e]&&p[e].classList.add(...`${a.bulletActiveClass}-main`.split(" "));h(e,"prev"),h(t,"next")}}if(a.dynamicBullets){let e=Math.min(p.length,a.dynamicMainBullets+4),r=(t*e-t)/2-d*t,n=s?"right":"left";p.forEach(e=>{e.style[i.isHorizontal()?n:"top"]=`${r}px`})}}u.forEach((t,r)=>{if("fraction"===a.type&&(t.querySelectorAll(l(a.currentClass)).forEach(t=>{t.textContent=a.formatFractionCurrent(e+1)}),t.querySelectorAll(l(a.totalClass)).forEach(e=>{e.textContent=a.formatFractionTotal(f)})),"progressbar"===a.type){let r;r=a.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let n=(e+1)/f,s=1,o=1;"horizontal"===r?s=n:o=n,t.querySelectorAll(l(a.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${s}) scaleY(${o})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===a.type&&a.renderCustom?(t.innerHTML=a.renderCustom(i,e+1,f),0===r&&o("paginationRender",t)):(0===r&&o("paginationRender",t),o("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](a.lockClass)})}function g(){let e=i.params.pagination;if(p())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,r=i.pagination.el;r=d(r);let n="";if("bullets"===e.type){let r=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&r>t&&(r=t);for(let t=0;t<r;t+=1)e.renderBullet?n+=e.renderBullet.call(i,t,e.bulletClass):n+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(n=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(n=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],r.forEach(t=>{"custom"!==e.type&&(t.innerHTML=n||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(l(e.bulletClass)))}),"custom"!==e.type&&o("paginationRender",r[0])}function v(){let e;i.params.pagination=s(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.filter(e=>(0,n.a)(e,".swiper")[0]===i.el)[0]),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=d(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),c=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",m),i.enabled||e.classList.add(t.lockClass)})))}function y(){let e=i.params.pagination;if(p())return;let t=i.pagination.el;t&&(t=d(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",m))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}a("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=d(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),a("init",()=>{!1===i.params.pagination.enabled?b():(v(),g(),f())}),a("activeIndexChange",()=>{void 0===i.snapIndex&&f()}),a("snapIndexChange",()=>{f()}),a("snapGridLengthChange",()=>{g(),f()}),a("destroy",()=>{y()}),a("enable disable",()=>{let{el:e}=i.pagination;e&&(e=d(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),a("lock unlock",()=>{f()}),a("click",(e,t)=>{let r=t.target,n=d(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&n&&n.length>0&&!r.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&r===i.navigation.nextEl||i.navigation.prevEl&&r===i.navigation.prevEl))return;!0===n[0].classList.contains(i.params.pagination.hiddenClass)?o("paginationShow"):o("paginationHide"),n.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let b=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=d(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),y()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=d(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),v(),g(),f()},disable:b,render:g,update:f,init:v,destroy:y})}function u(e){let t,i,n,s,a,l,o,u,c,{swiper:d,extendParams:p,on:h,emit:m,params:f}=e;d.autoplay={running:!1,paused:!1,timeLeft:0},p({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let g=f&&f.autoplay?f.autoplay.delay:3e3,v=f&&f.autoplay?f.autoplay.delay:3e3,y=new Date().getTime;function b(e){d&&!d.destroyed&&d.wrapperEl&&e.target===d.wrapperEl&&(d.wrapperEl.removeEventListener("transitionend",b),M())}let w=()=>{if(d.destroyed||!d.autoplay.running)return;d.autoplay.paused?s=!0:s&&(v=n,s=!1);let e=d.autoplay.paused?n:y+v-new Date().getTime();d.autoplay.timeLeft=e,m("autoplayTimeLeft",e,e/g),i=requestAnimationFrame(()=>{w()})},x=e=>{if(d.destroyed||!d.autoplay.running)return;cancelAnimationFrame(i),w();let r=void 0===e?d.params.autoplay.delay:e;g=d.params.autoplay.delay,v=d.params.autoplay.delay;let s=(()=>{let e;if(e=d.virtual&&d.params.virtual.enabled?d.slides.filter(e=>e.classList.contains("swiper-slide-active"))[0]:d.slides[d.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(s)&&s>0&&void 0===e&&(r=s,g=s,v=s),n=r;let a=d.params.speed,l=()=>{d&&!d.destroyed&&(d.params.autoplay.reverseDirection?!d.isBeginning||d.params.loop||d.params.rewind?(d.slidePrev(a,!0,!0),m("autoplay")):d.params.autoplay.stopOnLastSlide||(d.slideTo(d.slides.length-1,a,!0,!0),m("autoplay")):!d.isEnd||d.params.loop||d.params.rewind?(d.slideNext(a,!0,!0),m("autoplay")):d.params.autoplay.stopOnLastSlide||(d.slideTo(0,a,!0,!0),m("autoplay")),d.params.cssMode&&(y=new Date().getTime(),requestAnimationFrame(()=>{x()})))};return r>0?(clearTimeout(t),t=setTimeout(()=>{l()},r)):requestAnimationFrame(()=>{l()}),r},E=()=>{d.autoplay.running=!0,x(),m("autoplayStart")},S=()=>{d.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),m("autoplayStop")},C=(e,i)=>{if(d.destroyed||!d.autoplay.running)return;clearTimeout(t),e||(c=!0);let r=()=>{m("autoplayPause"),d.params.autoplay.waitForTransition?d.wrapperEl.addEventListener("transitionend",b):M()};if(d.autoplay.paused=!0,i){u&&(n=d.params.autoplay.delay),u=!1,r();return}n=(n||d.params.autoplay.delay)-(new Date().getTime()-y),d.isEnd&&n<0&&!d.params.loop||(n<0&&(n=0),r())},M=()=>{d.isEnd&&n<0&&!d.params.loop||d.destroyed||!d.autoplay.running||(y=new Date().getTime(),c?(c=!1,x(n)):x(),d.autoplay.paused=!1,m("autoplayResume"))},T=()=>{if(d.destroyed||!d.autoplay.running)return;let e=(0,r.g)();"hidden"===e.visibilityState&&(c=!0,C(!0)),"visible"===e.visibilityState&&M()},P=e=>{"mouse"===e.pointerType&&(c=!0,d.animating||d.autoplay.paused||C(!0))},k=e=>{"mouse"===e.pointerType&&d.autoplay.paused&&M()};h("init",()=>{d.params.autoplay.enabled&&(d.params.autoplay.pauseOnMouseEnter&&(d.el.addEventListener("pointerenter",P),d.el.addEventListener("pointerleave",k)),(0,r.g)().addEventListener("visibilitychange",T),y=new Date().getTime(),E())}),h("destroy",()=>{d.el.removeEventListener("pointerenter",P),d.el.removeEventListener("pointerleave",k),(0,r.g)().removeEventListener("visibilitychange",T),d.autoplay.running&&S()}),h("beforeTransitionStart",(e,t,i)=>{!d.destroyed&&d.autoplay.running&&(i||!d.params.autoplay.disableOnInteraction?C(!0,!0):S())}),h("sliderFirstMove",()=>{if(!d.destroyed&&d.autoplay.running){if(d.params.autoplay.disableOnInteraction)return void S();a=!0,l=!1,c=!1,o=setTimeout(()=>{c=!0,l=!0,C(!0)},200)}}),h("touchEnd",()=>{if(!d.destroyed&&d.autoplay.running&&a){if(clearTimeout(o),clearTimeout(t),d.params.autoplay.disableOnInteraction){l=!1,a=!1;return}l&&d.params.cssMode&&M(),l=!1,a=!1}}),h("slideChange",()=>{!d.destroyed&&d.autoplay.running&&(u=!0)}),Object.assign(d.autoplay,{start:E,stop:S,pause:C,resume:M})}},7677:(e,t,i)=>{"use strict";let r,n,s;i.d(t,{RC:()=>V,qr:()=>H});var a=i(2115),l=i(2379),o=i(2482);function u(){return r||(r=function(){let e=(0,l.a)(),t=(0,l.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),r}let c=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},d=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},p=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let r="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),n=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[n-t];i.push(...Array.from({length:t}).map((e,t)=>n+r+t)),e.slides.forEach((t,r)=>{i.includes(t.column)&&d(e,r)});return}let s=n+r-1;if(e.params.rewind||e.params.loop)for(let r=n-t;r<=s+t;r+=1){let t=(r%i+i)%i;(t<n||t>s)&&d(e,t)}else for(let r=Math.max(n-t,0);r<=Math.min(s+t,i-1);r+=1)r!==n&&(r>s||r<n)&&d(e,r)};function h(e){let{swiper:t,runCallbacks:i,direction:r,step:n}=e,{activeIndex:s,previousIndex:a}=t,l=r;if(l||(l=s>a?"next":s<a?"prev":"reset"),t.emit(`transition${n}`),i&&s!==a){if("reset"===l)return void t.emit(`slideResetTransition${n}`);t.emit(`slideChangeTransition${n}`),"next"===l?t.emit(`slideNextTransition${n}`):t.emit(`slidePrevTransition${n}`)}}function m(e){let t=(0,l.g)(),i=(0,l.a)(),r=this.touchEventsData;r.evCache.push(e);let{params:n,touches:s,enabled:a}=this;if(!a||!n.simulateTouch&&"mouse"===e.pointerType||this.animating&&n.preventInteractionOnTransition)return;!this.animating&&n.cssMode&&n.loop&&this.loopFix();let u=e;u.originalEvent&&(u=u.originalEvent);let c=u.target;if("wrapper"===n.touchEventsTarget&&!this.wrapperEl.contains(c)||"which"in u&&3===u.which||"button"in u&&u.button>0||r.isTouched&&r.isMoved)return;let d=!!n.noSwipingClass&&""!==n.noSwipingClass,p=e.composedPath?e.composedPath():e.path;d&&u.target&&u.target.shadowRoot&&p&&(c=p[0]);let h=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,m=!!(u.target&&u.target.shadowRoot);if(n.noSwiping&&(m?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,l.g)()||i===(0,l.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let r=i.closest(e);return r||i.getRootNode?r||t(i.getRootNode().host):null}(t)}(h,c):c.closest(h))){this.allowClick=!0;return}if(n.swipeHandler&&!c.closest(n.swipeHandler))return;s.currentX=u.pageX,s.currentY=u.pageY;let f=s.currentX,g=s.currentY,v=n.edgeSwipeDetection||n.iOSEdgeSwipeDetection,y=n.edgeSwipeThreshold||n.iOSEdgeSwipeThreshold;if(v&&(f<=y||f>=i.innerWidth-y))if("prevent"!==v)return;else e.preventDefault();Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=f,s.startY=g,r.touchStartTime=(0,o.d)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,n.threshold>0&&(r.allowThresholdMove=!1);let b=!0;c.matches(r.focusableElements)&&(b=!1,"SELECT"===c.nodeName&&(r.isTouched=!1)),t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==c&&t.activeElement.blur();let w=b&&this.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||w)&&!c.isContentEditable&&u.preventDefault(),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.animating&&!n.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",u)}function f(e){let t,i=(0,l.g)(),r=this.touchEventsData,{params:n,touches:s,rtlTranslate:a,enabled:u}=this;if(!u||!n.simulateTouch&&"mouse"===e.pointerType)return;let c=e;if(c.originalEvent&&(c=c.originalEvent),!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",c);return}let d=r.evCache.findIndex(e=>e.pointerId===c.pointerId);d>=0&&(r.evCache[d]=c);let p=r.evCache.length>1?r.evCache[0]:c,h=p.pageX,m=p.pageY;if(c.preventedByNestedSwiper){s.startX=h,s.startY=m;return}if(!this.allowTouchMove){c.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(s,{startX:h,startY:m,prevX:this.touches.currentX,prevY:this.touches.currentY,currentX:h,currentY:m}),r.touchStartTime=(0,o.d)());return}if(n.touchReleaseOnEdges&&!n.loop){if(this.isVertical()){if(m<s.startY&&this.translate<=this.maxTranslate()||m>s.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(h<s.startX&&this.translate<=this.maxTranslate()||h>s.startX&&this.translate>=this.minTranslate())return}if(i.activeElement&&c.target===i.activeElement&&c.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}if(r.allowTouchCallbacks&&this.emit("touchMove",c),c.targetTouches&&c.targetTouches.length>1)return;s.currentX=h,s.currentY=m;let f=s.currentX-s.startX,g=s.currentY-s.startY;if(this.params.threshold&&Math.sqrt(f**2+g**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&s.currentY===s.startY||this.isVertical()&&s.currentX===s.startX?r.isScrolling=!1:f*f+g*g>=25&&(e=180*Math.atan2(Math.abs(g),Math.abs(f))/Math.PI,r.isScrolling=this.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",c),void 0===r.startMoving&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(r.startMoving=!0),r.isScrolling||this.zoom&&this.params.zoom&&this.params.zoom.enabled&&r.evCache.length>1){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!n.cssMode&&c.cancelable&&c.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&c.stopPropagation();let v=this.isHorizontal()?f:g,y=this.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;n.oneWayMovement&&(v=Math.abs(v)*(a?1:-1),y=Math.abs(y)*(a?1:-1)),s.diff=v,v*=n.touchRatio,a&&(v=-v,y=-y);let b=this.touchesDirection;this.swipeDirection=v>0?"prev":"next",this.touchesDirection=y>0?"prev":"next";let w=this.params.loop&&!n.cssMode,x="next"===this.swipeDirection&&this.allowSlideNext||"prev"===this.swipeDirection&&this.allowSlidePrev;if(!r.isMoved){if(w&&x&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,n.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",c)}r.isMoved&&b!==this.touchesDirection&&w&&x&&Math.abs(v)>=1&&(this.loopFix({direction:this.swipeDirection,setTranslate:!0}),t=!0),this.emit("sliderMove",c),r.isMoved=!0,r.currentTranslate=v+r.startTranslate;let E=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),v>0?(w&&x&&!t&&r.currentTranslate>(n.centeredSlides?this.minTranslate()-this.size/2:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(E=!1,n.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+v)**S))):v<0&&(w&&x&&!t&&r.currentTranslate<(n.centeredSlides?this.maxTranslate()+this.size/2:this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===n.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(E=!1,n.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-v)**S))),E&&(c.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),n.threshold>0)if(Math.abs(v)>n.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,r.currentTranslate=r.startTranslate,s.diff=this.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{r.currentTranslate=r.startTranslate;return}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&this.freeMode||n.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function g(e){let t,i=this,r=i.touchEventsData,n=r.evCache.findIndex(t=>t.pointerId===e.pointerId);if(n>=0&&r.evCache.splice(n,1),["pointercancel","pointerout","pointerleave","contextmenu"].includes(e.type)&&!(["pointercancel","contextmenu"].includes(e.type)&&(i.browser.isSafari||i.browser.isWebView)))return;let{params:s,touches:a,rtlTranslate:l,slidesGrid:u,enabled:c}=i;if(!c||!s.simulateTouch&&"mouse"===e.pointerType)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),r.allowTouchCallbacks&&i.emit("touchEnd",d),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&s.grabCursor&&i.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}s.grabCursor&&r.isMoved&&r.isTouched&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!1);let p=(0,o.d)(),h=p-r.touchStartTime;if(i.allowClick){let e=d.path||d.composedPath&&d.composedPath();i.updateClickedSlide(e&&e[0]||d.target,e),i.emit("tap click",d),h<300&&p-r.lastClickTime<300&&i.emit("doubleTap doubleClick",d)}if(r.lastClickTime=(0,o.d)(),(0,o.n)(()=>{i.destroyed||(i.allowClick=!0)}),!r.isTouched||!r.isMoved||!i.swipeDirection||0===a.diff||r.currentTranslate===r.startTranslate){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,t=s.followFinger?l?i.translate:-i.translate:-r.currentTranslate,s.cssMode)return;if(s.freeMode&&s.freeMode.enabled)return void i.freeMode.onTouchEnd({currentPos:t});let m=0,f=i.slidesSizesGrid[0];for(let e=0;e<u.length;e+=e<s.slidesPerGroupSkip?1:s.slidesPerGroup){let i=e<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;void 0!==u[e+i]?t>=u[e]&&t<u[e+i]&&(m=e,f=u[e+i]-u[e]):t>=u[e]&&(m=e,f=u[u.length-1]-u[u.length-2])}let g=null,v=null;s.rewind&&(i.isBeginning?v=s.virtual&&s.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1:i.isEnd&&(g=0));let y=(t-u[m])/f,b=m<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;if(h>s.longSwipesMs){if(!s.longSwipes)return void i.slideTo(i.activeIndex);"next"===i.swipeDirection&&(y>=s.longSwipesRatio?i.slideTo(s.rewind&&i.isEnd?g:m+b):i.slideTo(m)),"prev"===i.swipeDirection&&(y>1-s.longSwipesRatio?i.slideTo(m+b):null!==v&&y<0&&Math.abs(y)>s.longSwipesRatio?i.slideTo(v):i.slideTo(m))}else{if(!s.shortSwipes)return void i.slideTo(i.activeIndex);i.navigation&&(d.target===i.navigation.nextEl||d.target===i.navigation.prevEl)?d.target===i.navigation.nextEl?i.slideTo(m+b):i.slideTo(m):("next"===i.swipeDirection&&i.slideTo(null!==g?g:m+b),"prev"===i.swipeDirection&&i.slideTo(null!==v?v:m))}}function v(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:r,allowSlidePrev:n,snapGrid:s}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let l=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||l?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=n,e.allowSlideNext=r,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}function y(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function b(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let r=this.maxTranslate()-this.minTranslate();(0===r?0:(this.translate-this.minTranslate())/r)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function w(e){c(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}let x=!1;function E(){}let S=(e,t)=>{let i=(0,l.g)(),{params:r,el:n,wrapperEl:s,device:a}=e,o=!!r.nested,u="on"===t?"addEventListener":"removeEventListener";n[u]("pointerdown",e.onTouchStart,{passive:!1}),i[u]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[u]("pointerup",e.onTouchEnd,{passive:!0}),i[u]("pointercancel",e.onTouchEnd,{passive:!0}),i[u]("pointerout",e.onTouchEnd,{passive:!0}),i[u]("pointerleave",e.onTouchEnd,{passive:!0}),i[u]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&n[u]("click",e.onClick,!0),r.cssMode&&s[u]("scroll",e.onScroll),r.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",v,!0):e[t]("observerUpdate",v,!0),n[u]("load",e.onLoad,{capture:!0})},C=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var M={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let T={eventsEmitter:{on(e,t,i){let r=this;if(!r.eventsListeners||r.destroyed||"function"!=typeof t)return r;let n=i?"unshift":"push";return e.split(" ").forEach(e=>{r.eventsListeners[e]||(r.eventsListeners[e]=[]),r.eventsListeners[e][n](t)}),r},once(e,t,i){let r=this;if(!r.eventsListeners||r.destroyed||"function"!=typeof t)return r;function n(){r.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var i=arguments.length,s=Array(i),a=0;a<i;a++)s[a]=arguments[a];t.apply(r,s)}return n.__emitterProxy=t,r.on(e,n,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((r,n)=>{(r===t||r.__emitterProxy&&r.__emitterProxy===t)&&i.eventsListeners[e].splice(n,1)})}),i},emit(){let e,t,i,r=this;if(!r.eventsListeners||r.destroyed||!r.eventsListeners)return r;for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return"string"==typeof s[0]||Array.isArray(s[0])?(e=s[0],t=s.slice(1,s.length),i=r):(e=s[0].events,t=s[0].data,i=s[0].context||r),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{r.eventsAnyListeners&&r.eventsAnyListeners.length&&r.eventsAnyListeners.forEach(r=>{r.apply(i,[e,...t])}),r.eventsListeners&&r.eventsListeners[e]&&r.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),r}},update:{updateSize:function(){let e,t,i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,o.l)(i,"padding-left")||0,10)-parseInt((0,o.l)(i,"padding-right")||0,10),t=t-parseInt((0,o.l)(i,"padding-top")||0,10)-parseInt((0,o.l)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e,t=this;function i(e){return t.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}function r(e,t){return parseFloat(e.getPropertyValue(i(t))||0)}let n=t.params,{wrapperEl:s,slidesEl:a,size:l,rtlTranslate:u,wrongRTL:c}=t,d=t.virtual&&n.virtual.enabled,p=d?t.virtual.slides.length:t.slides.length,h=(0,o.e)(a,`.${t.params.slideClass}, swiper-slide`),m=d?t.virtual.slides.length:h.length,f=[],g=[],v=[],y=n.slidesOffsetBefore;"function"==typeof y&&(y=n.slidesOffsetBefore.call(t));let b=n.slidesOffsetAfter;"function"==typeof b&&(b=n.slidesOffsetAfter.call(t));let w=t.snapGrid.length,x=t.slidesGrid.length,E=n.spaceBetween,S=-y,C=0,M=0;if(void 0===l)return;"string"==typeof E&&E.indexOf("%")>=0?E=parseFloat(E.replace("%",""))/100*l:"string"==typeof E&&(E=parseFloat(E)),t.virtualSize=-E,h.forEach(e=>{u?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),n.centeredSlides&&n.cssMode&&((0,o.s)(s,"--swiper-centered-offset-before",""),(0,o.s)(s,"--swiper-centered-offset-after",""));let T=n.grid&&n.grid.rows>1&&t.grid;T&&t.grid.initSlides(m);let P="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter(e=>void 0!==n.breakpoints[e].slidesPerView).length>0;for(let s=0;s<m;s+=1){let a;if(e=0,h[s]&&(a=h[s]),T&&t.grid.updateSlide(s,a,m,i),!h[s]||"none"!==(0,o.l)(a,"display")){if("auto"===n.slidesPerView){P&&(h[s].style[i("width")]="");let l=getComputedStyle(a),u=a.style.transform,c=a.style.webkitTransform;if(u&&(a.style.transform="none"),c&&(a.style.webkitTransform="none"),n.roundLengths)e=t.isHorizontal()?(0,o.f)(a,"width",!0):(0,o.f)(a,"height",!0);else{let t=r(l,"width"),i=r(l,"padding-left"),n=r(l,"padding-right"),s=r(l,"margin-left"),o=r(l,"margin-right"),u=l.getPropertyValue("box-sizing");if(u&&"border-box"===u)e=t+s+o;else{let{clientWidth:r,offsetWidth:l}=a;e=t+i+n+s+o+(l-r)}}u&&(a.style.transform=u),c&&(a.style.webkitTransform=c),n.roundLengths&&(e=Math.floor(e))}else e=(l-(n.slidesPerView-1)*E)/n.slidesPerView,n.roundLengths&&(e=Math.floor(e)),h[s]&&(h[s].style[i("width")]=`${e}px`);h[s]&&(h[s].swiperSlideSize=e),v.push(e),n.centeredSlides?(S=S+e/2+C/2+E,0===C&&0!==s&&(S=S-l/2-E),0===s&&(S=S-l/2-E),.001>Math.abs(S)&&(S=0),n.roundLengths&&(S=Math.floor(S)),M%n.slidesPerGroup==0&&f.push(S),g.push(S)):(n.roundLengths&&(S=Math.floor(S)),(M-Math.min(t.params.slidesPerGroupSkip,M))%t.params.slidesPerGroup==0&&f.push(S),g.push(S),S=S+e+E),t.virtualSize+=e+E,C=e,M+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+b,u&&c&&("slide"===n.effect||"coverflow"===n.effect)&&(s.style.width=`${t.virtualSize+E}px`),n.setWrapperSize&&(s.style[i("width")]=`${t.virtualSize+E}px`),T&&t.grid.updateWrapperSize(e,f,i),!n.centeredSlides){let e=[];for(let i=0;i<f.length;i+=1){let r=f[i];n.roundLengths&&(r=Math.floor(r)),f[i]<=t.virtualSize-l&&e.push(r)}f=e,Math.floor(t.virtualSize-l)-Math.floor(f[f.length-1])>1&&f.push(t.virtualSize-l)}if(d&&n.loop){let e=v[0]+E;if(n.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/n.slidesPerGroup),r=e*n.slidesPerGroup;for(let e=0;e<i;e+=1)f.push(f[f.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===n.slidesPerGroup&&f.push(f[f.length-1]+e),g.push(g[g.length-1]+e),t.virtualSize+=e}if(0===f.length&&(f=[0]),0!==E){let e=t.isHorizontal()&&u?"marginLeft":i("marginRight");h.filter((e,t)=>!n.cssMode||!!n.loop||t!==h.length-1).forEach(t=>{t.style[e]=`${E}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let e=0;v.forEach(t=>{e+=t+(E||0)});let t=(e-=E)-l;f=f.map(e=>e<=0?-y:e>t?t+b:e)}if(n.centerInsufficientSlides){let e=0;if(v.forEach(t=>{e+=t+(E||0)}),(e-=E)<l){let t=(l-e)/2;f.forEach((e,i)=>{f[i]=e-t}),g.forEach((e,i)=>{g[i]=e+t})}}if(Object.assign(t,{slides:h,snapGrid:f,slidesGrid:g,slidesSizesGrid:v}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){(0,o.s)(s,"--swiper-centered-offset-before",`${-f[0]}px`),(0,o.s)(s,"--swiper-centered-offset-after",`${t.size/2-v[v.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(m!==p&&t.emit("slidesLengthChange"),f.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),g.length!==x&&t.emit("slidesGridLengthChange"),n.watchSlidesProgress&&t.updateSlidesOffset(),!d&&!n.cssMode&&("slide"===n.effect||"fade"===n.effect)){let e=`${n.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);m<=n.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t,i=this,r=[],n=i.virtual&&i.params.virtual.enabled,s=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let a=e=>n?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1)if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{r.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!n)break;r.push(a(e))}else r.push(a(i.activeIndex));for(t=0;t<r.length;t+=1)if(void 0!==r[t]){let e=r[t].offsetHeight;s=e>s?e:s}(s||0===s)&&(i.wrapperEl.style.height=`${s}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:r,snapGrid:n}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let s=-e;r&&(s=e),i.forEach(e=>{e.classList.remove(t.slideVisibleClass)}),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let l=i[e],o=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let u=(s+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+a),c=(s-n[0]+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+a),d=-(s-o),p=d+this.slidesSizesGrid[e];(d>=0&&d<this.size-1||p>1&&p<=this.size||d<=0&&p>=this.size)&&(this.visibleSlides.push(l),this.visibleSlidesIndexes.push(e),i[e].classList.add(t.slideVisibleClass)),l.progress=r?-u:u,l.originalProgress=r?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:r,isBeginning:n,isEnd:s,progressLoop:a}=this,l=n,o=s;if(0===i)r=0,n=!0,s=!0;else{r=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());n=t||r<=0,s=a||r>=1,t&&(r=0),a&&(r=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),r=this.slidesGrid[t],n=this.slidesGrid[i],s=this.slidesGrid[this.slidesGrid.length-1],l=Math.abs(e);(a=l>=r?(l-r)/s:(l+s-n)/s)>1&&(a-=1)}Object.assign(this,{progress:r,progressLoop:a,isBeginning:n,isEnd:s}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),n&&!l&&this.emit("reachBeginning toEdge"),s&&!o&&this.emit("reachEnd toEdge"),(l&&!n||o&&!s)&&this.emit("fromEdge"),this.emit("progress",r)},updateSlidesClasses:function(){let e,{slides:t,params:i,slidesEl:r,activeIndex:n}=this,s=this.virtual&&i.virtual.enabled,a=e=>(0,o.e)(r,`.${i.slideClass}${e}, swiper-slide${e}`)[0];if(t.forEach(e=>{e.classList.remove(i.slideActiveClass,i.slideNextClass,i.slidePrevClass)}),s)if(i.loop){let t=n-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=a(`[data-swiper-slide-index="${t}"]`)}else e=a(`[data-swiper-slide-index="${n}"]`);else e=t[n];if(e){e.classList.add(i.slideActiveClass);let r=(0,o.m)(e,`.${i.slideClass}, swiper-slide`)[0];i.loop&&!r&&(r=t[0]),r&&r.classList.add(i.slideNextClass);let n=(0,o.o)(e,`.${i.slideClass}, swiper-slide`)[0];i.loop,n&&n.classList.add(i.slidePrevClass)}this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i,r=this,n=r.rtlTranslate?r.translate:-r.translate,{snapGrid:s,params:a,activeIndex:l,realIndex:o,snapIndex:u}=r,c=e,d=e=>{let t=e-r.virtual.slidesBefore;return t<0&&(t=r.virtual.slides.length+t),t>=r.virtual.slides.length&&(t-=r.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t,{slidesGrid:i,params:r}=e,n=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?n>=i[e]&&n<i[e+1]-(i[e+1]-i[e])/2?t=e:n>=i[e]&&n<i[e+1]&&(t=e+1):n>=i[e]&&(t=e);return r.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(r)),s.indexOf(n)>=0)t=s.indexOf(n);else{let e=Math.min(a.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/a.slidesPerGroup)}if(t>=s.length&&(t=s.length-1),c===l){t!==u&&(r.snapIndex=t,r.emit("snapIndexChange")),r.params.loop&&r.virtual&&r.params.virtual.enabled&&(r.realIndex=d(c));return}i=r.virtual&&a.virtual.enabled&&a.loop?d(c):r.slides[c]?parseInt(r.slides[c].getAttribute("data-swiper-slide-index")||c,10):c,Object.assign(r,{previousSnapIndex:u,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:l,activeIndex:c}),r.initialized&&p(r),r.emit("activeIndexChange"),r.emit("snapIndexChange"),(r.initialized||r.params.runCallbacksOnInit)&&(o!==i&&r.emit("realIndexChange"),r.emit("slideChange"))},updateClickedSlide:function(e,t){let i,r=this.params,n=e.closest(`.${r.slideClass}, swiper-slide`);!n&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!n&&e.matches&&e.matches(`.${r.slideClass}, swiper-slide`)&&(n=e)});let s=!1;if(n){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===n){s=!0,i=e;break}}if(n&&s)this.clickedSlide=n,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}r.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:r,wrapperEl:n}=this;if(t.virtualTranslate)return i?-r:r;if(t.cssMode)return r;let s=(0,o.h)(n,e);return s+=this.cssOverflowAdjustment(),i&&(s=-s),s||0},setTranslate:function(e,t){let{rtlTranslate:i,params:r,wrapperEl:n,progress:s}=this,a=0,l=0;this.isHorizontal()?a=i?-e:e:l=e,r.roundLengths&&(a=Math.floor(a),l=Math.floor(l)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:l,r.cssMode?n[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-l:r.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():l-=this.cssOverflowAdjustment(),n.style.transform=`translate3d(${a}px, ${l}px, 0px)`);let o=this.maxTranslate()-this.minTranslate();(0===o?0:(e-this.minTranslate())/o)!==s&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,r,n){let s;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===r&&(r=!0);let a=this,{params:l,wrapperEl:u}=a;if(a.animating&&l.preventInteractionOnTransition)return!1;let c=a.minTranslate(),d=a.maxTranslate();if(s=r&&e>c?c:r&&e<d?d:e,a.updateProgress(s),l.cssMode){let e=a.isHorizontal();if(0===t)u[e?"scrollLeft":"scrollTop"]=-s;else{if(!a.support.smoothScroll)return(0,o.p)({swiper:a,targetPosition:-s,side:e?"left":"top"}),!0;u.scrollTo({[e?"left":"top"]:-s,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(s),i&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(s),i&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),h({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),h({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,r,n){let s;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,l=e;l<0&&(l=0);let{params:u,snapGrid:c,slidesGrid:d,previousIndex:p,activeIndex:h,rtlTranslate:m,wrapperEl:f,enabled:g}=a;if(a.animating&&u.preventInteractionOnTransition||!g&&!r&&!n)return!1;let v=Math.min(a.params.slidesPerGroupSkip,l),y=v+Math.floor((l-v)/a.params.slidesPerGroup);y>=c.length&&(y=c.length-1);let b=-c[y];if(u.normalizeSlideIndex)for(let e=0;e<d.length;e+=1){let t=-Math.floor(100*b),i=Math.floor(100*d[e]),r=Math.floor(100*d[e+1]);void 0!==d[e+1]?t>=i&&t<r-(r-i)/2?l=e:t>=i&&t<r&&(l=e+1):t>=i&&(l=e)}if(a.initialized&&l!==h&&(!a.allowSlideNext&&(m?b>a.translate&&b>a.minTranslate():b<a.translate&&b<a.minTranslate())||!a.allowSlidePrev&&b>a.translate&&b>a.maxTranslate()&&(h||0)!==l))return!1;if(l!==(p||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(b),s=l>h?"next":l<h?"prev":"reset",m&&-b===a.translate||!m&&b===a.translate)return a.updateActiveIndex(l),u.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==u.effect&&a.setTranslate(b),"reset"!==s&&(a.transitionStart(i,s),a.transitionEnd(i,s)),!1;if(u.cssMode){let e=a.isHorizontal(),i=m?b:-b;if(0===t){let t=a.virtual&&a.params.virtual.enabled;t&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),t&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{f[e?"scrollLeft":"scrollTop"]=i})):f[e?"scrollLeft":"scrollTop"]=i,t&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1})}else{if(!a.support.smoothScroll)return(0,o.p)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}return a.setTransition(t),a.setTranslate(b),a.updateActiveIndex(l),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,r),a.transitionStart(i,s),0===t?a.transitionEnd(i,s):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,s))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let n=e;return this.params.loop&&(this.virtual&&this.params.virtual.enabled?n+=this.virtual.slidesBefore:n=this.getSlideIndexByData(n)),this.slideTo(n,t,i,r)},slideNext:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);let r=this,{enabled:n,params:s,animating:a}=r;if(!n)return r;let l=s.slidesPerGroup;"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(l=Math.max(r.slidesPerViewDynamic("current",!0),1));let o=r.activeIndex<s.slidesPerGroupSkip?1:l,u=r.virtual&&s.virtual.enabled;if(s.loop){if(a&&!u&&s.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+o,e,t,i)}),!0}return s.rewind&&r.isEnd?r.slideTo(0,e,t,i):r.slideTo(r.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);let r=this,{params:n,snapGrid:s,slidesGrid:a,rtlTranslate:l,enabled:o,animating:u}=r;if(!o)return r;let c=r.virtual&&n.virtual.enabled;if(n.loop){if(u&&!c&&n.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}function d(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let p=d(l?r.translate:-r.translate),h=s.map(e=>d(e)),m=s[h.indexOf(p)-1];if(void 0===m&&n.cssMode){let e;s.forEach((t,i)=>{p>=t&&(e=i)}),void 0!==e&&(m=s[e>0?e-1:e])}let f=0;if(void 0!==m&&((f=a.indexOf(m))<0&&(f=r.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(f=Math.max(f=f-r.slidesPerViewDynamic("previous",!0)+1,0))),n.rewind&&r.isBeginning){let n=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(n,e,t,i)}return n.loop&&0===r.activeIndex&&n.cssMode?(requestAnimationFrame(()=>{r.slideTo(f,e,t,i)}),!0):r.slideTo(f,e,t,i)},slideReset:function(e,t,i){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,r){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===r&&(r=.5);let n=this.activeIndex,s=Math.min(this.params.slidesPerGroupSkip,n),a=s+Math.floor((n-s)/this.params.slidesPerGroup),l=this.rtlTranslate?this.translate:-this.translate;if(l>=this.snapGrid[a]){let e=this.snapGrid[a];l-e>(this.snapGrid[a+1]-e)*r&&(n+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];l-e<=(this.snapGrid[a]-e)*r&&(n-=this.params.slidesPerGroup)}return n=Math.min(n=Math.max(n,0),this.slidesGrid.length-1),this.slideTo(n,e,t,i)},slideToClickedSlide:function(){let e,t=this,{params:i,slidesEl:r}=t,n="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,s=t.clickedIndex,a=t.isElement?"swiper-slide":`.${i.slideClass}`;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?s<t.loopedSlides-n/2||s>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),s=t.getSlideIndex((0,o.e)(r,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(s)})):t.slideTo(s):s>t.slides.length-n?(t.loopFix(),s=t.getSlideIndex((0,o.e)(r,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(s)})):t.slideTo(s)}else t.slideTo(s)}},loop:{loopCreate:function(e){let{params:t,slidesEl:i}=this;!t.loop||this.virtual&&this.params.virtual.enabled||((0,o.e)(i,`.${t.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),this.loopFix({slideRealIndex:e,direction:t.centeredSlides?void 0:"next"}))},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:r,setTranslate:n,activeSlideIndex:s,byController:a,byMousewheel:l}=void 0===e?{}:e,o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");let{slides:u,allowSlidePrev:c,allowSlideNext:d,slidesEl:p,params:h}=o;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&h.virtual.enabled){i&&(h.centeredSlides||0!==o.snapIndex?h.centeredSlides&&o.snapIndex<h.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0):o.slideTo(o.virtual.slides.length,0,!1,!0)),o.allowSlidePrev=c,o.allowSlideNext=d,o.emit("loopFix");return}let m="auto"===h.slidesPerView?o.slidesPerViewDynamic():Math.ceil(parseFloat(h.slidesPerView,10)),f=h.loopedSlides||m;f%h.slidesPerGroup!=0&&(f+=h.slidesPerGroup-f%h.slidesPerGroup),o.loopedSlides=f;let g=[],v=[],y=o.activeIndex;void 0===s?s=o.getSlideIndex(o.slides.filter(e=>e.classList.contains(h.slideActiveClass))[0]):y=s;let b="next"===r||!r,w="prev"===r||!r,x=0,E=0;if(s<f){x=Math.max(f-s,h.slidesPerGroup);for(let e=0;e<f-s;e+=1){let t=e-Math.floor(e/u.length)*u.length;g.push(u.length-t-1)}}else if(s>o.slides.length-2*f){E=Math.max(s-(o.slides.length-2*f),h.slidesPerGroup);for(let e=0;e<E;e+=1){let t=e-Math.floor(e/u.length)*u.length;v.push(t)}}if(w&&g.forEach(e=>{o.slides[e].swiperLoopMoveDOM=!0,p.prepend(o.slides[e]),o.slides[e].swiperLoopMoveDOM=!1}),b&&v.forEach(e=>{o.slides[e].swiperLoopMoveDOM=!0,p.append(o.slides[e]),o.slides[e].swiperLoopMoveDOM=!1}),o.recalcSlides(),"auto"===h.slidesPerView&&o.updateSlides(),h.watchSlidesProgress&&o.updateSlidesOffset(),i){if(g.length>0&&w)if(void 0===t){let e=o.slidesGrid[y],t=o.slidesGrid[y+x]-e;l?o.setTranslate(o.translate-t):(o.slideTo(y+x,0,!1,!0),n&&(o.touches[o.isHorizontal()?"startX":"startY"]+=t,o.touchEventsData.currentTranslate=o.translate))}else n&&(o.slideToLoop(t,0,!1,!0),o.touchEventsData.currentTranslate=o.translate);else if(v.length>0&&b)if(void 0===t){let e=o.slidesGrid[y],t=o.slidesGrid[y-E]-e;l?o.setTranslate(o.translate-t):(o.slideTo(y-E,0,!1,!0),n&&(o.touches[o.isHorizontal()?"startX":"startY"]+=t,o.touchEventsData.currentTranslate=o.translate))}else o.slideToLoop(t,0,!1,!0)}if(o.allowSlidePrev=c,o.allowSlideNext=d,o.controller&&o.controller.control&&!a){let e={slideRealIndex:t,direction:r,setTranslate:n,activeSlideIndex:s,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===h.slidesPerView&&i})}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...e,slideTo:o.controller.control.params.slidesPerView===h.slidesPerView&&i})}o.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let e=(0,l.g)(),{params:t}=this;this.onTouchStart=m.bind(this),this.onTouchMove=f.bind(this),this.onTouchEnd=g.bind(this),t.cssMode&&(this.onScroll=b.bind(this)),this.onClick=y.bind(this),this.onLoad=w.bind(this),x||(e.addEventListener("touchstart",E),x=!0),S(this,"on")},detachEvents:function(){S(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:r,el:n}=e,s=r.breakpoints;if(!s||s&&0===Object.keys(s).length)return;let a=e.getBreakpoint(s,e.params.breakpointsBase,e.el);if(!a||e.currentBreakpoint===a)return;let l=(a in s?s[a]:void 0)||e.originalParams,u=C(e,r),c=C(e,l),d=r.enabled;u&&!c?(n.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!u&&c&&(n.classList.add(`${r.containerModifierClass}grid`),(l.grid.fill&&"column"===l.grid.fill||!l.grid.fill&&"column"===r.grid.fill)&&n.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===l[t])return;let i=r[t]&&r[t].enabled,n=l[t]&&l[t].enabled;i&&!n&&e[t].disable(),!i&&n&&e[t].enable()});let p=l.direction&&l.direction!==r.direction,h=r.loop&&(l.slidesPerView!==r.slidesPerView||p),m=r.loop;p&&i&&e.changeDirection(),(0,o.q)(e.params,l);let f=e.params.enabled,g=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),d&&!f?e.disable():!d&&f&&e.enable(),e.currentBreakpoint=a,e.emit("_beforeBreakpoint",l),i&&(h?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!m&&g?(e.loopCreate(t),e.updateSlides()):m&&!g&&e.loopDestroy()),e.emit("breakpoint",l)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let r=!1,n=(0,l.a)(),s="window"===t?n.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:s*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:s,value:l}=a[e];"window"===t?n.matchMedia(`(min-width: ${l}px)`).matches&&(r=s):l<=i.clientWidth&&(r=s)}return r||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:r,device:n}=this,s=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(r=>{e[r]&&i.push(t+r)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...s),r.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e.classList.remove(...t),this.emitContainerClasses()}}},P={};class k{constructor(){let e,t;for(var i=arguments.length,r=Array(i),a=0;a<i;a++)r[a]=arguments[a];1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?t=r[0]:[e,t]=r,t||(t={}),t=(0,o.q)({},t),e&&!t.el&&(t.el=e);let c=(0,l.g)();if(t.el&&"string"==typeof t.el&&c.querySelectorAll(t.el).length>1){let e=[];return c.querySelectorAll(t.el).forEach(i=>{let r=(0,o.q)({},t,{el:i});e.push(new k(r))}),e}let d=this;d.__swiper__=!0,d.support=u(),d.device=function(e){return void 0===e&&(e={}),n||(n=function(e){let{userAgent:t}=void 0===e?{}:e,i=u(),r=(0,l.a)(),n=r.navigator.platform,s=t||r.navigator.userAgent,a={ios:!1,android:!1},o=r.screen.width,c=r.screen.height,d=s.match(/(Android);?[\s\/]+([\d.]+)?/),p=s.match(/(iPad).*OS\s([\d_]+)/),h=s.match(/(iPod)(.*OS\s([\d_]+))?/),m=!p&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="MacIntel"===n;return!p&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${c}`)>=0&&((p=s.match(/(Version)\/([\d.]+)/))||(p=[0,1,"13_0_0"]),f=!1),d&&"Win32"!==n&&(a.os="android",a.android=!0),(p||m||h)&&(a.os="ios",a.ios=!0),a}(e)),n}({userAgent:t.userAgent}),s||(s=function(){let e=(0,l.a)(),t=!1;function i(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(i()){let i=String(e.navigator.userAgent);if(i.includes("Version/")){let[e,r]=i.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));t=e<16||16===e&&r<2}}return{isSafari:t||i(),needPerspectiveFix:t,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),d.browser=s,d.eventsListeners={},d.eventsAnyListeners=[],d.modules=[...d.__modules__],t.modules&&Array.isArray(t.modules)&&d.modules.push(...t.modules);let p={};d.modules.forEach(e=>{e({params:t,swiper:d,extendParams:function(e,t){return function(i){void 0===i&&(i={});let r=Object.keys(i)[0],n=i[r];return"object"!=typeof n||null===n?void(0,o.q)(t,i):(!0===e[r]&&(e[r]={enabled:!0}),"navigation"===r&&e[r]&&e[r].enabled&&!e[r].prevEl&&!e[r].nextEl&&(e[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&e[r]&&e[r].enabled&&!e[r].el&&(e[r].auto=!0),r in e&&"enabled"in n)?void("object"==typeof e[r]&&!("enabled"in e[r])&&(e[r].enabled=!0),!e[r]&&(e[r]={enabled:!1}),(0,o.q)(t,i)):void(0,o.q)(t,i)}}(t,p),on:d.on.bind(d),once:d.once.bind(d),off:d.off.bind(d),emit:d.emit.bind(d)})});let h=(0,o.q)({},M,p);return d.params=(0,o.q)({},h,P,t),d.originalParams=(0,o.q)({},d.params),d.passedParams=(0,o.q)({},t),d.params&&d.params.on&&Object.keys(d.params.on).forEach(e=>{d.on(e,d.params.on[e])}),d.params&&d.params.onAny&&d.onAny(d.params.onAny),Object.assign(d,{enabled:d.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===d.params.direction,isVertical:()=>"vertical"===d.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:d.params.allowSlideNext,allowSlidePrev:d.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:d.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:d.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),d.emit("_swiper"),d.params.init&&d.init(),d}getSlideIndex(e){let{slidesEl:t,params:i}=this,r=(0,o.e)(t,`.${i.slideClass}, swiper-slide`),n=(0,o.g)(r[0]);return(0,o.g)(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>+t.getAttribute("data-swiper-slide-index")===e)[0])}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,o.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),r=(this.maxTranslate()-i)*e+i;this.translateTo(r,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let r=e.getSlideClasses(i);t.push({slideEl:i,classNames:r}),e.emit("_slideClass",i,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:r,slidesGrid:n,slidesSizesGrid:s,size:a,activeIndex:l}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=r[l]?r[l].swiperSlideSize:0;for(let i=l+1;i<r.length;i+=1)r[i]&&!e&&(t+=r[i].swiperSlideSize,o+=1,t>a&&(e=!0));for(let i=l-1;i>=0;i-=1)r[i]&&!e&&(t+=r[i].swiperSlideSize,o+=1,t>a&&(e=!0))}else if("current"===e)for(let e=l+1;e<r.length;e+=1)(t?n[e]+s[e]-n[l]<a:n[e]-n[l]<a)&&(o+=1);else for(let e=l-1;e>=0;e-=1)n[l]-n[e]<a&&(o+=1);return o}update(){let e,t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:r}=t;function n(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&c(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),r.freeMode&&r.freeMode.enabled&&!r.cssMode)n(),r.autoHeight&&t.updateAutoHeight();else{if(("auto"===r.slidesPerView||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){let i=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||n()}r.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&"SWIPER-CONTAINER"===i.parentNode.host.nodeName&&(t.isElement=!0);let r=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,n=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(r()):(0,o.e)(i,r())[0];return!n&&t.params.createElements&&(n=(0,o.c)("div",t.params.wrapperClass),i.append(n),(0,o.e)(i,`.${t.params.slideClass}`).forEach(e=>{n.append(e)})),Object.assign(t,{el:i,wrapperEl:n,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:n,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,o.l)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,o.l)(i,"direction")),wrongRTL:"-webkit-box"===(0,o.l)(n,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?c(t,e):e.addEventListener("load",e=>{c(t,e.target)})}),p(t),t.initialized=!0,p(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:r,el:n,wrapperEl:s,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),r.loop&&i.loopDestroy(),t&&(i.removeClasses(),n.removeAttribute("style"),s.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(r.slideVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el.swiper=null,(0,o.r)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,o.q)(P,e)}static get extendedDefaults(){return P}static get defaults(){return M}static installModule(e){k.prototype.__modules__||(k.prototype.__modules__=[]);let t=k.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>k.installModule(e)):k.installModule(e),k}}Object.keys(T).forEach(e=>{Object.keys(T[e]).forEach(t=>{k.prototype[t]=T[e][t]})}),k.use([function(e){let{swiper:t,on:i,emit:r}=e,n=(0,l.a)(),s=null,a=null,o=()=>{t&&!t.destroyed&&t.initialized&&(r("beforeResize"),r("resize"))},u=()=>{t&&!t.destroyed&&t.initialized&&r("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==n.ResizeObserver)return void(t&&!t.destroyed&&t.initialized&&(s=new ResizeObserver(e=>{a=n.requestAnimationFrame(()=>{let{width:i,height:r}=t,n=i,s=r;e.forEach(e=>{let{contentBoxSize:i,contentRect:r,target:a}=e;a&&a!==t.el||(n=r?r.width:(i[0]||i).inlineSize,s=r?r.height:(i[0]||i).blockSize)}),(n!==i||s!==r)&&o()})})).observe(t.el));n.addEventListener("resize",o),n.addEventListener("orientationchange",u)}),i("destroy",()=>{a&&n.cancelAnimationFrame(a),s&&s.unobserve&&t.el&&(s.unobserve(t.el),s=null),n.removeEventListener("resize",o),n.removeEventListener("orientationchange",u)})},function(e){let{swiper:t,extendParams:i,on:r,emit:n}=e,s=[],a=(0,l.a)(),u=function(e,i){void 0===i&&(i={});let r=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void n("observerUpdate",e[0]);let i=function(){n("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});r.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:void 0===i.childList||i.childList,characterData:void 0===i.characterData||i.characterData}),s.push(r)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,o.a)(t.hostEl);for(let t=0;t<e.length;t+=1)u(e[t])}u(t.hostEl,{childList:t.params.observeSlideChildren}),u(t.wrapperEl,{attributes:!1})}}),r("destroy",()=>{s.forEach(e=>{e.disconnect()}),s.splice(0,s.length)})}]);let A=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function L(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function N(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:L(t[i])&&L(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:N(e[i],t[i]):e[i]=t[i]})}function O(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function I(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function z(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function _(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e}).apply(this,arguments)}function D(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function R(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let F=(0,a.createContext)(null),j=(0,a.createContext)(null),V=(0,a.forwardRef)(function(e,t){var i;let{className:r,tag:n="div",wrapperTag:s="div",children:l,onSwiper:o,...u}=void 0===e?{}:e,c=!1,[d,p]=(0,a.useState)("swiper"),[h,m]=(0,a.useState)(null),[f,g]=(0,a.useState)(!1),v=(0,a.useRef)(!1),y=(0,a.useRef)(null),b=(0,a.useRef)(null),w=(0,a.useRef)(null),x=(0,a.useRef)(null),E=(0,a.useRef)(null),S=(0,a.useRef)(null),C=(0,a.useRef)(null),T=(0,a.useRef)(null),{params:P,passedParams:F,rest:V,events:H}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},r={},n={};N(i,M),i._emitClasses=!0,i.init=!1;let s={},a=A.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(l=>{void 0!==e[l]&&(a.indexOf(l)>=0?L(e[l])?(i[l]={},n[l]={},N(i[l],e[l]),N(n[l],e[l])):(i[l]=e[l],n[l]=e[l]):0===l.search(/on[A-Z]/)&&"function"==typeof e[l]?t?r[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:i.on[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:s[l]=e[l])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:n,rest:s,events:r}}(u),{slides:q,slots:B}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(D(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let r=function e(t){let i=[];return a.Children.toArray(t).forEach(t=>{D(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);r.length>0?r.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(l),G=()=>{g(!f)};Object.assign(P.on,{_containerClasses(e,t){p(t)}});let W=()=>{Object.assign(P.on,H),c=!0;let e={...P};if(delete e.wrapperClass,b.current=new k(e),b.current.virtual&&b.current.params.virtual.enabled){b.current.virtual.slides=q;let e={cache:!1,slides:q,renderExternal:m,renderExternalUpdate:!1};N(b.current.params.virtual,e),N(b.current.originalParams.virtual,e)}};return y.current||W(),b.current&&b.current.on("_beforeBreakpoint",G),(0,a.useEffect)(()=>()=>{b.current&&b.current.off("_beforeBreakpoint",G)}),(0,a.useEffect)(()=>{!v.current&&b.current&&(b.current.emitSlidesClasses(),v.current=!0)}),R(()=>{if(t&&(t.current=y.current),y.current)return b.current.destroyed&&W(),!function(e,t){let{el:i,nextEl:r,prevEl:n,paginationEl:s,scrollbarEl:a,swiper:l}=e;O(t)&&r&&n&&(l.params.navigation.nextEl=r,l.originalParams.navigation.nextEl=r,l.params.navigation.prevEl=n,l.originalParams.navigation.prevEl=n),I(t)&&s&&(l.params.pagination.el=s,l.originalParams.pagination.el=s),z(t)&&a&&(l.params.scrollbar.el=a,l.originalParams.scrollbar.el=a),l.init(i)}({el:y.current,nextEl:E.current,prevEl:S.current,paginationEl:C.current,scrollbarEl:T.current,swiper:b.current},P),o&&o(b.current),()=>{b.current&&!b.current.destroyed&&b.current.destroy(!0,!1)}},[]),R(()=>{!c&&H&&b.current&&Object.keys(H).forEach(e=>{b.current.on(e,H[e])});let e=function(e,t,i,r,n){let s=[];if(!t)return s;let a=e=>{0>s.indexOf(e)&&s.push(e)};if(i&&r){let e=r.map(n),t=i.map(n);e.join("")!==t.join("")&&a("children"),r.length!==i.length&&a("children")}return A.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t)if(L(e[i])&&L(t[i])){let r=Object.keys(e[i]),n=Object.keys(t[i]);r.length!==n.length?a(i):(r.forEach(r=>{e[i][r]!==t[i][r]&&a(i)}),n.forEach(r=>{e[i][r]!==t[i][r]&&a(i)}))}else e[i]!==t[i]&&a(i)}),s}(F,w.current,q,x.current,e=>e.key);return w.current=F,x.current=q,e.length&&b.current&&!b.current.destroyed&&function(e){let t,i,r,n,s,a,l,o,{swiper:u,slides:c,passedParams:d,changedParams:p,nextEl:h,prevEl:m,scrollbarEl:f,paginationEl:g}=e,v=p.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:y,pagination:b,navigation:w,scrollbar:x,virtual:E,thumbs:S}=u;p.includes("thumbs")&&d.thumbs&&d.thumbs.swiper&&y.thumbs&&!y.thumbs.swiper&&(t=!0),p.includes("controller")&&d.controller&&d.controller.control&&y.controller&&!y.controller.control&&(i=!0),p.includes("pagination")&&d.pagination&&(d.pagination.el||g)&&(y.pagination||!1===y.pagination)&&b&&!b.el&&(r=!0),p.includes("scrollbar")&&d.scrollbar&&(d.scrollbar.el||f)&&(y.scrollbar||!1===y.scrollbar)&&x&&!x.el&&(n=!0),p.includes("navigation")&&d.navigation&&(d.navigation.prevEl||m)&&(d.navigation.nextEl||h)&&(y.navigation||!1===y.navigation)&&w&&!w.prevEl&&!w.nextEl&&(s=!0);let C=e=>{u[e]&&(u[e].destroy(),"navigation"===e?(u.isElement&&(u[e].prevEl.remove(),u[e].nextEl.remove()),y[e].prevEl=void 0,y[e].nextEl=void 0,u[e].prevEl=void 0,u[e].nextEl=void 0):(u.isElement&&u[e].el.remove(),y[e].el=void 0,u[e].el=void 0))};p.includes("loop")&&u.isElement&&(y.loop&&!d.loop?a=!0:!y.loop&&d.loop?l=!0:o=!0),v.forEach(e=>{if(L(y[e])&&L(d[e]))N(y[e],d[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in d[e]&&!d[e].enabled&&C(e);else{let t=d[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&C(e):y[e]=d[e]}}),v.includes("controller")&&!i&&u.controller&&u.controller.control&&y.controller&&y.controller.control&&(u.controller.control=y.controller.control),p.includes("children")&&c&&E&&y.virtual.enabled&&(E.slides=c,E.update(!0)),p.includes("children")&&c&&y.loop&&(o=!0),t&&S.init()&&S.update(!0),i&&(u.controller.control=y.controller.control),r&&(u.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-pagination"),g.part.add("pagination"),u.el.appendChild(g)),g&&(y.pagination.el=g),b.init(),b.render(),b.update()),n&&(u.isElement&&(!f||"string"==typeof f)&&((f=document.createElement("div")).classList.add("swiper-scrollbar"),f.part.add("scrollbar"),u.el.appendChild(f)),f&&(y.scrollbar.el=f),x.init(),x.updateSize(),x.setTranslate()),s&&(u.isElement&&(h&&"string"!=typeof h||((h=document.createElement("div")).classList.add("swiper-button-next"),h.innerHTML=u.hostEl.constructor.nextButtonSvg,h.part.add("button-next"),u.el.appendChild(h)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),m.innerHTML=u.hostEl.constructor.prevButtonSvg,m.part.add("button-prev"),u.el.appendChild(m))),h&&(y.navigation.nextEl=h),m&&(y.navigation.prevEl=m),w.init(),w.update()),p.includes("allowSlideNext")&&(u.allowSlideNext=d.allowSlideNext),p.includes("allowSlidePrev")&&(u.allowSlidePrev=d.allowSlidePrev),p.includes("direction")&&u.changeDirection(d.direction,!1),(a||o)&&u.loopDestroy(),(l||o)&&u.loopCreate(),u.update()}({swiper:b.current,slides:q,passedParams:F,changedParams:e,nextEl:E.current,prevEl:S.current,scrollbarEl:T.current,paginationEl:C.current}),()=>{H&&b.current&&Object.keys(H).forEach(e=>{b.current.off(e,H[e])})}}),R(()=>{var e;(e=b.current)&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())},[h]),a.createElement(n,$({ref:y,className:_(`${d}${r?` ${r}`:""}`)},V),a.createElement(j.Provider,{value:b.current},B["container-start"],a.createElement(s,{className:(void 0===(i=P.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},B["wrapper-start"],P.virtual?function(e,t,i){if(!i)return null;let r=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},n=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:s,to:l}=i,o=e.params.loop?-t.length:0,u=e.params.loop?2*t.length:t.length,c=[];for(let e=o;e<u;e+=1)e>=s&&e<=l&&c.push(t[r(e)]);return c.map((t,i)=>a.cloneElement(t,{swiper:e,style:n,key:`slide-${i}`}))}(b.current,q,h):q.map((e,t)=>a.cloneElement(e,{swiper:b.current,swiperSlideIndex:t})),B["wrapper-end"]),O(P)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:S,className:"swiper-button-prev"}),a.createElement("div",{ref:E,className:"swiper-button-next"})),z(P)&&a.createElement("div",{ref:T,className:"swiper-scrollbar"}),I(P)&&a.createElement("div",{ref:C,className:"swiper-pagination"}),B["container-end"]))});V.displayName="Swiper";let H=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:r,className:n="",swiper:s,zoom:l,lazy:o,virtualIndex:u,swiperSlideIndex:c,...d}=void 0===e?{}:e,p=(0,a.useRef)(null),[h,m]=(0,a.useState)("swiper-slide"),[f,g]=(0,a.useState)(!1);function v(e,t,i){t===p.current&&m(i)}R(()=>{if(void 0!==c&&(p.current.swiperSlideIndex=c),t&&(t.current=p.current),p.current&&s){if(s.destroyed){"swiper-slide"!==h&&m("swiper-slide");return}return s.on("_slideClass",v),()=>{s&&s.off("_slideClass",v)}}}),R(()=>{s&&p.current&&!s.destroyed&&m(s.getSlideClasses(p.current))},[s]);let y={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},b=()=>"function"==typeof r?r(y):r;return a.createElement(i,$({ref:p,className:_(`${h}${n?` ${n}`:""}`),"data-swiper-slide-index":u,onLoad:()=>{g(!0)}},d),l&&a.createElement(F.Provider,{value:y},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof l?l:void 0},b(),o&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!l&&a.createElement(F.Provider,{value:y},b(),o&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"})))});H.displayName="SwiperSlide"},8561:()=>{},9408:()=>{},9984:(e,t,i)=>{"use strict";function r(e){return e}i.d(t,{A:()=>r})}}]);
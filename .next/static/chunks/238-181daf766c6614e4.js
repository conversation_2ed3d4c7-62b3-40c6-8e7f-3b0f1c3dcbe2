"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[238],{4143:(s,e,a)=>{a.d(e,{A:()=>o});var i=a(5155),l=a(2115),n=a(6874),c=a.n(n),r=a(7652);function o(){let s=(0,r.c3)("HeaderBtn"),[e,a]=(0,l.useState)(!1);return(0,i.jsx)(i.Fragment,{children:e?(0,i.jsx)(c(),{href:"tel:".concat(s("phone_no")),children:s("phone_no")}):(0,i.jsxs)("a",{style:{cursor:"pointer"},onClick:()=>{a(!e)},children:[s("phone_no").slice(0,7)," ****"]})})}},9238:(s,e,a)=>{a.d(e,{default:()=>C});var i=a(5155),l=a(2115);function n(s){let{scroll:e}=s;return(0,i.jsx)(i.Fragment,{children:e&&(0,i.jsx)("a",{className:"scroll-to-top scroll-to-target d-block",href:"#top",children:(0,i.jsx)("i",{className:"fas fa-angle-up"})})})}function c(){return(0,l.useEffect)(()=>{document.querySelectorAll("[data-bg]").forEach(s=>{s.style.backgroundImage="url(".concat(s.getAttribute("data-bg"),")")})},[]),(0,i.jsx)(i.Fragment,{})}var r=a(6874),o=a.n(r);function d(s){let{breadcrumbTitle:e}=s;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"page-header",children:[(0,i.jsx)("div",{className:"page-header__bg",style:{backgroundImage:"url(assets/images/pattern/store-pattern.png)"}}),(0,i.jsx)("div",{className:"page-header__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/page-header-pattern.png",alt:""})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"page-header__inner",children:[(0,i.jsx)("h2",{children:e}),(0,i.jsxs)("ul",{className:"thm-breadcrumb",children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"/",children:"Home"})}),(0,i.jsx)("li",{children:(0,i.jsx)("span",{className:"icon-right-arrow21"})}),(0,i.jsx)("li",{children:e})]})]})})]})})}function t(s){let{isPopup:e,handlePopup:a}=s;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"search-popup ".concat(e?"active":""),children:[(0,i.jsx)("div",{className:"search-popup__overlay search-toggler",onClick:a}),(0,i.jsx)("div",{className:"search-popup__content",children:(0,i.jsxs)("form",{action:"#",children:[(0,i.jsx)("label",{className:"sr-only",children:"search here"}),(0,i.jsx)("input",{type:"text",id:"search",placeholder:"Search Here..."}),(0,i.jsx)("button",{type:"submit","aria-label":"search submit",className:"thm-btn",children:(0,i.jsx)("i",{className:"icon-magnifying-glass"})})]})})]})})}function h(s){let{isSidebar:e,handleSidebar:a}=s;return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"xs-sidebar-group info-group info-sidebar ".concat(e?"isActive":""),children:[(0,i.jsx)("div",{className:"xs-overlay xs-bg-black",onClick:a}),(0,i.jsx)("div",{className:"xs-sidebar-widget",children:(0,i.jsxs)("div",{className:"sidebar-widget-container",children:[(0,i.jsx)("div",{className:"widget-heading",children:(0,i.jsx)(o(),{href:"#",className:"close-side-widget",children:"X"})}),(0,i.jsx)("div",{className:"sidebar-textwidget",children:(0,i.jsx)("div",{className:"sidebar-info-contents",children:(0,i.jsxs)("div",{className:"content-inner",children:[(0,i.jsx)("div",{className:"logo",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"/assets/images/resources/sidebar-logo.png",alt:""})})}),(0,i.jsxs)("div",{className:"content-box",children:[(0,i.jsx)("h4",{children:"About Us"}),(0,i.jsx)("div",{className:"inner-text",children:(0,i.jsx)("p",{children:"Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classNameical Latin literature from 45 BC, making it over 2000 years old."})})]}),(0,i.jsxs)("div",{className:"form-inner",children:[(0,i.jsx)("h4",{children:"Get a free quote"}),(0,i.jsxs)("form",{action:"/",method:"post",children:[(0,i.jsx)("div",{className:"form-group",children:(0,i.jsx)("input",{type:"text",name:"name",placeholder:"Name",required:""})}),(0,i.jsx)("div",{className:"form-group",children:(0,i.jsx)("input",{type:"email",name:"email",placeholder:"Email",required:""})}),(0,i.jsx)("div",{className:"form-group",children:(0,i.jsx)("textarea",{name:"message",placeholder:"Message..."})}),(0,i.jsx)("div",{className:"form-group message-btn",children:(0,i.jsxs)("button",{className:"thm-btn",type:"submit","data-loading-text":"Please wait...",children:["Submit Now",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,i.jsxs)("div",{className:"sidebar-contact-info",children:[(0,i.jsx)("h4",{children:"Contact Info"}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("span",{className:"icon-location1"})," 88 broklyn street, New York"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("span",{className:"icon-phone"}),(0,i.jsx)(o(),{href:"tel:123456789",children:"******-9990-153"})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("span",{className:"fa fa-envelope"}),(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})]})]})]}),(0,i.jsx)("div",{className:"thm-social-link1",children:(0,i.jsxs)("ul",{className:"social-box",children:[(0,i.jsx)("li",{className:"facebook",children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("i",{className:"icon-facebook-f","aria-hidden":"true"})})}),(0,i.jsx)("li",{className:"twitter",children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("i",{className:"icon-twitter","aria-hidden":"true"})})}),(0,i.jsx)("li",{className:"linkedin",children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("i",{className:"icon-instagram","aria-hidden":"true"})})}),(0,i.jsx)("li",{className:"gplus",children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("i",{className:"icon-linkedin","aria-hidden":"true"})})})]})})]})})})]})})]})})}var m=a(7652);function x(){let s=(0,m.c3)("MenuData");return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("ul",{className:"main-menu__list",children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#about",children:s("nav1")})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#product",children:s("nav3")})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#service",children:s("nav2")})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#branch",children:s("nav4")})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#store",children:s("nav5")})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#contact",children:s("nav6")})})]})})}var j=a(2550),N=a(5695);let v=(0,a(9984).A)({locales:["th","en","ru","ch"],defaultLocale:"th"});function p(){let s=(0,j.Ym)(),e=(0,N.useRouter)(),a=(0,N.usePathname)(),[n,c]=(0,l.useTransition)(),r=v.locales.map(s=>({value:s,label:s.toUpperCase()})),o={en:"Eng",ru:"Рус",th:"ไทย",ch:"中"};return(0,i.jsxs)("div",{className:"main-header__language-switcher",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-globe"})}),(0,i.jsx)("div",{className:"language-switcher clearfix",children:(0,i.jsx)("div",{className:"select-box clearfix",children:(0,i.jsx)("select",{className:"selectmenu wide",value:s,onChange:i=>{var l;(l=i.target.value)!==s&&c(()=>{let i=a.replace("/".concat(s),"")||"/",n="/".concat(l).concat(i);e.push(n)})},disabled:n,style:{cursor:"pointer"},children:r.map(s=>(0,i.jsx)("option",{value:s.value,children:o[s.value]},s.value))})})})]})}var b=a(4143);let f=s=>{let{isSidebar:e,handleMobileMenu:a,handleSidebar:n}=s,c=(0,m.c3)("MenuData");(0,m.c3)("HeaderBtn");let[r,d]=(0,l.useState)({status:!1,key:"",subMenuKey:""});return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"mobile-nav__wrapper",children:[(0,i.jsx)("div",{className:"mobile-nav__overlay mobile-nav__toggler",onClick:a}),(0,i.jsxs)("div",{className:"mobile-nav__content",children:[(0,i.jsx)("span",{className:"mobile-nav__close mobile-nav__toggler",onClick:a,children:(0,i.jsx)("i",{className:"fa fa-times"})}),(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/","aria-label":"logo image",children:(0,i.jsx)("img",{src:"/assets/images/resources/logo-wh.svg",width:"150",alt:""})})}),(0,i.jsx)("div",{className:"mobile-nav__container",children:(0,i.jsx)("div",{className:"collapse navbar-collapse show clearfix",id:"navbarSupportedContent",children:(0,i.jsx)("ul",{className:"main-menu__list",children:(0,i.jsxs)("li",{children:[(0,i.jsx)(o(),{href:"#about",children:c("nav1")}),(0,i.jsx)(o(),{href:"#product",children:c("nav3")}),(0,i.jsx)(o(),{href:"#service",children:c("nav2")}),(0,i.jsx)(o(),{href:"#branch",children:c("nav4")}),(0,i.jsx)(o(),{href:"#store",children:c("nav5")}),(0,i.jsx)(o(),{href:"#contact",children:c("nav6")})]})})})}),(0,i.jsx)("div",{className:"mobile-nav__container",children:(0,i.jsxs)("ul",{className:"mobile-nav__contact list-unstyled",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"fa fa-phone-alt"}),(0,i.jsx)("div",{style:{color:"white"},children:(0,i.jsx)(b.A,{})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("i",{className:"fa fa-envelope"}),(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})]})]})}),(0,i.jsxs)("div",{className:"mobile-nav__social",children:[(0,i.jsx)(o(),{href:"https://www.facebook.com/Sakwwth",className:"fab fa-facebook-f"}),(0,i.jsx)(o(),{href:"https://www.facebook.com/Sakwwth",className:"fab fa-youtube"}),(0,i.jsx)(o(),{href:"https://www.tiktok.com/@sakwoodworks",className:"fab fa-tiktok"}),(0,i.jsx)(o(),{href:"https://www.instagram.com/sakwoodworks",className:"fab fa-instagram"}),(0,i.jsx)(o(),{href:"https://lin.ee/smwoT3j",className:"fab fa-line"})]})]})]})})};function _(s){let{scroll:e,handlePopup:a,handleMobileMenu:l}=s;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("header",{className:"main-header main-header-one",children:(0,i.jsx)("nav",{className:"main-menu",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-one__inner",children:[(0,i.jsx)("div",{className:"main-header-one__top",children:(0,i.jsxs)("div",{className:"main-header-one__top-inner",children:[(0,i.jsx)("div",{className:"main-header-one__top-left",children:(0,i.jsx)("div",{className:"header-contact-style1",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Talk to Us"})," ",(0,i.jsx)(o(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Mail Us"})," ",(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})}),(0,i.jsxs)("div",{className:"main-header-one__top-right",children:[(0,i.jsxs)("div",{className:"header-social-links",children:[(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})}),(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})}),(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})}),(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})]}),(0,i.jsx)("div",{className:"header-search-box",children:(0,i.jsxs)(o(),{href:"#",className:"main-menu__search search-toggler",onClick:a,children:["Search",(0,i.jsx)("i",{className:"icon-search"})]})})]})]})}),(0,i.jsx)("div",{className:"main-header-one__bottom",children:(0,i.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,i.jsxs)("div",{className:"main-header-one__bottom-inner",children:[(0,i.jsxs)("div",{className:"main-header-one__bottom-left",children:[(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/logo-1.png",alt:""})})}),(0,i.jsx)("div",{className:"main-header-one__bottom-menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:l,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})]}),(0,i.jsxs)("div",{className:"main-header-one__bottom-right",children:[(0,i.jsx)("div",{className:"main-header-one__bottom-right-btn",children:(0,i.jsxs)(o(),{href:"contact",children:["Track Order",(0,i.jsx)("i",{className:"icon-right-arrow21"})]})}),(0,i.jsx)("div",{className:"login-box",children:(0,i.jsxs)(o(),{href:"#",children:[(0,i.jsx)("i",{className:"fa fa-sign-in"})," ",(0,i.jsxs)("span",{children:["Member ",(0,i.jsx)("br",{}),"Login"]})," "]})})]})]})})})]})})})})}),(0,i.jsx)("div",{className:"stricky-header stricky-header--style1 stricked-menu main-menu ".concat(e?"stricky-fixed":""),children:(0,i.jsx)("div",{className:"sticky-header__content",children:(0,i.jsx)("div",{className:"main-header-one__bottom",children:(0,i.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,i.jsxs)("div",{className:"main-header-one__bottom-inner",children:[(0,i.jsxs)("div",{className:"main-header-one__bottom-left",children:[(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/logo-1.png",alt:""})})}),(0,i.jsx)("div",{className:"main-header-one__bottom-menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:l,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})]}),(0,i.jsxs)("div",{className:"main-header-one__bottom-right",children:[(0,i.jsx)("div",{className:"main-header-one__bottom-right-btn",children:(0,i.jsxs)(o(),{href:"contact",children:["Track Order",(0,i.jsx)("i",{className:"icon-right-arrow21"})]})}),(0,i.jsx)("div",{className:"login-box",children:(0,i.jsxs)(o(),{href:"#",children:[(0,i.jsx)("i",{className:"fa fa-sign-in"})," ",(0,i.jsxs)("span",{children:["Member ",(0,i.jsx)("br",{}),"Login"]})," "]})})]})]})})})})}),(0,i.jsx)(f,{handleMobileMenu:l})]})}function u(s){let{scroll:e,handlePopup:a,handleSidebar:l,handleMobileMenu:n}=s;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("header",{className:"main-header main-header-two",children:(0,i.jsx)("nav",{className:"main-menu",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,i.jsxs)("div",{className:"main-header-two__inner",children:[(0,i.jsx)("div",{className:"logo-box-two",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/logo-2.png",alt:""})})}),(0,i.jsx)("div",{className:"main-header-two__top",children:(0,i.jsxs)("div",{className:"main-header-two__top-inner",children:[(0,i.jsx)("div",{className:"main-header-two__top-left",children:(0,i.jsx)("div",{className:"header-contact-style2",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-clock"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Opening Hours"}),(0,i.jsx)("p",{className:"text2",children:"Mon - Sat: 8am - 5pm"})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Send Us Mail"}),(0,i.jsx)("p",{className:"text2",children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone2"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Make A Call"}),(0,i.jsx)("p",{className:"text2",children:(0,i.jsx)(o(),{href:"tel:**********",children:"680 123 456 789"})})]})]})]})})}),(0,i.jsx)("div",{className:"main-header-two__top-right",children:(0,i.jsxs)("div",{className:"header-social-link-style2",children:[(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("p",{children:"Follow Us On:"})}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})})]})]})})]})}),(0,i.jsxs)("div",{className:"main-header-two__bottom",children:[(0,i.jsx)("div",{className:"shape1"}),(0,i.jsxs)("div",{className:"main-header-two__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-two__bottom-left",children:(0,i.jsx)("div",{className:"main-header-two__menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:n,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})}),(0,i.jsxs)("div",{className:"main-header-two__bottom-right",children:[(0,i.jsx)("div",{className:"header-search-box-two",children:(0,i.jsx)(o(),{href:"#",className:"main-menu__search search-toggler icon-search",onClick:a})}),(0,i.jsx)("div",{className:"sidebar-icon",children:(0,i.jsxs)(o(),{className:"navSidebar-button icon2",href:"#",onClick:l,children:[(0,i.jsx)("span",{className:"nav-sidebar-menu-1"}),(0,i.jsx)("span",{className:"nav-sidebar-menu-2"}),(0,i.jsx)("span",{className:"nav-sidebar-menu-3"})]})}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)(o(),{className:"thm-btn",href:"contact",children:["Track Order",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})]})]})})})})})}),(0,i.jsx)("div",{className:"stricky-header stricky-header--style2 stricked-menu main-menu ".concat(e?"stricky-fixed":""),children:(0,i.jsx)("div",{className:"sticky-header__content",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,i.jsxs)("div",{className:"main-header-two__inner",children:[(0,i.jsx)("div",{className:"logo-box-two",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/logo-2.png",alt:""})})}),(0,i.jsx)("div",{className:"main-header-two__top",children:(0,i.jsxs)("div",{className:"main-header-two__top-inner",children:[(0,i.jsx)("div",{className:"main-header-two__top-left",children:(0,i.jsx)("div",{className:"header-contact-style2",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-clock"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Opening Hours"}),(0,i.jsx)("p",{className:"text2",children:"Mon - Sat: 8am - 5pm"})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Send Us Mail"}),(0,i.jsx)("p",{className:"text2",children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone2"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{className:"text1",children:"Make A Call"}),(0,i.jsx)("p",{className:"text2",children:(0,i.jsx)(o(),{href:"tel:**********",children:"680 123 456 789"})})]})]})]})})}),(0,i.jsx)("div",{className:"main-header-two__top-right",children:(0,i.jsxs)("div",{className:"header-social-link-style2",children:[(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("p",{children:"Follow Us On:"})}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})})]})]})})]})}),(0,i.jsxs)("div",{className:"main-header-two__bottom",children:[(0,i.jsx)("div",{className:"shape1"}),(0,i.jsxs)("div",{className:"main-header-two__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-two__bottom-left",children:(0,i.jsx)("div",{className:"main-header-two__menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:n,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})}),(0,i.jsxs)("div",{className:"main-header-two__bottom-right",children:[(0,i.jsx)("div",{className:"header-search-box-two",children:(0,i.jsx)(o(),{href:"#",className:"main-menu__search search-toggler icon-search",onClick:a})}),(0,i.jsx)("div",{className:"sidebar-icon",children:(0,i.jsxs)(o(),{className:"navSidebar-button icon2",href:"#",onClick:l,children:[(0,i.jsx)("span",{className:"nav-sidebar-menu-1"}),(0,i.jsx)("span",{className:"nav-sidebar-menu-2"}),(0,i.jsx)("span",{className:"nav-sidebar-menu-3"})]})}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)(o(),{className:"thm-btn",href:"contact",children:["Track Order",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})]})]})})})})})}),(0,i.jsx)(f,{handleMobileMenu:n})]})}function g(s){let{scroll:e,handleMobileMenu:a}=s,l=(0,m.c3)("HeaderBtn");return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("header",{className:"main-header main-header-three",children:(0,i.jsx)("nav",{className:"main-menu",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,i.jsx)("div",{className:"main-header-three__top",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-three__top-inner",children:[(0,i.jsx)("div",{className:"header-contact-style1",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)(b.A,{})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})})]})]})}),(0,i.jsx)("div",{className:"main-header-three__right",children:(0,i.jsxs)(o(),{className:"thm-btn2",href:"#contact",children:[l("btn_text"),(0,i.jsx)("span",{className:"hover-btn hover-cx"}),(0,i.jsx)("span",{className:"hover-btn hover-cx2"}),(0,i.jsx)("span",{className:"hover-btn hover-cx3"}),(0,i.jsx)("span",{className:"hover-btn hover-cx4"})]})})]})})}),(0,i.jsx)("div",{className:"main-header-three__bottom",style:{backgroundColor:"white"},children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-three__bottom-left",children:(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,i.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,i.jsx)(p,{}),(0,i.jsx)("div",{className:"main-header-three__menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:a,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})]})]})})})]})})})}),(0,i.jsx)("div",{className:"stricky-header stricky-header--style3 stricked-menu main-menu ".concat(e?"stricky-fixed":""),children:(0,i.jsx)("div",{className:"sticky-header__content",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,i.jsx)("div",{className:"main-header-three__top",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"main-header-three__top-inner",children:(0,i.jsx)("div",{className:"header-contact-style1",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Talk to Us"})," ",(0,i.jsx)(o(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Mail Us"})," ",(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})})})}),(0,i.jsx)("div",{className:"main-header-three__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-three__bottom-left",children:(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,i.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,i.jsx)(p,{}),(0,i.jsx)("div",{className:"main-header-three__menu",children:(0,i.jsxs)("div",{className:"main-menu__main-menu-box",children:[(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:a,children:(0,i.jsx)("i",{className:"fa fa-bars"})}),(0,i.jsx)(x,{})]})})]})]})})})]})})})}),(0,i.jsx)(f,{handleMobileMenu:a})]})}function w(s){let{scroll:e,handleMobileMenu:a}=s;return(0,m.c3)("HeaderBtn"),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("header",{className:"main-header main-header-three",children:(0,i.jsx)("nav",{className:"main-menu",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsx)("div",{className:"main-menu__wrapper-inner",children:(0,i.jsx)("div",{className:"main-header-three__bottom",style:{backgroundColor:"white",marginTop:"0px"},children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-three__bottom-left",children:(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,i.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,i.jsx)(p,{}),(0,i.jsx)("div",{className:"main-header-three__menu",children:(0,i.jsx)("div",{className:"main-menu__main-menu-box",children:(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:a,children:(0,i.jsx)("i",{className:"fa fa-bars"})})})})]})]})})})})})})}),(0,i.jsx)("div",{className:"stricky-header stricky-header--style3 stricked-menu main-menu ".concat(e?"stricky-fixed":""),children:(0,i.jsx)("div",{className:"sticky-header__content",children:(0,i.jsx)("div",{className:"main-menu__wrapper",children:(0,i.jsxs)("div",{className:"main-menu__wrapper-inner",children:[(0,i.jsx)("div",{className:"main-header-three__top",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"main-header-three__top-inner",children:(0,i.jsx)("div",{className:"header-contact-style1",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Talk to Us"})," ",(0,i.jsx)(o(),{href:"tel:**********",children:"[+123 456 789]"})]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{children:"Mail Us"})," ",(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"[<EMAIL>]"})]})})]})]})})})})}),(0,i.jsx)("div",{className:"main-header-three__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"main-header-three__bottom-inner",children:[(0,i.jsx)("div",{className:"main-header-three__bottom-left",children:(0,i.jsx)("div",{className:"logo-box",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"/assets/images/resources/logo.svg",alt:""})})})}),(0,i.jsxs)("div",{className:"main-header-three__bottom-middle",children:[(0,i.jsx)(p,{}),(0,i.jsx)("div",{className:"main-header-three__menu",children:(0,i.jsx)("div",{className:"main-menu__main-menu-box",children:(0,i.jsx)(o(),{href:"#",className:"mobile-nav__toggler",onClick:a,children:(0,i.jsx)("i",{className:"fa fa-bars"})})})})]})]})})})]})})})}),(0,i.jsx)(f,{handleMobileMenu:a})]})}function k(){return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("footer",{className:"footer-one",children:[(0,i.jsx)("div",{className:"footer-one__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,i.jsx)("div",{className:"footer-one__top",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"footer-one__top-inner",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,i.jsx)("div",{className:"footer-one__about-logo",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,i.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,i.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-support"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:"Make a Call"}),(0,i.jsx)("h4",{children:(0,i.jsx)(o(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Quick Links ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"/",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"about",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"service",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"project",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"contact",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Get In Touch ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsx)("div",{className:"footer-one__contact-box",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-address"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:["3060 Commercial Street Road ",(0,i.jsx)("br",{})," Fratton, Australia"]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Subscribe Us ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,i.jsx)("br",{}),"thoughts, and insights"]}),(0,i.jsx)("div",{className:"footer-one__subscribe-form",children:(0,i.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,i.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,i.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})})}),(0,i.jsx)("div",{className:"footer-one__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,i.jsx)("div",{className:"footer-one__bottom-text",children:(0,i.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,i.jsx)(o(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,i.jsx)("div",{className:"footer-one__social-links",children:(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function y(){return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,i.jsx)("div",{className:"footer-one__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,i.jsx)("div",{className:"shape3 float-bob-y",children:(0,i.jsx)("img",{src:"assets/images/shapes/footer-v2-shape3.png",alt:""})}),(0,i.jsx)("div",{className:"footer-one__top",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"footer-one--two__cta",children:[(0,i.jsx)("div",{className:"shape1",children:(0,i.jsx)("img",{className:"float-bob-x3",src:"assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,i.jsx)("div",{className:"shape2",children:(0,i.jsx)("img",{className:"float-bob-y",src:"assets/images/shapes/footer-v2-shape1.png",alt:""})}),(0,i.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("h2",{children:"Efficient, Safe, & Swift Logistics Solution!"})}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)(o(),{className:"thm-btn",href:"contact",children:["Contact with Us",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,i.jsx)("div",{className:"footer-one__top-inner",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,i.jsx)("div",{className:"footer-one__about-logo",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,i.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,i.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-support"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:"Make a Call"}),(0,i.jsx)("h4",{children:(0,i.jsx)(o(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Quick Links ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"/",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"about",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"service",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"project",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"contact",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Get In Touch ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsx)("div",{className:"footer-one__contact-box",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-address"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:["3060 Commercial Street Road ",(0,i.jsx)("br",{})," Fratton, Australia"]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Subscribe Us ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,i.jsx)("br",{}),"thoughts, and insights"]}),(0,i.jsx)("div",{className:"footer-one__subscribe-form",children:(0,i.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,i.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,i.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})]})}),(0,i.jsx)("div",{className:"footer-one__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,i.jsx)("div",{className:"footer-one__bottom-text",children:(0,i.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,i.jsx)(o(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,i.jsx)("div",{className:"footer-one__social-links",children:(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function S(){return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,i.jsx)("div",{className:"footer-one__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,i.jsx)("div",{className:"shape3 float-bob-y",children:(0,i.jsx)("img",{src:"assets/images/shapes/footer-v2-shape3.png",alt:""})}),(0,i.jsx)("div",{className:"footer-one__top",children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"footer-one--two__cta",children:[(0,i.jsx)("div",{className:"shape1",children:(0,i.jsx)("img",{className:"float-bob-x3",src:"assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,i.jsx)("div",{className:"shape2",children:(0,i.jsx)("img",{className:"float-bob-y",src:"assets/images/shapes/footer-v2-shape1.png",alt:""})}),(0,i.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("h2",{children:"Efficient, Safe, & Swift Logistics Solution!"})}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)(o(),{className:"thm-btn",href:"contact",children:["Contact with Us",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]}),(0,i.jsx)("div",{className:"footer-one__top-inner",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"100ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__about",children:[(0,i.jsx)("div",{className:"footer-one__about-logo",children:(0,i.jsx)(o(),{href:"/",children:(0,i.jsx)("img",{src:"assets/images/resources/footer-logo.png",alt:""})})}),(0,i.jsx)("p",{className:"footer-one__about-text",children:"Logistic service provider company plays a pivotal role in the global supply chain logistic service provider."}),(0,i.jsxs)("div",{className:"footer-one__about-contact-info",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-support"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:"Make a Call"}),(0,i.jsx)("h4",{children:(0,i.jsx)(o(),{href:"tel:+**********",children:"+*********** 789"})})]})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__quick-links",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Quick Links ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("ul",{className:"footer-one__quick-links-list",children:[(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"/",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Home"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"about",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," About Us"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"service",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Service"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"project",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Latest Project"]})}),(0,i.jsx)("li",{children:(0,i.jsxs)(o(),{href:"contact",children:[(0,i.jsx)("span",{className:"icon-right-arrow1"})," Contact Us"]})})]})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__contact",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Get In Touch ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsx)("div",{className:"footer-one__contact-box",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-address"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("p",{children:["3060 Commercial Street Road ",(0,i.jsx)("br",{})," Fratton, Australia"]})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:(0,i.jsx)(o(),{href:"tel:**********",children:"+*********** 789 "})})})]})]})})]})}),(0,i.jsx)("div",{className:"col-xl-3 col-lg-6 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:(0,i.jsxs)("div",{className:"footer-widget__single footer-one__subscribe",children:[(0,i.jsx)("div",{className:"title",children:(0,i.jsxs)("h2",{children:["Subscribe Us ",(0,i.jsx)("span",{className:"icon-plane3"})]})}),(0,i.jsxs)("p",{className:"footer-one__subscribe-text",children:["Sign up for alerts, our latest blogs, ",(0,i.jsx)("br",{}),"thoughts, and insights"]}),(0,i.jsx)("div",{className:"footer-one__subscribe-form",children:(0,i.jsxs)("form",{className:"subscribe-form",action:"#",children:[(0,i.jsx)("input",{type:"email",name:"email",placeholder:"Your E-mail"}),(0,i.jsxs)("button",{type:"submit",className:"thm-btn",children:["Subcribe",(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})]})})]})})]})})]})}),(0,i.jsx)("div",{className:"footer-one__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"footer-one__bottom-inner",children:[(0,i.jsx)("div",{className:"footer-one__bottom-text",children:(0,i.jsxs)("p",{children:["\xa9 Copyright 2024 ",(0,i.jsx)(o(),{href:"/",children:"Logistiq."})," All Rights Reserved"]})}),(0,i.jsx)("div",{className:"footer-one__social-links",children:(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-twitter1"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"#",children:(0,i.jsx)("span",{className:"icon-linkedin"})})})]})})]})})})]})})}function M(){let s=(0,m.c3)("Footer"),e=(0,m.c3)("HeaderBtn");return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("footer",{className:"footer-one footer-one--two",children:[(0,i.jsx)("div",{className:"footer-one__pattern",children:(0,i.jsx)("img",{src:"/assets/images/pattern/footer-v1-pattern.png",alt:"#"})}),(0,i.jsx)("div",{className:"footer-four__top",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"footer-one--two__cta",children:[(0,i.jsx)("div",{className:"shape1",children:(0,i.jsx)("img",{className:"float-bob-x3",src:"/assets/images/shapes/footer-v2-shape2.png",alt:""})}),(0,i.jsxs)("div",{className:"footer-one--two__cta-inner",children:[(0,i.jsx)("div",{className:"text-box",children:(0,i.jsxs)("h2",{children:[" ",s("title")," "]})}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)(o(),{className:"thm-btn",href:"tel:".concat(e("phone_no")),children:[(0,i.jsx)("i",{className:"icon-phone",style:{paddingRight:"8px"}}),s("btn_text"),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]})})]})]})})}),(0,i.jsx)("div",{className:"footer-one__bottom",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"footer-one__bottom-inner",style:{border:"none"},children:[(0,i.jsx)("div",{className:"footer-one__bottom-text",children:(0,i.jsxs)("p",{children:["\xa9 ",s("copy_right")," ",(0,i.jsxs)(o(),{href:"/",target:"_blank",children:[" ",s("company")," "]})," ",s("all_right")]})}),(0,i.jsx)("div",{className:"footer-one__social-links",children:(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"https://www.facebook.com/Sakwwth",target:"_blank",children:(0,i.jsx)("span",{className:"fab fa-facebook-f"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"https://www.youtube.com/@sakwoodworks",target:"_blank",children:(0,i.jsx)("span",{className:"fab fa-youtube"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"https://www.tiktok.com/@sakwoodworks",target:"_blank",children:(0,i.jsx)("span",{className:"fab fa-tiktok"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"https://www.instagram.com/sakwoodworks",target:"_blank",children:(0,i.jsx)("span",{className:"fab fa-instagram"})})}),(0,i.jsx)("li",{children:(0,i.jsx)(o(),{href:"https://lin.ee/3sa8cDh",target:"_blank",children:(0,i.jsx)("span",{className:"fab fa-line"})})})]})})]})})})]})})}function C(s){let{headerStyle:e,footerStyle:r,headTitle:o,breadcrumbTitle:m,children:x,wrapperCls:j}=s,[N,v]=(0,l.useState)(0),[p,b]=(0,l.useState)(!1),f=()=>{b(!p),p?document.body.classList.remove("mobile-menu-visible"):document.body.classList.add("mobile-menu-visible")},[C,U]=(0,l.useState)(!1),F=()=>U(!C),[L,I]=(0,l.useState)(!1),A=()=>I(!L);return(0,l.useEffect)(()=>{try{{let{WOW:s}=a(1606);window.wow=new s({live:!1}),window.wow.init()}}catch(s){console.warn("WOW.js initialization failed:",s)}let s=()=>{let s=window.scrollY>100;s!==N&&v(s)};return document.addEventListener("scroll",s),()=>{document.removeEventListener("scroll",s)}},[N]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c,{}),(0,i.jsxs)("div",{className:"page-wrapper ".concat(j||""),id:"#top",children:[!e&&(0,i.jsx)(_,{scroll:N,isMobileMenu:p,handleMobileMenu:f,handlePopup:F,isSidebar:L,handleSidebar:A}),1==e?(0,i.jsx)(_,{scroll:N,isMobileMenu:p,handleMobileMenu:f,handlePopup:F,isSidebar:L,handleSidebar:A}):null,2==e?(0,i.jsx)(u,{scroll:N,isMobileMenu:p,handleMobileMenu:f,handlePopup:F,isSidebar:L,handleSidebar:A}):null,3==e?(0,i.jsx)(g,{scroll:N,isMobileMenu:p,handleMobileMenu:f,handlePopup:F,isSidebar:L,handleSidebar:A}):null,4==e?(0,i.jsx)(w,{scroll:N,isMobileMenu:p,handleMobileMenu:f,handlePopup:F,isSidebar:L,handleSidebar:A}):null,(0,i.jsx)(h,{isSidebar:L,handleSidebar:A}),(0,i.jsx)(t,{isPopup:C,handlePopup:F}),m&&(0,i.jsx)(d,{breadcrumbTitle:m}),x,!r&&(0,i.jsx)(k,{}),1==r?(0,i.jsx)(k,{}):null,2==r?(0,i.jsx)(y,{}):null,3==r?(0,i.jsx)(S,{}):null,4==r?(0,i.jsx)(M,{}):null]}),(0,i.jsx)(n,{scroll:N})]})}}}]);
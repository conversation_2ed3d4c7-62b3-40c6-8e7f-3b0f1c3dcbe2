(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{586:(e,s,a)=>{"use strict";a.d(s,{default:()=>i});var n=a(5155),o=a(2115),t=a(6874),l=a.n(t),r=a(7652);function i(){let[e,s]=(0,o.useState)(!1),a=(0,r.c3)("ConsentCookies");return((0,o.useEffect)(()=>{localStorage.getItem("cookies-consent")||s(!0)},[]),e)?(0,n.jsx)("div",{className:"cookies-consent2",children:(0,n.jsxs)("div",{className:"cookies-consent__message",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("span",{children:a("message")})," ",(0,n.jsx)("span",{children:a("moreInfo")})," ",(0,n.jsx)(l(),{href:"/privacy-policy",target:"_blank",rel:"noopener noreferrer",children:(0,n.jsx)("span",{children:a("privacyPolicy")})}),(0,n.jsx)("span",{children:a("and")}),(0,n.jsx)(l(),{href:"/cookies-policy",target:"_blank",rel:"noopener noreferrer",children:(0,n.jsx)("span",{children:a("cookiePolicy")})}),(0,n.jsx)("span",{children:a("period")})]}),(0,n.jsx)("button",{onClick:()=>{localStorage.setItem("cookies-consent","true"),s(!1)},className:"cookies-consent__button cookies-consent__button--accept",children:(0,n.jsx)("span",{children:a("accept")})})]})}):null}a(5528)},2252:()=>{},2428:()=>{},2638:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});var n=a(5155),o=a(2115),t=a(6874),l=a.n(t);function r(){let[e,s]=(0,o.useState)(!1);return(0,n.jsxs)("div",{className:"chatContainer",children:[(0,n.jsx)("button",{onClick:()=>{s(!e)},className:"chatButton",children:e?(0,n.jsx)("i",{className:"fas fa-times close"}):(0,n.jsx)("i",{className:"fa fa-comments"})}),e&&(0,n.jsxs)("div",{className:"iconConatiner",children:[(0,n.jsx)(l(),{className:"fab fa-facebook-f msgIcon",href:"https://m.me/207440262444547",target:"_blank"}),(0,n.jsx)(l(),{className:"fab fa-line lineIcon",href:"https://lin.ee/UZ46BIS",target:"_blank"})]})]})}a(8484)},2948:()=>{},4987:()=>{},5528:()=>{},6096:(e,s,a)=>{"use strict";a.d(s,{default:()=>t});var n=a(2550),o=a(5155);function t(e){let{locale:s,...a}=e;if(!s)throw Error(void 0);return(0,o.jsx)(n.Dk,{locale:s,...a})}},6886:e=>{e.exports={style:{fontFamily:"'Noto Sans', 'Noto Sans Fallback'",fontStyle:"normal"},className:"__className_9ba018",variable:"__variable_9ba018"}},6970:()=>{},8416:(e,s,a)=>{Promise.resolve().then(a.bind(a,586)),Promise.resolve().then(a.bind(a,2638)),Promise.resolve().then(a.bind(a,6096)),Promise.resolve().then(a.t.bind(a,2948,23)),Promise.resolve().then(a.t.bind(a,4987,23)),Promise.resolve().then(a.t.bind(a,2252,23)),Promise.resolve().then(a.t.bind(a,6970,23)),Promise.resolve().then(a.t.bind(a,2428,23)),Promise.resolve().then(a.t.bind(a,6886,23)),Promise.resolve().then(a.t.bind(a,9987,23)),Promise.resolve().then(a.t.bind(a,9106,23))},8484:()=>{},9106:e=>{e.exports={style:{fontFamily:"'Noto Sans SC', 'Noto Sans SC Fallback'",fontStyle:"normal"},className:"__className_a19ac1",variable:"__variable_a19ac1"}},9987:e=>{e.exports={style:{fontFamily:"'Noto Sans Thai', 'Noto Sans Thai Fallback'",fontStyle:"normal"},className:"__className_a060d5",variable:"__variable_a060d5"}}},e=>{e.O(0,[673,402,781,598,764,335,947,192,648,570,269,46,839,665,441,964,358],()=>e(e.s=8416)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{1222:(s,e,a)=>{"use strict";a.d(e,{default:()=>v});var i=a(5155),n=a(2115),c=a(7269),l=a(7677);a(2252),a(9408);var r=a(7652),d=a(6874),t=a.n(d);function o(){let s=(0,r.c3)("BranchData"),[e,a]=(0,n.useState)(!1);return(0,i.jsx)("div",{children:(0,i.jsx)("div",{className:"pricing-two__single",children:(0,i.jsxs)("div",{className:"pricing-two__single-inner",children:[(0,i.jsxs)("div",{className:"table-header",children:[(0,i.jsx)("div",{className:"img-box",children:(0,i.jsx)("img",{src:"assets/images/branch/store_1.png",alt:""})}),(0,i.jsx)("div",{className:"title-box text-center",children:(0,i.jsx)("h2",{children:s("branch_1")})})]}),(0,i.jsx)("div",{className:"table-content",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-map"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:s("add_1")})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-phone-alt"})}),(0,i.jsx)("div",{className:"text-box",children:e?(0,i.jsx)(t(),{href:"tel:".concat(s("phone_1")),children:s("phone_1")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{a(!e)},children:[s("phone_1").slice(0,7)," ****"]})})]})]})})]})})})}function x(){let s=(0,r.c3)("BranchData"),[e,a]=(0,n.useState)(!1);return(0,i.jsx)("div",{children:(0,i.jsx)("div",{className:"pricing-one__single",children:(0,i.jsxs)("div",{className:"pricing-one__single-inner",children:[(0,i.jsxs)("div",{className:"table-header",children:[(0,i.jsx)("div",{className:"img-box",children:(0,i.jsx)("img",{src:"assets/images/branch/store_2.png",alt:""})}),(0,i.jsx)("div",{className:"title-box text-center",children:(0,i.jsx)("h2",{children:s("branch_2")})})]}),(0,i.jsx)("div",{className:"table-content",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-map"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:s("add_2")})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-phone-alt"})}),(0,i.jsx)("div",{className:"text-box",children:e?(0,i.jsx)(t(),{href:"tel:".concat(s("phone_2")),children:s("phone_2")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{a(!e)},children:[s("phone_2").slice(0,7)," ****"]})})]})]})})]})})})}function h(){let s=(0,r.c3)("BranchData"),[e,a]=(0,n.useState)(!1),[c,l]=(0,n.useState)(!1);return(0,i.jsx)("div",{children:(0,i.jsx)("div",{className:"pricing-two__single",children:(0,i.jsxs)("div",{className:"pricing-two__single-inner",children:[(0,i.jsxs)("div",{className:"table-header",children:[(0,i.jsx)("div",{className:"img-box",children:(0,i.jsx)("img",{src:"assets/images/branch/store_3.png",alt:""})}),(0,i.jsx)("div",{className:"title-box text-center",children:(0,i.jsx)("h2",{children:s("branch_3")})})]}),(0,i.jsx)("div",{className:"table-content",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-map"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:s("add_3")})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-phone-alt"})}),(0,i.jsxs)("div",{className:"text-box",children:[e?(0,i.jsx)(t(),{href:"tel:".concat(s("phone_3")),children:s("phone_3")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{a(!e)},children:[s("phone_3").slice(0,7)," ****"]}),","," ",c?(0,i.jsx)(t(),{href:"tel:".concat(s("mobile_3")),children:s("mobile_3")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{l(!c)},children:[s("mobile_3").slice(0,7)," ****"]})]})]})]})})]})})})}function m(){let s=(0,r.c3)("BranchData"),[e,a]=(0,n.useState)(!1),[c,l]=(0,n.useState)(!1);return(0,i.jsx)("div",{children:(0,i.jsx)("div",{className:"pricing-one__single",children:(0,i.jsxs)("div",{className:"pricing-one__single-inner",children:[(0,i.jsxs)("div",{className:"table-header",children:[(0,i.jsx)("div",{className:"img-box",children:(0,i.jsx)("img",{src:"assets/images/branch/store_4.png",alt:""})}),(0,i.jsx)("div",{className:"title-box text-center",children:(0,i.jsx)("h2",{children:s("branch_4")})})]}),(0,i.jsx)("div",{className:"table-content",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-map"})}),(0,i.jsx)("div",{className:"text-box",children:(0,i.jsx)("p",{children:s("add_4")})})]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fa fa-phone-alt"})}),(0,i.jsxs)("div",{className:"text-box",children:[e?(0,i.jsx)(t(),{href:"tel:".concat(s("phone_4")),children:s("phone_4")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{a(!e)},children:[s("phone_4").slice(0,7)," ****"]}),","," ",c?(0,i.jsx)(t(),{href:"tel:".concat(s("mobile_4")),children:s("mobile_4")}):(0,i.jsxs)("a",{className:"btn-box",style:{cursor:"pointer"},onClick:()=>{l(!c)},children:[s("mobile_4").slice(0,7)," ****"]})]})]})]})})]})})})}let j={modules:[c.Ij,c.Vx],slidesPerView:3,spaceBetween:30,loop:!0,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},breakpoints:{320:{slidesPerView:1},575:{slidesPerView:1},767:{slidesPerView:2},991:{slidesPerView:2},1199:{slidesPerView:2},1350:{slidesPerView:2}}};function v(){let s=(0,r.c3)("BranchArea");return(0,r.c3)("BranchData"),(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"branch-one",id:"branch",children:[(0,i.jsx)("div",{className:"pricing-one__pattern",style:{backgroundImage:"url(assets/images/pattern/pricing-v1-pattern.png)"}}),(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsx)("div",{className:"text tg-element-title",children:(0,i.jsx)("h4",{children:s("title")})}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[s("sub_title1")," ",(0,i.jsx)("br",{})," ",s("sub_title2")," ",(0,i.jsx)("span",{children:s("sub_title3")})]})]}),(0,i.jsx)("div",{className:"row justify-content-center",children:(0,i.jsxs)("div",{className:"col-xl-9 col-lg-9 col-md-12 col-sm-8",children:[(0,i.jsxs)(l.RC,{...j,className:"swiper swiper-initialized swiper-horizontal service-one__carousel owl-carousel owl-theme owl-dot-style1 swiper-backface-hidden",children:[(0,i.jsx)(l.qr,{style:{cursor:"pointer"},children:(0,i.jsx)("div",{className:"wow fadeInUp","data-wow-delay":".3s",children:(0,i.jsx)(o,{})})}),(0,i.jsx)(l.qr,{style:{cursor:"pointer"},children:(0,i.jsx)("div",{className:"wow fadeInDown","data-wow-delay":".3s",children:(0,i.jsx)(x,{})})}),(0,i.jsx)(l.qr,{style:{cursor:"pointer"},children:(0,i.jsx)("div",{className:"wow fadeInUp","data-wow-delay":".3s",children:(0,i.jsx)(h,{})})}),(0,i.jsx)(l.qr,{style:{cursor:"pointer"},children:(0,i.jsx)("div",{className:"wow fadeInDown","data-wow-delay":".3s",children:(0,i.jsx)(m,{})})})]}),(0,i.jsx)("div",{className:"swiper-button-prev"}),(0,i.jsx)("div",{className:"swiper-button-next"})]})})]})]})})}},1421:(s,e,a)=>{"use strict";a.d(e,{default:()=>d});var i=a(5155);a(6874);var n=a(7269),c=a(7677),l=a(7652);let r={modules:[n.Ij,n.dK,n.Vx],slidesPerView:1,spaceBetween:0,autoplay:{delay:8e3,disableOnInteraction:!1},loop:!0,navigation:{nextEl:".h1n",prevEl:".h1p"},pagination:{el:".swiper-pagination",clickable:!0}};function d(){let s=(0,l.c3)("HeroTitle"),e=(0,l.c3)("HeroSlider");return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("section",{className:"slider-one",children:(0,i.jsx)("div",{className:"",children:(0,i.jsxs)(c.RC,{...r,className:"slider-one__carousel owl-carousel owl-theme",children:[(0,i.jsx)(c.qr,{children:(0,i.jsxs)("div",{className:"slider-one__single",children:[(0,i.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner1.png)"}}),(0,i.jsx)("div",{className:"shape1"}),(0,i.jsx)("div",{className:"shape2"}),(0,i.jsx)("div",{className:"shape3"}),(0,i.jsx)("div",{className:"shape4"}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"slider-one__single-inner",children:(0,i.jsxs)("div",{className:"slider-one__single-content",children:[(0,i.jsxs)("div",{className:"tagline",children:[(0,i.jsx)("div",{className:"round"}),(0,i.jsx)("div",{className:"text",children:(0,i.jsx)("span",{children:s("title1")})})]}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h2",{children:e("slide1")})})]})})})]})}),(0,i.jsx)(c.qr,{children:(0,i.jsxs)("div",{className:"slider-one__single",children:[(0,i.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner2.png)"}}),(0,i.jsx)("div",{className:"shape1"}),(0,i.jsx)("div",{className:"shape2"}),(0,i.jsx)("div",{className:"shape3"}),(0,i.jsx)("div",{className:"shape4"}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"slider-one__single-inner",children:(0,i.jsxs)("div",{className:"slider-one__single-content",children:[(0,i.jsxs)("div",{className:"tagline",children:[(0,i.jsx)("div",{className:"round"}),(0,i.jsx)("div",{className:"text",children:(0,i.jsx)("span",{children:s("title2")})})]}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h2",{children:e("slide2")})})]})})})]})}),(0,i.jsx)(c.qr,{children:(0,i.jsxs)("div",{className:"slider-one__single",children:[(0,i.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner3.png)"}}),(0,i.jsx)("div",{className:"shape1"}),(0,i.jsx)("div",{className:"shape2"}),(0,i.jsx)("div",{className:"shape3"}),(0,i.jsx)("div",{className:"shape4"}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"slider-one__single-inner",children:(0,i.jsxs)("div",{className:"slider-one__single-content",children:[(0,i.jsxs)("div",{className:"tagline",children:[(0,i.jsx)("div",{className:"round"}),(0,i.jsx)("div",{className:"text",children:(0,i.jsx)("span",{children:s("title3")})})]}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h2",{children:e("slide3")})})]})})})]})}),(0,i.jsx)(c.qr,{children:(0,i.jsxs)("div",{className:"slider-one__single",children:[(0,i.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner4.png)"}}),(0,i.jsx)("div",{className:"shape1"}),(0,i.jsx)("div",{className:"shape2"}),(0,i.jsx)("div",{className:"shape3"}),(0,i.jsx)("div",{className:"shape4"}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"slider-one__single-inner",children:(0,i.jsxs)("div",{className:"slider-one__single-content",children:[(0,i.jsxs)("div",{className:"tagline",children:[(0,i.jsx)("div",{className:"round"}),(0,i.jsx)("div",{className:"text",children:(0,i.jsx)("span",{children:s("title4")})})]}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h2",{children:e("slide4")})})]})})})]})}),(0,i.jsx)(c.qr,{children:(0,i.jsxs)("div",{className:"slider-one__single",children:[(0,i.jsx)("div",{className:"slider-one__single-bg",style:{backgroundImage:"url(assets/images/banner/banner5.png)"}}),(0,i.jsx)("div",{className:"shape1"}),(0,i.jsx)("div",{className:"shape2"}),(0,i.jsx)("div",{className:"shape3"}),(0,i.jsx)("div",{className:"shape4"}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"slider-one__single-inner",children:(0,i.jsxs)("div",{className:"slider-one__single-content",children:[(0,i.jsxs)("div",{className:"tagline",children:[(0,i.jsx)("div",{className:"round"}),(0,i.jsx)("div",{className:"text",children:(0,i.jsx)("span",{children:s("title5")})})]}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h2",{children:e("slide5")})})]})})})]})})]})})})})}},3784:(s,e,a)=>{"use strict";a.d(e,{default:()=>t});var i=a(5155),n=a(6874),c=a.n(n),l=a(772),r=a(7652),d=a(4143);function t(){let s=(0,r.c3)("AboutArea"),e=(0,r.c3)("HeaderBtn");return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("section",{className:"about-one",id:"about",children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-7",children:(0,i.jsxs)("div",{className:"about-one__content",children:[(0,i.jsxs)("div",{className:"sec-title tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsx)("div",{className:"text tg-element-title",children:(0,i.jsx)("h4",{children:s("title")})}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[s("sub_title1")," ",(0,i.jsxs)("span",{children:[" ",s("sub_title2")," "]}),(0,i.jsxs)("span",{children:[" ",s("sub_title3")]})]})]}),(0,i.jsx)("div",{className:"about-one__content-text1",children:(0,i.jsx)("p",{children:s("sm_des")})}),(0,i.jsx)("div",{className:"about-one__content-text2",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-6 col-lg-6 col-md-6",children:(0,i.jsxs)("div",{className:"about-one__content-text2-single",children:[(0,i.jsxs)("div",{className:"about-one__content-text2-single-top",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{children:(0,i.jsx)("img",{style:{width:"32px"},src:"assets/images/about/Environment.svg",alt:""})})}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h3",{children:s("card1_title")})})]}),(0,i.jsx)("p",{children:s("card1_des")})]})}),(0,i.jsx)("div",{className:"col-xl-6 col-lg-6 col-md-6",children:(0,i.jsxs)("div",{className:"about-one__content-text2-single",children:[(0,i.jsxs)("div",{className:"about-one__content-text2-single-top",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{children:(0,i.jsx)("img",{style:{width:"32px"},src:"assets/images/about/Carving.svg",alt:""})})}),(0,i.jsx)("div",{className:"title-box",children:(0,i.jsx)("h3",{children:s("card2_title")})})]}),(0,i.jsx)("p",{children:s("card2_des")})]})})]})}),(0,i.jsx)("div",{className:"about-one__content-bottom",children:(0,i.jsxs)("div",{className:"contact-box",children:[(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone2"})}),(0,i.jsxs)("div",{className:"text-box",children:[(0,i.jsx)("p",{children:e("phone_des")}),(0,i.jsx)("h4",{children:(0,i.jsx)(d.A,{})})]})]})})]})}),(0,i.jsx)("div",{className:"about-one__img-box col-xl-5 ",children:(0,i.jsxs)("div",{className:"about-one__img",children:[(0,i.jsx)("div",{className:"shape2 float-bob-y",children:(0,i.jsx)("img",{src:"assets/images/shapes/about-v1-shape2.png",alt:""})}),(0,i.jsx)("div",{className:"about-one__img1 reveal",children:(0,i.jsx)("img",{src:"assets/images/about/about-v1-img3.png",alt:""})}),(0,i.jsxs)("div",{className:"about-one__img2",children:[(0,i.jsx)("div",{className:"about-one__img2-inner reveal",children:(0,i.jsx)("img",{src:"assets/images/about/about-v4-img2.png",alt:""})}),(0,i.jsx)("div",{className:"about-one__circle-text",children:(0,i.jsxs)("div",{className:"about-one__round-text-box",children:[(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("div",{className:"about-one__curved-circle rotate-me",children:(0,i.jsx)(l.A,{width:"150",height:"150",cx:"75",cy:"75",rx:"55",ry:"55",startOffset:"0",reversed:!0,text:"SAK WOODWORKS SINCE 2016",textProps:{style:{fontSize:"14"}},textPathProps:{fill:"#ffffff"},tspanProps:null,ellipseProps:null,svgProps:null})})}),(0,i.jsx)("div",{className:"overlay-icon-box",children:(0,i.jsx)(c(),{href:"#",children:(0,i.jsx)("img",{src:"assets/images/icon/symbol.svg",style:{width:"64px"}})})})]})})]})]})})]})})})})}},4666:(s,e,a)=>{"use strict";a.d(e,{default:()=>d});var i=a(5155);a(6874);var n=a(7269),c=a(7677),l=a(7652);let r={modules:[n.Ij,n.dK,n.Vx],slidesPerView:3,spaceBetween:30,autoplay:{delay:6e3,disableOnInteraction:!1},loop:!0,navigation:{nextEl:".h1n",prevEl:".h1p"},pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{320:{slidesPerView:1},575:{slidesPerView:1},767:{slidesPerView:2},991:{slidesPerView:2},1199:{slidesPerView:3},1350:{slidesPerView:3}}};function d(){let s=(0,l.c3)("ServicesArea"),e=(0,l.c3)("ServicesCard");return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"service-one",id:"service",children:[(0,i.jsx)("div",{className:"service-one__pattern",style:{backgroundImage:"url(assets/images/pattern/service-v1-pattern.jpg)"}}),(0,i.jsxs)("div",{className:"container",children:[(0,i.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsxs)("div",{className:"text tg-element-title",children:[(0,i.jsx)("h4",{children:s("title")}),""]}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[s("sub_title1"),(0,i.jsx)("br",{}),(0,i.jsx)("span",{children:s("sub_title2")})," ",s("sub_title3")]})]}),(0,i.jsx)("div",{className:"row",children:(0,i.jsx)("div",{className:"",children:(0,i.jsxs)(c.RC,{...r,className:"service-one__carousel owl-carousel owl-theme owl-dot-style1",children:[(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-Delivery-img.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service1.title")}),(0,i.jsx)("p",{children:e("service1.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsxs)("span",{children:[" ",(0,i.jsx)("img",{src:"assets/images/services/Delivery.svg"})]})})]})}),(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-Packing-img2.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service2.title")}),(0,i.jsx)("p",{children:e("service2.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsxs)("span",{children:[" ",(0,i.jsx)("img",{src:"assets/images/services/Packing.svg"})]})})]})}),(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-CCA-img2.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service3.title")}),(0,i.jsx)("p",{children:e("service3.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsxs)("span",{children:[" ",(0,i.jsx)("img",{src:"assets/images/services/CCA.svg"})]})})]})}),(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-Wholesale-img4.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service4.title")}),(0,i.jsx)("p",{children:e("service4.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsxs)("span",{children:[" ",(0,i.jsx)("img",{src:"assets/images/services/Wholesale-service.svg"})]})})]})}),(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-Cutting-img3.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service5.title")}),(0,i.jsx)("p",{children:e("service5.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsxs)("span",{children:[" ",(0,i.jsx)("img",{src:"assets/images/services/Cutting.svg"})]})})]})}),(0,i.jsx)(c.qr,{style:{cursor:"pointer"},children:(0,i.jsxs)("div",{className:"service-one__single",children:[(0,i.jsxs)("div",{className:"service-one__single-inner",children:[(0,i.jsx)("div",{className:"service-one__single-img",children:(0,i.jsx)("img",{src:"assets/images/services/service-Get Advice-img3.png",alt:"#"})}),(0,i.jsxs)("div",{className:"service-one__single-content",children:[(0,i.jsx)("h2",{children:e("service6.title")}),(0,i.jsx)("p",{children:e("service6.sm_des")}),(0,i.jsx)("div",{className:"btn-box",children:(0,i.jsxs)("a",{children:[(0,i.jsx)("span",{className:"icon-right-arrow",children:" "}),e("swipe")," ",(0,i.jsx)("span",{className:"icon-right-arrow1"})]})})]})]}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{children:(0,i.jsx)("img",{src:"assets/images/services/Advice.svg"})})})]})})]})})})]})]})})}},5610:(s,e,a)=>{"use strict";a.d(e,{default:()=>x});var i=a(5155),n=a(2115),c=a(7652),l=a(6209),r=a(4178);function d(){let s=(0,c.c3)("Conifer"),[e,a]=(0,n.useState)({status:!1,key:1}),d=s=>{e.key===s?a({status:!1}):a({status:!0,key:s})},[t,o]=(0,n.useState)(!1),x=[{src:"assets/images/product/".concat(s("images.img1"))},{src:"assets/images/product/".concat(s("images.img2"))},{src:"assets/images/product/".concat(s("images.img3"))},{src:"assets/images/product/".concat(s("images.img4"))},{src:"assets/images/product/".concat(s("images.img5"))},{src:"assets/images/product/".concat(s("images.img6"))},{src:"assets/images/product/".concat(s("images.img7"))},{src:"assets/images/product/".concat(s("images.img8"))},{src:"assets/images/product/".concat(s("images.img9"))},{src:"assets/images/product/".concat(s("images.img10"))},{src:"assets/images/product/".concat(s("images.img11"))},{src:"assets/images/product/".concat(s("images.img12"))}],h=s=>{o(!0)};return(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-6 d-flex justify-content-center",style:{padding:0},children:(0,i.jsx)("div",{className:"testimonial-one__content",children:(0,i.jsxs)("div",{className:"faq-one__content-faq conifer-faq",children:[(0,i.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,i.jsxs)("span",{children:[" ",s("title1")," "]})," ",(0,i.jsxs)("span",{children:[" ",s("title2")," "]})," ",s("title3")]})}),(0,i.jsxs)("div",{className:"accrodion-grp faq-one-accrodion","data-grp-name":"faq-one-accrodion-1",children:[(0,i.jsxs)("div",{className:1==e.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq1.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq1.ans")})})})]}),(0,i.jsxs)("div",{className:2==e.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq2.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq2.ans")})})})]}),(0,i.jsxs)("div",{className:3==e.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq3.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq3.ans")})})})]}),(0,i.jsxs)("div",{className:4==e.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq4.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq4.ans")})})})]}),(0,i.jsxs)("div",{className:5==e.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq5.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq5.ans")})})})]})]})]})})}),(0,i.jsx)("div",{className:"col-xl-6  d-flex justify-content-center",children:(0,i.jsx)("div",{className:"product-one__img",children:(0,i.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,i.jsx)("button",{className:"conifer-btn",onClick:()=>h(0),children:(0,i.jsxs)("a",{children:["Click to View ",(0,i.jsx)("i",{className:"fas fa-expand"})]})}),(0,i.jsx)("img",{onClick:()=>h(0),style:{cursor:"pointer"},src:x[0].src,alt:"Nortern Conifer Wood"}),(0,i.jsx)(l.Ay,{open:t,close:()=>o(!1),slides:x,plugins:[r.A],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function t(){let s=(0,c.c3)("Bamboo"),[e,a]=(0,n.useState)({status:!1,key:1}),d=s=>{e.key===s?a({status:!1}):a({status:!0,key:s})},[t,o]=(0,n.useState)(!1),x=[{src:"assets/images/product/".concat(s("images.img1"))},{src:"assets/images/product/".concat(s("images.img2"))},{src:"assets/images/product/".concat(s("images.img3"))},{src:"assets/images/product/".concat(s("images.img4"))}],h=s=>{o(!0)};return(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-6",style:{padding:0},children:(0,i.jsx)("div",{className:"testimonial-one__content",children:(0,i.jsxs)("div",{className:"faq-one__content-faq bamboo-faq",children:[(0,i.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,i.jsx)("span",{style:{color:"#445539"},children:s("title1")})," ",s("title2"),s("title3")]})}),(0,i.jsxs)("div",{className:"accrodion-grp bamboo-one-accrodion","data-grp-name":"bamboo-one-accrodion-1",children:[(0,i.jsxs)("div",{className:1==e.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq1.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq1.ans")})})})]}),(0,i.jsxs)("div",{className:2==e.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq2.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq2.ans")})})})]}),(0,i.jsxs)("div",{className:3==e.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq3.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq3.ans")})})})]}),(0,i.jsxs)("div",{className:4==e.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq4.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq4.ans")})})})]}),(0,i.jsxs)("div",{className:5==e.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq5.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq5.ans")})})})]})]})]})})}),(0,i.jsx)("div",{className:"col-xl-6",children:(0,i.jsx)("div",{className:"product-one__img",children:(0,i.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,i.jsx)("button",{className:"bamboo-btn",onClick:()=>h(0),children:(0,i.jsxs)("a",{children:["Click to View ",(0,i.jsx)("i",{className:"fas fa-expand"})]})}),(0,i.jsx)("img",{onClick:()=>h(0),style:{cursor:"pointer"},src:x[0].src,alt:"Nortern Conifer Wood"}),(0,i.jsx)(l.Ay,{open:t,close:()=>o(!1),slides:x,plugins:[r.A],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function o(){let s=(0,c.c3)("Teak"),[e,a]=(0,n.useState)({status:!1,key:1}),d=s=>{e.key===s?a({status:!1}):a({status:!0,key:s})},[t,o]=(0,n.useState)(!1),x=[{src:"assets/images/product/".concat(s("images.img1"))},{src:"assets/images/product/".concat(s("images.img2"))},{src:"assets/images/product/".concat(s("images.img3"))},{src:"assets/images/product/".concat(s("images.img4"))},{src:"assets/images/product/".concat(s("images.img5"))},{src:"assets/images/product/".concat(s("images.img6"))},{src:"assets/images/product/".concat(s("images.img7"))},{src:"assets/images/product/".concat(s("images.img8"))},{src:"assets/images/product/".concat(s("images.img9"))},{src:"assets/images/product/".concat(s("images.img10"))},{src:"assets/images/product/".concat(s("images.img11"))},{src:"assets/images/product/".concat(s("images.img12"))}],h=s=>{o(!0)};return(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-6 ",style:{padding:0},children:(0,i.jsx)("div",{className:"testimonial-one__content",children:(0,i.jsxs)("div",{className:"faq-one__content-faq teak-faq",children:[(0,i.jsx)("div",{className:"sec-title tg-heading-subheading animation-style2",children:(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[(0,i.jsx)("span",{style:{color:"#BA9B7F"},children:s("title1")})," ",s("title2")," ",s("title3")]})}),(0,i.jsxs)("div",{className:"accrodion-grp teak-one-accrodion","data-grp-name":"teak-one-accrodion-1",children:[(0,i.jsxs)("div",{className:1==e.key?"accrodion active":"accrodion",onClick:()=>d(1),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq1.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq1.ans")})})})]}),(0,i.jsxs)("div",{className:2==e.key?"accrodion active":"accrodion",onClick:()=>d(2),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq2.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq2.ans")})})})]}),(0,i.jsxs)("div",{className:3==e.key?"accrodion active":"accrodion",onClick:()=>d(3),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq3.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq3.ans")})})})]}),(0,i.jsxs)("div",{className:4==e.key?"accrodion active":"accrodion",onClick:()=>d(4),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq4.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq4.ans")})})})]}),(0,i.jsxs)("div",{className:5==e.key?"accrodion active":"accrodion",onClick:()=>d(5),children:[(0,i.jsx)("div",{className:"accrodion-title",children:(0,i.jsx)("h4",{children:s("faq5.que")})}),(0,i.jsx)("div",{className:"accrodion-content",children:(0,i.jsx)("div",{className:"inner",children:(0,i.jsx)("p",{children:s("faq5.ans")})})})]})]})]})})}),(0,i.jsx)("div",{className:"col-xl-6",children:(0,i.jsx)("div",{className:"product-one__img",children:(0,i.jsxs)("div",{className:"testimonial-one__img1 reveal",children:[(0,i.jsx)("button",{className:"teak-btn",onClick:()=>h(0),children:(0,i.jsxs)("a",{children:["Click to View ",(0,i.jsx)("i",{className:"fas fa-expand"})]})}),(0,i.jsx)("img",{onClick:()=>h(0),style:{cursor:"pointer"},src:x[0].src,alt:"Thai teak"}),(0,i.jsx)(l.Ay,{open:t,close:()=>o(!1),slides:x,plugins:[r.A],styles:{container:{backgroundColor:"rgba(0, 0, 0, 0.65)"}}})]})})})]})})}function x(){let s=(0,c.c3)("ProductArea"),e=(0,c.c3)("ProductNavTab"),[a,l]=(0,n.useState)("conifer"),[r,x]=(0,n.useState)("conifer"),h=s=>{l(s)},m=s=>{x(r===s?null:s)};return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"product-one",id:"product",children:[(0,i.jsx)("div",{className:"testimonial-one__pattern",style:{backgroundImage:"url(assets/images/pattern/products-v2-pattern.png)"}}),(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("div",{className:"row",children:(0,i.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsx)("div",{className:"text tg-element-title",children:(0,i.jsx)("h4",{children:s("title")})}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[s("sub_title1"),(0,i.jsx)("br",{})," ",(0,i.jsx)("span",{children:s("sub_title2")})]})]})}),(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:"row",children:(0,i.jsx)("div",{className:"col-xl-6",children:(0,i.jsx)("div",{className:"product-menu",children:(0,i.jsxs)("ul",{className:"nav nav-tabs nav-fill",children:[(0,i.jsx)("li",{className:"conifer-menu nav-item",children:(0,i.jsx)("button",{className:"nav-link ".concat("conifer"===a?"active":""),onClick:()=>h("conifer"),children:e("conifer")})}),(0,i.jsx)("li",{className:"bamboo-menu nav-item",children:(0,i.jsx)("button",{className:"nav-link ".concat("bamboo"===a?"active":""),onClick:()=>h("bamboo"),children:e("bamboo")})}),(0,i.jsx)("li",{className:"teak-menu nav-item",children:(0,i.jsx)("button",{className:"nav-link ".concat("teak"===a?"active":""),onClick:()=>h("teak"),children:e("teak")})})]})})})})}),(0,i.jsxs)("div",{className:"product-content",children:["conifer"===a&&(0,i.jsx)(d,{}),"bamboo"===a&&(0,i.jsx)(t,{}),"teak"===a&&(0,i.jsx)(o,{})]}),(0,i.jsxs)("div",{className:"accordion",children:[(0,i.jsxs)("div",{className:"accordion-item conifer",children:[(0,i.jsx)("div",{className:"conifer"===r?"accordion-conifer active":"accordion-conifer",onClick:()=>m("conifer"),children:(0,i.jsx)("h5",{children:e("conifer")})}),(0,i.jsx)("div",{className:"accordion-content ".concat("conifer"===r?"active":""),children:(0,i.jsx)(d,{})})]}),(0,i.jsxs)("div",{className:"accordion-item bamboo",children:[(0,i.jsx)("div",{className:"bamboo"===r?"accordion-bamboo active":"accordion-bamboo",onClick:()=>m("bamboo"),children:(0,i.jsx)("h5",{children:e("bamboo")})}),(0,i.jsx)("div",{className:"accordion-content ".concat("bamboo"===r?"active":""),children:(0,i.jsx)(t,{})})]}),(0,i.jsxs)("div",{className:"accordion-item teak",children:[(0,i.jsx)("div",{className:"teak"===r?"accordion-teak active":"accordion-teak",onClick:()=>m("teak"),children:(0,i.jsx)("h5",{children:e("teak")})}),(0,i.jsx)("div",{className:"accordion-content ".concat("teak"===r?"active":""),children:(0,i.jsx)(o,{})})]})]})]})]})]})})}a(8561),a(9778)},5752:(s,e,a)=>{"use strict";a.d(e,{default:()=>o});var i=a(5155),n=a(6874),c=a.n(n),l=a(7269),r=a(7677),d=a(7652);let t={modules:[l.Ij,l.dK,l.Vx],slidesPerView:6,spaceBetween:30,autoplay:{delay:0,disableOnInteraction:!1},speed:4e3,loop:!0,navigation:{nextEl:".srn",prevEl:".srp"},pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{320:{slidesPerView:2},575:{slidesPerView:3},767:{slidesPerView:4},991:{slidesPerView:4},1199:{slidesPerView:5},1350:{slidesPerView:6}}};function o(){let s=(0,d.c3)("BrandArea"),e=(0,d.c3)("BrandData");return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"store-one",id:"store",children:[(0,i.jsx)("div",{className:"store-one__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/store-pattern.png",alt:""})}),(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("div",{className:"row",children:(0,i.jsx)("div",{className:"why-choose-one__content",children:(0,i.jsxs)("div",{className:"sec-title center text-center tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsx)("div",{className:"text tg-element-title",children:(0,i.jsx)("h4",{children:s("title")})}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[s("sub_title1")," ",(0,i.jsx)("br",{})," ",(0,i.jsxs)("span",{children:[" ",s("sub_title2")," "]})," ",s("sub_title3")," ",(0,i.jsxs)("span",{children:[" ",s("sub_title4")," "]})]})]})})}),(0,i.jsx)("div",{className:"row"}),(0,i.jsxs)(r.RC,{...t,className:"brand-one__carousel owl-carousel owl-theme",children:[(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://shopee.co.th/sakwoodworks",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand01.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_1")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.thaiwatsadu.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand04.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_4")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.lazada.co.th/shop/sak-woodworks/",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand02.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_2")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.homepro.co.th/search?searchtype=&q=sak+woodworks",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand03.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_3")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://nocnoc.com/sl/SAK-WoodWorks/267885?area=pdp-sellerProfile-10409699",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand05.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_5")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.bnbhome.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand06.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_6")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"http://th1268030936ctqi.trustpass.alibaba.com",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand07.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_7")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"#",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand08.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_8")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://shopee.co.th/sakwoodworks",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand01.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_1")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.thaiwatsadu.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand04.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_4")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.lazada.co.th/shop/sak-woodworks/",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand02.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_2")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.homepro.co.th/search?searchtype=&q=sak+woodworks",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand03.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_3")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://nocnoc.com/sl/SAK-WoodWorks/267885?area=pdp-sellerProfile-10409699",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand05.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_5")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"https://www.bnbhome.com/th/brand/SAK%20WOODWORKS",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand06.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_6")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"http://th1268030936ctqi.trustpass.alibaba.com",target:"_blank",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand07.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_7")})})]})})})}),(0,i.jsx)(r.qr,{children:(0,i.jsx)("div",{className:"brand-one__single text-center",children:(0,i.jsx)("div",{className:"brand-one__single-inner",children:(0,i.jsxs)(c(),{href:"#",children:[(0,i.jsx)("div",{className:"brand-one__image",children:(0,i.jsx)("img",{src:"assets/images/brand/brand08.svg",alt:""})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{children:e("brand_8")})})]})})})})]})]})]})})}},8770:(s,e,a)=>{Promise.resolve().then(a.bind(a,9238)),Promise.resolve().then(a.bind(a,3784)),Promise.resolve().then(a.bind(a,1421)),Promise.resolve().then(a.bind(a,1222)),Promise.resolve().then(a.bind(a,9092)),Promise.resolve().then(a.bind(a,5610)),Promise.resolve().then(a.bind(a,4666)),Promise.resolve().then(a.bind(a,5752))},9092:(s,e,a)=>{"use strict";a.d(e,{default:()=>d});var i=a(5155),n=a(6874),c=a.n(n),l=a(2115),r=a(7652);function d(){let s=(0,r.c3)("HeaderBtn"),e=(0,r.c3)("ContactArea"),a=(0,r.c3)("InputForm"),[n,d]=(0,l.useState)(""),[t,o]=(0,l.useState)(""),[x,h]=(0,l.useState)(""),[m,j]=(0,l.useState)(""),[v,g]=(0,l.useState)(""),[N,p]=(0,l.useState)(!1),[_,b]=(0,l.useState)(!1),[u,f]=(0,l.useState)(null),[w,k]=(0,l.useState)(null),[y,q]=(0,l.useState)(null),[C,S]=(0,l.useState)(null),[P,V]=(0,l.useState)(null),[A,I]=(0,l.useState)(null),O=async s=>{s.preventDefault(),p(!0),f(null);let e=!1;if(n?k(null):(k("Name is required."),e=!0),t?q(null):(q("Email is required."),e=!0),x?S(null):(S("Phone No. is required."),e=!0),m?V(null):(V("Line ID is required."),e=!0),v?I(null):(I("Message is required."),e=!0),e)return void p(!1);let a=new FormData;a.append("name",n),a.append("email",t),a.append("phone",x),a.append("idline",m),a.append("massege",v);try{let s=await fetch("https://script.google.com/macros/s/AKfycbxvM-rtnmkhW3XpEk1HC9Vfb6cLwv_boDpDT10wImaRh-CUI3hiLWZGla4h0BMwZp9_/exec",{method:"POST",body:a});if(!s.ok){let e=await s.text();throw Error("HTTP error! status: ".concat(s.status,", message: ").concat(e))}b(!0),d(""),o(""),h(""),j(""),g("")}catch(s){console.error("Submission failed! Please try again.",s),f(s.message),b(!1)}finally{p(!1)}};return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("section",{className:"contact-one",id:"contact",children:[(0,i.jsx)("div",{className:"contact-one__pattern",children:(0,i.jsx)("img",{src:"assets/images/pattern/Contact-Us BG-pattern-01.png",alt:""})}),(0,i.jsx)("div",{className:"shape1 float-bob-y",children:(0,i.jsx)("img",{src:"assets/images/shapes/why-choose-v1-shape1.png",alt:""})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsx)("div",{className:"col-xl-6",children:(0,i.jsxs)("div",{className:"contact-one__content",children:[(0,i.jsxs)("div",{className:"sec-title tg-heading-subheading animation-style2",children:[(0,i.jsxs)("div",{className:"sec-title__tagline",children:[(0,i.jsx)("div",{className:"line"}),(0,i.jsx)("div",{className:"text tg-element-title",children:(0,i.jsx)("h4",{children:e("title")})}),(0,i.jsx)("div",{className:"line2"})]}),(0,i.jsxs)("h2",{className:"sec-title__title tg-element-title",children:[e("sub_title1")," ",(0,i.jsxs)("span",{children:[" ",e("sub_title2")," "]})," ",e("sub_title3")," ",(0,i.jsxs)("span",{children:[" ",e("sub_title4")," "]})," ",e("sub_title5")]})]}),(0,i.jsx)("div",{className:"contact-one__title-box",children:(0,i.jsx)("h4",{children:e("header")})}),(0,i.jsx)("div",{className:"contact-one__content-list",children:(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-phone"}),e("sub_header1")]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-phone",style:{opacity:"0"}})," ",(0,i.jsx)("span",{className:"text-box",children:(0,i.jsx)(c(),{href:"tel:".concat(s("phone_no")),children:s("phone_no")})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-envelope-open-text"})," ",e("sub_header2")]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-phone",style:{opacity:"0"}})," ",(0,i.jsx)("span",{className:"text-box",children:(0,i.jsx)(c(),{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,i.jsxs)("li",{children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-share-alt"})," ",e("sub_header3")]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("span",{className:"fa fa-share-alt",style:{opacity:"0"}})," ",(0,i.jsxs)("span",{className:"social-list",style:{paddingLeft:"0.06em"},children:[(0,i.jsx)(c(),{className:"fab fa-facebook-square",href:"https://www.facebook.com/Sakwwth",target:"_blank"}),(0,i.jsx)(c(),{className:"fab fa-youtube",href:"https://www.youtube.com/@sakwoodworks",target:"_blank"}),(0,i.jsx)(c(),{className:"fab fa-tiktok",href:"https://www.tiktok.com/@sakwoodworks",target:"_blank"}),(0,i.jsx)(c(),{className:"fab fa-instagram",href:"https://www.instagram.com/sakwoodworks",target:"_blank"}),(0,i.jsx)(c(),{className:"fab fa-line",href:"https://lin.ee/smwoT3j",target:"_blank"})]})]})]})]})})]})}),(0,i.jsx)("div",{className:"col-xl-6",children:(0,i.jsxs)("div",{className:"contact-one__form-box wow fadeInRight","data-wow-delay":"0ms","data-wow-duration":"1500ms",children:[(0,i.jsxs)("div",{className:"title-box",children:[(0,i.jsxs)("h2",{children:[" ",a("title")," "]}),(0,i.jsxs)("p",{children:[" ",a("desc")," "]})]}),(0,i.jsx)("form",{className:"contact-form-validated why-choose-one__form",onSubmit:O,children:(0,i.jsxs)("div",{className:"row",children:[(0,i.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-3",children:[(0,i.jsxs)("div",{className:"input-box",children:[(0,i.jsx)("input",{type:"text",placeholder:"".concat(a("name")),value:n,onChange:s=>d(s.target.value),required:""}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-user"})})]}),w&&(0,i.jsx)("p",{className:"error-message",children:w})]}),(0,i.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-4",children:[(0,i.jsxs)("div",{className:"input-box",children:[(0,i.jsx)("input",{type:"email",placeholder:"".concat(a("email")),required:"",value:t,onChange:s=>o(s.target.value)}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-email"})})]}),y&&(0,i.jsx)("p",{className:"error-message",children:y})]}),(0,i.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-4",children:[(0,i.jsxs)("div",{className:"input-box",children:[(0,i.jsx)("input",{type:"text",placeholder:"".concat(a("phone")),required:"",value:x,onChange:s=>h(s.target.value)}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"icon-phone2"})})]}),C&&(0,i.jsx)("p",{className:"error-message",children:C})]}),(0,i.jsxs)("div",{className:"col-xl-6 col-lg-6 col-md-6 mb-3",children:[(0,i.jsxs)("div",{className:"input-box",children:[(0,i.jsx)("input",{type:"text",placeholder:"".concat(a("idline")),required:"",value:m,onChange:s=>j(s.target.value)}),(0,i.jsx)("div",{className:"icon",children:(0,i.jsx)("span",{className:"fab fa-line"})})]}),P&&(0,i.jsx)("p",{className:"error-message",children:P})]}),(0,i.jsxs)("div",{className:"col-xl-12 mb-3",children:[(0,i.jsxs)("div",{className:"input-box",children:[(0,i.jsx)("textarea",{placeholder:"".concat(a("massege")),value:v,onChange:s=>g(s.target.value)}),(0,i.jsx)("div",{className:"icon style2",children:(0,i.jsx)("span",{className:"icon-pen"})})]}),A&&(0,i.jsx)("p",{className:"error-message",children:A})]}),(0,i.jsx)("div",{className:"col-xl-12",children:(0,i.jsxs)("div",{className:"why-choose-one__form-btn",children:[(0,i.jsxs)("button",{type:"submit",className:"thm-btn",disabled:N,children:[N?"Submitting...":"".concat(a("submit")),(0,i.jsx)("i",{className:"icon-right-arrow21"}),(0,i.jsx)("span",{className:"hover-btn hover-bx"}),(0,i.jsx)("span",{className:"hover-btn hover-bx2"}),(0,i.jsx)("span",{className:"hover-btn hover-bx3"}),(0,i.jsx)("span",{className:"hover-btn hover-bx4"})]}),_&&(0,i.jsx)("p",{style:{color:"green"},children:a("success")}),u&&(0,i.jsx)("p",{className:"error-message",children:a("error")})]})})]})})]})})]})})]})})}a(4143)},9778:()=>{}},s=>{s.O(0,[269,136,947,665,155,238,441,964,358],()=>s(s.s=8770)),_N_E=s.O()}]);
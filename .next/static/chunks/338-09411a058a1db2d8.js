(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[338],{1606:function(){(function(){var t,n,e,i,s,r=function(t,n){return function(){return t.apply(n,arguments)}},o=[].indexOf||function(t){for(var n=0,e=this.length;n<e;n++)if(n in this&&this[n]===t)return n;return -1};n=function(){function t(){}return t.prototype.extend=function(t,n){var e,i;for(e in n)i=n[e],null==t[e]&&(t[e]=i);return t},t.prototype.isMobile=function(t){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)},t.prototype.createEvent=function(t,n,e,i){var s;return null==n&&(n=!1),null==e&&(e=!1),null==i&&(i=null),null!=document.createEvent?(s=document.createEvent("CustomEvent")).initCustomEvent(t,n,e,i):null!=document.createEventObject?(s=document.createEventObject()).eventType=t:s.eventName=t,s},t.prototype.emitEvent=function(t,n){return null!=t.dispatchEvent?t.dispatchEvent(n):n in(null!=t)?t[n]():"on"+n in(null!=t)?t["on"+n]():void 0},t.prototype.addEvent=function(t,n,e){return null!=t.addEventListener?t.addEventListener(n,e,!1):null!=t.attachEvent?t.attachEvent("on"+n,e):t[n]=e},t.prototype.removeEvent=function(t,n,e){return null!=t.removeEventListener?t.removeEventListener(n,e,!1):null!=t.detachEvent?t.detachEvent("on"+n,e):delete t[n]},t.prototype.innerHeight=function(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight},t}(),e=this.WeakMap||this.MozWeakMap||(e=function(){function t(){this.keys=[],this.values=[]}return t.prototype.get=function(t){var n,e,i,s;for(s=this.keys,n=e=0,i=s.length;e<i;n=++e)if(s[n]===t)return this.values[n]},t.prototype.set=function(t,n){var e,i,s,r;for(r=this.keys,e=i=0,s=r.length;i<s;e=++i)if(r[e]===t){this.values[e]=n;return}return this.keys.push(t),this.values.push(n)},t}()),t=this.MutationObserver||this.WebkitMutationObserver||this.MozMutationObserver||(t=function(){function t(){"undefined"!=typeof console&&null!==console&&console.warn("MutationObserver is not supported by your browser."),"undefined"!=typeof console&&null!==console&&console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content.")}return t.notSupported=!0,t.prototype.observe=function(){},t}()),i=this.getComputedStyle||function(t,n){return this.getPropertyValue=function(n){var e;return"float"===n&&(n="styleFloat"),s.test(n)&&n.replace(s,function(t,n){return n.toUpperCase()}),(null!=(e=t.currentStyle)?e[n]:void 0)||null},this},s=/(\-([a-z]){1})/g,this.WOW=function(){function s(t){null==t&&(t={}),this.scrollCallback=r(this.scrollCallback,this),this.scrollHandler=r(this.scrollHandler,this),this.resetAnimation=r(this.resetAnimation,this),this.start=r(this.start,this),this.scrolled=!0,this.config=this.util().extend(t,this.defaults),null!=t.scrollContainer&&(this.config.scrollContainer=document.querySelector(t.scrollContainer)),this.animationNameCache=new e,this.wowEvent=this.util().createEvent(this.config.boxClass)}return s.prototype.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null},s.prototype.init=function(){var t;return this.element=window.document.documentElement,"interactive"===(t=document.readyState)||"complete"===t?this.start():this.util().addEvent(document,"DOMContentLoaded",this.start),this.finished=[]},s.prototype.start=function(){var n,e,i,s,r;if(this.stopped=!1,this.boxes=(function(){var t,e,i,s;for(t=0,i=this.element.querySelectorAll("."+this.config.boxClass),s=[],e=i.length;t<e;t++)n=i[t],s.push(n);return s}).call(this),this.all=(function(){var t,e,i,s;for(t=0,i=this.boxes,s=[],e=i.length;t<e;t++)n=i[t],s.push(n);return s}).call(this),this.boxes.length)if(this.disabled())this.resetStyle();else for(e=0,i=(s=this.boxes).length;e<i;e++)n=s[e],this.applyStyle(n,!0);if(this.disabled()||(this.util().addEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().addEvent(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live)return new t((r=this,function(t){var n,e,i,s,o;for(n=0,o=[],e=t.length;n<e;n++)s=t[n],o.push((function(){var t,n,e,r;for(t=0,e=s.addedNodes||[],r=[],n=e.length;t<n;t++)i=e[t],r.push(this.doSync(i));return r}).call(r));return o})).observe(document.body,{childList:!0,subtree:!0})},s.prototype.stop=function(){if(this.stopped=!0,this.util().removeEvent(this.config.scrollContainer||window,"scroll",this.scrollHandler),this.util().removeEvent(window,"resize",this.scrollHandler),null!=this.interval)return clearInterval(this.interval)},s.prototype.sync=function(n){if(t.notSupported)return this.doSync(this.element)},s.prototype.doSync=function(t){var n,e,i,s,r;if(null==t&&(t=this.element),1===t.nodeType){for(e=0,s=(t=t.parentNode||t).querySelectorAll("."+this.config.boxClass),r=[],i=s.length;e<i;e++)n=s[e],0>o.call(this.all,n)?(this.boxes.push(n),this.all.push(n),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(n,!0),r.push(this.scrolled=!0)):r.push(void 0);return r}},s.prototype.show=function(t){return this.applyStyle(t),t.className=t.className+" "+this.config.animateClass,null!=this.config.callback&&this.config.callback(t),this.util().emitEvent(t,this.wowEvent),this.util().addEvent(t,"animationend",this.resetAnimation),this.util().addEvent(t,"oanimationend",this.resetAnimation),this.util().addEvent(t,"webkitAnimationEnd",this.resetAnimation),this.util().addEvent(t,"MSAnimationEnd",this.resetAnimation),t},s.prototype.applyStyle=function(t,n){var e,i,s,r;return i=t.getAttribute("data-wow-duration"),e=t.getAttribute("data-wow-delay"),s=t.getAttribute("data-wow-iteration"),this.animate((r=this,function(){return r.customStyle(t,n,i,e,s)}))},s.prototype.animate="requestAnimationFrame"in window?function(t){return window.requestAnimationFrame(t)}:function(t){return t()},s.prototype.resetStyle=function(){var t,n,e,i,s;for(n=0,i=this.boxes,s=[],e=i.length;n<e;n++)t=i[n],s.push(t.style.visibility="visible");return s},s.prototype.resetAnimation=function(t){var n;if(t.type.toLowerCase().indexOf("animationend")>=0)return(n=t.target||t.srcElement).className=n.className.replace(this.config.animateClass,"").trim()},s.prototype.customStyle=function(t,n,e,i,s){return n&&this.cacheAnimationName(t),t.style.visibility=n?"hidden":"visible",e&&this.vendorSet(t.style,{animationDuration:e}),i&&this.vendorSet(t.style,{animationDelay:i}),s&&this.vendorSet(t.style,{animationIterationCount:s}),this.vendorSet(t.style,{animationName:n?"none":this.cachedAnimationName(t)}),t},s.prototype.vendors=["moz","webkit"],s.prototype.vendorSet=function(t,n){var e,i,s,r;for(e in i=[],n)s=n[e],t[""+e]=s,i.push((function(){var n,i,o,l;for(n=0,o=this.vendors,l=[],i=o.length;n<i;n++)r=o[n],l.push(t[""+r+e.charAt(0).toUpperCase()+e.substr(1)]=s);return l}).call(this));return i},s.prototype.vendorCSS=function(t,n){var e,s,r,o,l,c;for(e=0,o=(l=i(t)).getPropertyCSSValue(n),s=(r=this.vendors).length;e<s;e++)c=r[e],o=o||l.getPropertyCSSValue("-"+c+"-"+n);return o},s.prototype.animationName=function(t){var n;try{n=this.vendorCSS(t,"animation-name").cssText}catch(e){n=i(t).getPropertyValue("animation-name")}return"none"===n?"":n},s.prototype.cacheAnimationName=function(t){return this.animationNameCache.set(t,this.animationName(t))},s.prototype.cachedAnimationName=function(t){return this.animationNameCache.get(t)},s.prototype.scrollHandler=function(){return this.scrolled=!0},s.prototype.scrollCallback=function(){var t;if(this.scrolled&&(this.scrolled=!1,this.boxes=(function(){var n,e,i,s;for(n=0,i=this.boxes,s=[],e=i.length;n<e;n++)if(t=i[n]){if(this.isVisible(t)){this.show(t);continue}s.push(t)}return s}).call(this),!(this.boxes.length||this.config.live)))return this.stop()},s.prototype.offsetTop=function(t){for(var n;void 0===t.offsetTop;)t=t.parentNode;for(n=t.offsetTop;t=t.offsetParent;)n+=t.offsetTop;return n},s.prototype.isVisible=function(t){var n,e,i,s,r;return e=t.getAttribute("data-wow-offset")||this.config.offset,s=(r=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset)+Math.min(this.element.clientHeight,this.util().innerHeight())-e,n=(i=this.offsetTop(t))+t.clientHeight,i<=s&&n>=r},s.prototype.util=function(){return null!=this._util?this._util:this._util=new n},s.prototype.disabled=function(){return!this.config.mobile&&this.util().isMobile(navigator.userAgent)},s}()}).call(this)},3066:(t,n,e)=>{"use strict";e.d(n,{A:()=>l});var i=e(5155);e(2115);var s=e(7652),r=e(6874),o=e.n(r);function l(){let t=(0,s.c3)("PrivacyPolicy");return(0,i.jsxs)("div",{className:"privacy-content",children:[(0,i.jsx)("h2",{children:(0,i.jsx)("span",{children:t("title2")})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section7.title")})}),(0,i.jsx)("a",{children:"●"})," ",(0,i.jsx)("span",{children:t("section7.content1")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section8.title")})}),(0,i.jsx)("a",{children:"●"})," ",(0,i.jsx)("span",{children:t("section8.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:["・ ",(0,i.jsx)("span",{children:t("section8.list1")})]}),(0,i.jsxs)("li",{children:["・ ",(0,i.jsx)("span",{children:t("section8.list2")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section8.list3")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section8.list4")})]})]}),(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section9.title")})}),(0,i.jsx)("a",{children:"●"})," ",(0,i.jsx)("span",{children:t("section9.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section9.list1")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section9.list2")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section9.list3")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section9.list4")})]}),(0,i.jsxs)("li",{children:["・",(0,i.jsx)("span",{children:t("section9.list5")})]})]}),(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section10.title")})}),(0,i.jsx)("span",{children:t("section10.content1")})," ",(0,i.jsx)("br",{})]}),(0,i.jsxs)("span",{style:{color:"#325ae1"},children:[(0,i.jsx)("a",{children:t("contact.email")}),t("contact.info1")]})," ",(0,i.jsx)("br",{})," ",(0,i.jsxs)("span",{style:{color:"#325ae1"},children:[(0,i.jsx)("a",{children:t("contact.phone")})," ",(0,i.jsx)(o(),{href:"tel:".concat(t("contact.info2")),children:t("contact.info2")})]})," ",(0,i.jsx)("span",{children:t("contact.hours")})," ",(0,i.jsx)("br",{}),(0,i.jsxs)("span",{children:[(0,i.jsx)("a",{style:{color:"#325ae1"},children:t("contact.address")})," ",t("contact.info3")]})]})}e(3846)},3846:()=>{},4341:(t,n,e)=>{"use strict";e.d(n,{A:()=>l});var i=e(5155);e(2115);var s=e(7652),r=e(6874),o=e.n(r);function l(){let t=(0,s.c3)("PrivacyPolicy");return(0,i.jsxs)("div",{className:"privacy-content",children:[(0,i.jsx)("h2",{children:(0,i.jsx)("span",{children:t("title1")})}),(0,i.jsxs)("span",{children:[(0,i.jsx)("span",{children:t("intro1")})," ",(0,i.jsxs)("a",{children:[t("name")," "]})," ",(0,i.jsx)("span",{children:t("intro2")})," ",(0,i.jsx)("a",{children:t("company")})," ",(0,i.jsx)("span",{children:t("intro3")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("span",{children:t("intro4")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("span",{children:t("intro5")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("span",{children:t("intro6")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section1.title")})}),(0,i.jsx)("a",{children:"1.1."})," ",(0,i.jsx)("span",{children:t("section1.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:"1.2."})," ",(0,i.jsx)("span",{children:t("section1.content2")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section2.title")})}),(0,i.jsx)("a",{children:"2.1."})," ",(0,i.jsx)("span",{children:t("section2.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:["2.1.1 ",(0,i.jsx)("span",{children:t("section2.list1")})]}),(0,i.jsxs)("li",{children:["2.1.2 ",(0,i.jsx)("span",{children:t("section2.list2")})]}),(0,i.jsxs)("li",{children:["2.1.3 ",(0,i.jsx)("span",{children:t("section2.list3")})]}),(0,i.jsxs)("li",{children:["2.1.4 ",(0,i.jsx)("span",{children:t("section2.list4")})]}),(0,i.jsxs)("li",{children:["2.1.5 ",(0,i.jsx)("span",{children:t("section2.list5")})]}),(0,i.jsxs)("li",{children:["2.1.6 ",(0,i.jsx)("span",{children:t("section2.list6")})]}),(0,i.jsxs)("li",{children:["2.1.7 ",(0,i.jsx)("span",{children:t("section2.list7")})]}),(0,i.jsxs)("li",{children:["2.1.8 ",(0,i.jsx)("span",{children:t("section2.list8")})]})]}),(0,i.jsx)("a",{children:"2.2"})," ",(0,i.jsx)("span",{children:t("section2.content2")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:"2.3"})," ",(0,i.jsx)("span",{children:t("section2.content3")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:"2.4"})," ",(0,i.jsx)("span",{children:t("section2.content4")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section3.title")})}),(0,i.jsx)("a",{children:"3.1"})," ",(0,i.jsx)("span",{children:t("section3.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:"3.2"})," ",(0,i.jsx)("span",{children:t("section3.content2")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section4.title")})}),(0,i.jsx)("a",{children:"4.1"})," ",(0,i.jsx)("span",{children:t("section4.content1")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section5.title")})}),(0,i.jsx)("a",{children:"5.1"})," ",(0,i.jsx)("span",{children:t("section5.content1")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section6.title")})}),(0,i.jsx)("a",{children:"6.1"})," ",(0,i.jsx)("span",{children:t("section6.content1")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:t("section6.howWillNotify")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:t("section6.subtitle1")})," ",(0,i.jsx)("span",{children:t("section6.option1")})," ",(0,i.jsx)("br",{}),(0,i.jsx)("a",{children:t("section6.or")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("a",{children:t("section6.subtitle2")})," ",(0,i.jsx)("span",{children:t("section6.option2")})," ",(0,i.jsx)("br",{})," ",(0,i.jsx)("br",{}),(0,i.jsx)("h3",{children:(0,i.jsx)("span",{children:t("section10.title")})}),(0,i.jsx)("span",{children:t("section10.content1")})," ",(0,i.jsx)("br",{})]}),(0,i.jsxs)("span",{style:{color:"#325ae1"},children:[(0,i.jsx)("a",{children:t("contact.email")}),t("contact.info1")]})," ",(0,i.jsx)("br",{})," ",(0,i.jsxs)("span",{style:{color:"#325ae1"},children:[(0,i.jsx)("a",{children:t("contact.phone")})," ",(0,i.jsx)(o(),{href:"tel:".concat(t("contact.info2")),children:t("contact.info2")})]})," ",(0,i.jsx)("span",{children:t("contact.hours")})," ",(0,i.jsx)("br",{}),(0,i.jsxs)("span",{children:[(0,i.jsx)("a",{style:{color:"#325ae1"},children:t("contact.address")})," ",t("contact.info3")]})]})}e(3846)},5695:(t,n,e)=>{"use strict";var i=e(8999);e.o(i,"usePathname")&&e.d(n,{usePathname:function(){return i.usePathname}}),e.o(i,"useRouter")&&e.d(n,{useRouter:function(){return i.useRouter}})},9984:(t,n,e)=>{"use strict";function i(t){return t}e.d(n,{A:()=>i})}}]);
{"version": 3, "file": "no-confusing-void-expression.js", "sourceRoot": "", "sources": ["../../src/rules/no-confusing-void-expression.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAIjC,kCASiB;AACjB,yEAAsE;AAoBtE,kBAAe,IAAA,iBAAU,EAAqB;IAC5C,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,eAAe,EACb,oEAAoE;gBACpE,uCAAuC;YACzC,oBAAoB,EAClB,6EAA6E;gBAC7E,0CAA0C;YAC5C,4BAA4B,EAC1B,6DAA6D;gBAC7D,qDAAqD;YACvD,qBAAqB,EACnB,4DAA4D;gBAC5D,+CAA+C;YACjD,yBAAyB,EACvB,4DAA4D;gBAC5D,uCAAuC;YACzC,6BAA6B,EAC3B,4CAA4C;gBAC5C,qDAAqD;YACvD,uBAAuB,EACrB,kDAAkD;gBAClD,qCAAqC;gBACrC,gDAAgD;YAClD,gBAAgB,EAAE,wCAAwC;SAC3D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,oBAAoB,EAAE;wBACpB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,wFAAwF;qBAC3F;oBACD,kBAAkB,EAAE;wBAClB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,gEAAgE;qBACnE;oBACD,4BAA4B,EAAE;wBAC5B,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,+HAA+H;qBAClI;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,oBAAoB,EAAE,KAAK;YAC3B,kBAAkB,EAAE,KAAK;YACzB,4BAA4B,EAAE,KAAK;SACpC;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,OAAO;YACL,2DAA2D,CACzD,IAGqC;gBAErC,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC1D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACxD,wBAAwB;oBACxB,OAAO;gBACT,CAAC;gBAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;oBAC5B,uCAAuC;oBACvC,OAAO;gBACT,CAAC;gBAED,MAAM,WAAW,GAAG,CAAC,KAAyB,EAAoB,EAAE;oBAClE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClD,MAAM,WAAW,GAAG,QAAQ,QAAQ,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC9C,CAAC,CAAC;gBAEF,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE,CAAC;oBACpE,kCAAkC;oBAElC,IAAI,OAAO,CAAC,4BAA4B,EAAE,CAAC;wBACzC,MAAM,WAAW,GAAG,2BAA2B,CAAC,eAAe,CAAC,CAAC;wBAEjE,IAAI,WAAW,EAAE,CAAC;4BAChB,OAAO;wBACT,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,8BAA8B;4BACzC,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;oBACL,CAAC;oBAED,8BAA8B;oBAC9B,MAAM,aAAa,GAAG,eAAe,CAAC;oBACtC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;gCAC3B,OAAO,IAAI,CAAC;4BACd,CAAC;4BACD,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;4BACrC,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM,gBAAgB,GAAG,KAAK,aAAa,KAAK,CAAC;4BACjD,IAAI,IAAA,sBAAe,EAAC,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gCACnD,MAAM,gBAAgB,GAAG,IAAA,iBAAU,EACjC,OAAO,CAAC,UAAU,CAAC,cAAc,CAC/B,SAAS,EACT,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAC5B,qBAAqB,EACrB,YAAY,CACb,CACF,CAAC;gCACF,MAAM,gBAAgB,GAAG,IAAA,iBAAU,EACjC,OAAO,CAAC,UAAU,CAAC,aAAa,CAC9B,SAAS,EACT,0BAAmB,CACpB,EACD,wBAAiB,CAAC,YAAY,CAC5B,qBAAqB,EACrB,YAAY,CACb,CACF,CAAC;gCACF,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,gBAAgB,CACjB,CAAC;4BACJ,CAAC;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;wBACxD,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBAC5D,0BAA0B;oBAE1B,IAAI,OAAO,CAAC,4BAA4B,EAAE,CAAC;wBACzC,MAAM,YAAY,GAAG,IAAA,6CAAqB,EAAC,eAAe,CAAC,CAAC;wBAE5D,IAAI,YAAY,EAAE,CAAC;4BACjB,MAAM,WAAW,GAAG,2BAA2B,CAAC,YAAY,CAAC,CAAC;4BAE9D,IAAI,WAAW,EAAE,CAAC;gCAChB,OAAO;4BACT,CAAC;wBACH,CAAC;oBACH,CAAC;oBAED,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,+BAA+B;4BAC1C,GAAG,EAAE,WAAW;yBACjB,CAAC,CAAC;oBACL,CAAC;oBAED,IAAI,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC;wBACnC,8BAA8B;wBAC9B,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI;4BACJ,SAAS,EAAE,2BAA2B;4BACtC,GAAG,CAAC,KAAK;gCACP,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;oCAC7B,OAAO,IAAI,CAAC;gCACd,CAAC;gCACD,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC;gCAC7C,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gCAChE,IAAI,iBAAiB,GAAG,GAAG,eAAe,GAAG,CAAC;gCAC9C,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;oCACjC,+CAA+C;oCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;gCAC9C,CAAC;gCACD,OAAO,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;4BAC/D,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;oBAED,mCAAmC;oBACnC,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,uBAAuB;wBAClC,GAAG,CAAC,KAAK;4BACP,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC;4BAC7C,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;4BAChE,IAAI,iBAAiB,GAAG,GAAG,eAAe,WAAW,CAAC;4BACtD,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gCACjC,+CAA+C;gCAC/C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;4BAC9C,CAAC;4BACD,IACE,eAAe,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAC7D,CAAC;gCACD,2CAA2C;gCAC3C,mCAAmC;gCACnC,iBAAiB,GAAG,KAAK,iBAAiB,IAAI,CAAC;4BACjD,CAAC;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;wBAC/D,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,sBAAsB;gBACtB,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,sDAAsD;oBACtD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS,EAAE,yBAAyB;wBACpC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;qBAC/D,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,iBAAiB;iBAC7B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAWF;;;;;;;WAOG;QACH,SAAS,mBAAmB,CAAC,IAAmB;YAC9C,MAAM,MAAM,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,MAAM,EAAE,wBAAiB,CAAC,aAAa,CAAC,CAAC;YACxE,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBACjD,IAAI,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAC1D,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;gBACvD,iCAAiC;gBACjC,uBAAuB;gBACvB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBAChD,MAAM,CAAC,KAAK,KAAK,IAAI,EACrB,CAAC;gBACD,6BAA6B;gBAC7B,mDAAmD;gBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;gBACpD,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,EACzD,CAAC;gBACD,uDAAuD;gBACvD,mDAAmD;gBACnD,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACtD,kCAAkC;gBAClC,2CAA2C;gBAC3C,OAAO,CAAC,oBAAoB,EAC5B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC9C,MAAM,CAAC,QAAQ,KAAK,MAAM;gBAC1B,iCAAiC;gBACjC,2CAA2C;gBAC3C,OAAO,CAAC,kBAAkB,EAC1B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACnD,6BAA6B;gBAC7B,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,+BAA+B;YAC/B,0DAA0D;YAC1D,OAAO,MAAyB,CAAC;QACnC,CAAC;QAED,oFAAoF;QACpF,SAAS,aAAa,CAAC,IAA8B;YACnD,6BAA6B;YAC7B,MAAM,KAAK,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,MAAM,EAAE,wBAAiB,CAAC,aAAa,CAAC,CAAC;YACvE,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBACjD,4CAA4C;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,wCAAwC;YACxC,MAAM,WAAW,GAAG,IAAA,iBAAU,EAC5B,KAAK,CAAC,MAAM,EACZ,wBAAiB,CAAC,aAAa,CAChC,CAAC;YACF,IACE,CAAC;gBACC,sBAAc,CAAC,uBAAuB;gBACtC,sBAAc,CAAC,mBAAmB;gBAClC,sBAAc,CAAC,kBAAkB;aAClC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAC5B,CAAC;gBACD,+BAA+B;gBAC/B,oCAAoC;gBACpC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,sCAAsC;YACtC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,sCAAsC;gBACtC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;WAKG;QACH,SAAS,eAAe,CAAC,IAAyB;YAChD,MAAM,UAAU,GAAG,IAAA,iBAAU,EAC3B,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EACtC,wBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CACzD,CAAC;YAEF,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,SAAS,MAAM,CACb,IAAoE;YAEpE,MAAM,UAAU,GACd,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC1C,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACf,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAEhB,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAChE,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QAED,SAAS,gCAAgC,CAAC,YAAqB;YAC7D,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAErE,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACrC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBAE7C,OAAO,OAAO;qBACX,cAAc,CAAC,UAAU,CAAC;qBAC1B,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS,2BAA2B,CAClC,YAG+B;YAE/B,aAAa;YACb,+EAA+E;YAC/E,8EAA8E;YAC9E,sFAAsF;YACtF,iEAAiE;YACjE,yEAAyE;YAEzE,MAAM,cAAc,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAExE,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;gBACxB,MAAM,UAAU,GAAG,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAEpE,OAAO,OAAO;qBACX,cAAc,CAAC,UAAU,CAAC;qBAC1B,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAED,IAAI,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpC,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBAE/D,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,OAAO;yBACX,cAAc,CAAC,YAAY,CAAC;yBAC5B,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC,CAAC"}
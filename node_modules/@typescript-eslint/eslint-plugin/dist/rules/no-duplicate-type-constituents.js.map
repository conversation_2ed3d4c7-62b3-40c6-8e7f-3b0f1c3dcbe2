{"version": 3, "file": "no-duplicate-type-constituents.js", "sourceRoot": "", "sources": ["../../src/rules/no-duplicate-type-constituents.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAMiB;AAajB,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAE1D,MAAM,aAAa,GAAG,CAAC,UAAmB,EAAE,YAAqB,EAAW,EAAE;IAC5E,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IACE,UAAU;QACV,YAAY;QACZ,OAAO,UAAU,KAAK,QAAQ;QAC9B,OAAO,YAAY,KAAK,QAAQ,EAChC,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7D,IAAI,UAAU,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,CAAC,UAAU,CAAC,IAAI,CACrB,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CACjE,CAAC;QACJ,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CACnD,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAC/B,CAAC;QACF,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CACvD,GAAG,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAC/B,CAAC;QACF,IAAI,cAAc,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IACE,cAAc,CAAC,IAAI,CACjB,aAAa,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,CAC7D,EACD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IACE,cAAc,CAAC,IAAI,CACjB,aAAa,CAAC,EAAE,CACd,CAAC,aAAa,CACZ,UAAU,CAAC,aAAwC,CAAC,EACpD,YAAY,CAAC,aAA0C,CAAC,CACzD,CACJ,EACD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,SAAS,EAAE,4DAA4D;YACvE,WAAW,EACT,6DAA6D;SAChE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,mBAAmB,EAAE;wBACnB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,sCAAsC;qBACpD;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,+BAA+B;qBAC7C;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,mBAAmB,EAAE,KAAK;YAC1B,YAAY,EAAE,KAAK;SACpB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,mBAAmB,EAAE,YAAY,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAE/B,SAAS,MAAM,CACb,SAAqB,EACrB,eAAkC,EAClC,IAA8B;YAE9B,MAAM,2BAA2B,GAAG,CAClC,KAAyB,EACzB,EAAU,EACkB,EAAE,CAC9B,UAAU,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,eAAe,EAAE;gBAC/C,MAAM,EAAE,KAAK,CAAC,EAAE,CACd,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;oBAChC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;oBACjD,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aACpD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAEZ,MAAM,8BAA8B,GAAG,2BAA2B,CAChE,QAAQ,EACR,CAAC,CAAC,CACH,CAAC;YACF,IAAI,6BAAyD,CAAC;YAC9D,IAAI,mBAAmB,CAAC;YACxB,IAAI,kBAAkB,CAAC;YACvB,IAAI,8BAA8B,EAAE,CAAC;gBACnC,mBAAmB,GAAG,UAAU,CAAC,gBAAgB,CAC/C,8BAA8B,EAC9B,eAAe,CAChB,CAAC;gBACF,kBAAkB,GAAG,UAAU,CAAC,cAAc,CAAC,eAAe,EAAE;oBAC9D,KAAK,EAAE,mBAAmB,CAAC,MAAM;iBAClC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,6BAA6B,GAAG,IAAA,iBAAU,EACxC,2BAA2B,CAAC,OAAO,EAAE,CAAC,CAAC,EACvC,wBAAiB,CAAC,YAAY,CAC5B,6BAA6B,EAC7B,4BAA4B,CAC7B,CACF,CAAC;gBACF,kBAAkB,GAAG,UAAU,CAAC,gBAAgB,CAC9C,eAAe,EACf,6BAA6B,CAC9B,CAAC;gBACF,mBAAmB,GAAG,UAAU,CAAC,eAAe,CAAC,eAAe,EAAE;oBAChE,KAAK,EAAE,kBAAkB,CAAC,MAAM;iBACjC,CAAC,CAAC;YACL,CAAC;YACD,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,EAAE;oBACH,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK;oBAChC,GAAG,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,GAAG,CAAC,GAAG;iBAC5D;gBACD,IAAI,EAAE,eAAe;gBACrB,SAAS;gBACT,IAAI;gBACJ,GAAG,EAAE,KAAK,CAAC,EAAE,CACX;oBACE,8BAA8B;oBAC9B,GAAG,mBAAmB;oBACtB,eAAe;oBACf,GAAG,kBAAkB;oBACrB,6BAA6B;iBAC9B,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACzD,CAAC,CAAC;QACL,CAAC;QAED,SAAS,yBAAyB,CAChC,mBAAwC,EACxC,eAAkC,EAClC,kBAAuC,EACvC,aAA2C,EAC3C,eAA+D;YAE/D,MAAM,IAAI,GAAG,cAAc,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC/D,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,OAAO;YACT,CAAC;YACD,MAAM,kBAAkB,GACtB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;gBACnE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE1B,IAAI,kBAAkB,EAAE,CAAC;gBACvB,MAAM,CAAC,WAAW,EAAE,eAAe,EAAE;oBACnC,IAAI,EAAE,mBAAmB;oBACzB,QAAQ,EAAE,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC;iBACjD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,eAAe,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YACzC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YACzC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAEzC,IACE,CAAC,mBAAmB,KAAK,OAAO;gBAC9B,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC;gBACtD,CAAC,mBAAmB,KAAK,cAAc;oBACrC,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,EAC7D,CAAC;gBACD,KAAK,MAAM,WAAW,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;oBAChD,yBAAyB,CACvB,mBAAmB,EACnB,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,eAAe,CAChB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,cAAc,CACrB,IAAwD,EACxD,eAGS;YAET,MAAM,aAAa,GAAG,IAAI,GAAG,EAA2B,CAAC;YACzD,MAAM,kBAAkB,GAAwB,EAAE,CAAC;YAEnD,MAAM,mBAAmB,GACvB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gBAC7C,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,OAAO,CAAC;YAEd,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC9B,yBAAyB,CACvB,mBAAmB,EACnB,IAAI,EACJ,kBAAkB,EAClB,aAAa,EACb,eAAe,CAChB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,GAAG,CAAC,CAAC,mBAAmB,IAAI;gBAC1B,kBAAkB,CAAC,IAAI;oBACrB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;wBAC3D,OAAO;oBACT,CAAC;oBACD,cAAc,CAAC,IAAI,CAAC,CAAC;gBACvB,CAAC;aACF,CAAC;YACF,GAAG,CAAC,CAAC,YAAY,IAAI;gBACnB,WAAW,EAAE,CAAC,IAAI,EAAQ,EAAE;oBAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;wBACpD,OAAO;oBACT,CAAC;oBACD,cAAc,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,EAAE;wBAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC;wBACxC,IAAI,mBAAmB,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;4BACjE,MAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,CAAC;4BACnD,IACE,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gCAClD,eAAe,CAAC,QAAQ,EACxB,CAAC;gCACD,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC;gCAC7C,IACE,IAAA,+BAAwB,EAAC,aAAa,CAAC;oCACvC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;oCAC9C,OAAO,CAAC,aAAa,CACnB,mBAAmB,EACnB,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,EACD,CAAC;oCACD,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;gCACzC,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}
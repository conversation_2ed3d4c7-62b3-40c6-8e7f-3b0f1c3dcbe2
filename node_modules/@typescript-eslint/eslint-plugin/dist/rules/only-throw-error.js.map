{"version": 3, "file": "only-throw-error.js", "sourceRoot": "", "sources": ["../../src/rules/only-throw-error.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,+CAAiC;AAIjC,kCAQiB;AAYjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,eAAe,EAAE,kBAAkB;YACnC,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,MAAM,EAAE,wCAAwC;YAChD,KAAK,EAAE,yBAAyB;SACjC;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,GAAG,kCAA2B;wBAC9B,WAAW,EAAE,qCAAqC;qBACnD;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,yDAAyD;qBAC5D;oBACD,oBAAoB,EAAE;wBACpB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,6DAA6D;qBAChE;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;YACT,gBAAgB,EAAE,IAAI;YACtB,oBAAoB,EAAE,IAAI;SAC3B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,SAAS,kBAAkB,CAAC,IAAmB;YAC7C,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAC5C,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE9C,IAAI,IAAA,+BAAwB,EAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACxC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC7C,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAA,oBAAa,EAAC,IAAI,CAAC,EAAE,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,oBAAoB,IAAI,IAAA,wBAAiB,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,IAAI,IAAA,kBAAW,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}
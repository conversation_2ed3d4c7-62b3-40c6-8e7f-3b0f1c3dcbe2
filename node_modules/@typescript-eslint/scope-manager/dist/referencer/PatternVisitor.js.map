{"version": 3, "file": "PatternVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/PatternVisitor.ts"], "names": [], "mappings": ";;;AAEA,oDAA0D;AAI1D,+CAA4C;AAY5C,MAAa,cAAe,SAAQ,yBAAW;IACpC,YAAY,GAGf,EAAE,CAAC;IACA,SAAS,CAAyB;IAClC,aAAa,GAA2B,EAAE,CAAC;IAC3C,YAAY,CAAgB;IAErB,cAAc,GAAoB,EAAE,CAAC;IAErD,YACE,OAA8B,EAC9B,WAA0B,EAC1B,QAAgC;QAEhC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAEM,MAAM,CAAC,SAAS,CACrB,IAAmB;QAQnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAE3B,OAAO,CACL,QAAQ,KAAK,sBAAc,CAAC,UAAU;YACtC,QAAQ,KAAK,sBAAc,CAAC,aAAa;YACzC,QAAQ,KAAK,sBAAc,CAAC,YAAY;YACxC,QAAQ,KAAK,sBAAc,CAAC,aAAa;YACzC,QAAQ,KAAK,sBAAc,CAAC,WAAW;YACvC,QAAQ,KAAK,sBAAc,CAAC,iBAAiB,CAC9C,CAAC;IACJ,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAES,YAAY,CAAC,OAA8B;QACnD,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAES,oBAAoB,CAAC,IAAmC;QAChE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IAES,iBAAiB,CAAC,OAAmC;QAC7D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IAES,cAAc,CAAC,IAA6B;QACpD,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAES,SAAS;QACjB,sDAAsD;IACxD,CAAC;IAES,UAAU,CAAC,OAA4B;QAC/C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACtB,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,IAAI,EAAE,eAAe,EAAE,QAAQ,KAAK,OAAO;YAC3C,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC,YAAY;SACxC,CAAC,CAAC;IACL,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,gDAAgD;QAChD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAES,QAAQ,CAAC,QAA2B;QAC5C,gDAAgD;QAChD,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACzC,CAAC;QAED,mDAAmD;QACnD,mHAAmH;QACnH,kEAAkE;QAClE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAES,WAAW,CAAC,OAA6B;QACjD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;IAC3B,CAAC;IAES,aAAa,CAAC,IAA4B;QAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAES,gBAAgB;QACxB,+BAA+B;IACjC,CAAC;CACF;AA3HD,wCA2HC"}